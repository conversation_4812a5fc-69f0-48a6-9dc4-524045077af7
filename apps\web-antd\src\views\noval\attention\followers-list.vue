<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { Avatar, List, Empty } from 'ant-design-vue';
import { UserOutlined } from '@ant-design/icons-vue';
import { attentionList } from '#/api/noval/attention';
import { findUserInfo } from '#/api/system/user';
import { formatDateTime } from '@vben/utils';
import type { AttentionVO } from '#/api/noval/attention/model';

interface Props {
  currentUserId?: string | number;
}

interface FollowerWithUser extends AttentionVO {
  userName?: string;
  userAvatar?: string;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  viewUserPosts: [userId: string | number, userName: string];
}>();

const followers = ref<FollowerWithUser[]>([]);
const loading = ref(false);

// 默认头像
const defaultAvatars = [
  'https://unpkg.com/@vbenjs/static-source@0.1.7/source/avatar-v1.webp',
  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiMzYjgyZjYiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMTRDOC42ODYyOSAxNCA2IDE2LjY4NjMgNiAyMEg2VjIwSDZIMThIMThWMjBDMTggMTYuNjg2MyAxNS4zMTM3IDE0IDEyIDE0WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cjwvc3ZnPgo=',
];

// 获取有效的头像URL
function getValidAvatarUrl(userAvatar?: string): string {
  if (userAvatar && userAvatar.trim() && userAvatar !== 'null' && userAvatar !== 'undefined') {
    return userAvatar.trim();
  }
  return defaultAvatars[0];
}

// 获取用户信息
async function getUserInfo(userId: string | number) {
  try {
    const userInfo = await findUserInfo(userId);
    return {
      userName: userInfo.user?.nickName || userInfo.user?.userName || '未知用户',
      userAvatar: getValidAvatarUrl(userInfo.user?.avatar),
    };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      userName: '未知用户',
      userAvatar: getValidAvatarUrl(),
    };
  }
}

// 获取粉丝列表
async function loadFollowers() {
  if (!props.currentUserId) return;

  loading.value = true;
  try {
    // 查询关注当前用户的人（即粉丝）
    const result = await attentionList({
      pageNum: 1,
      pageSize: 100,
      otherId: props.currentUserId, // 查询关注当前用户的记录
    });

    if (result.rows && result.rows.length > 0) {
      // 为每个粉丝记录获取用户信息
      const enhancedRows = await Promise.all(
        result.rows.map(async (row: AttentionVO) => {
          try {
            // 根据id（关注者的ID）获取用户信息
            const userInfo = await getUserInfo(row.id);
            return {
              ...row,
              userName: userInfo?.userName || '未知用户',
              userAvatar: userInfo?.userAvatar || getValidAvatarUrl(),
            };
          } catch (error) {
            console.error('获取用户信息失败:', error);
            return {
              ...row,
              userName: '未知用户',
              userAvatar: getValidAvatarUrl(),
            };
          }
        }),
      );
      followers.value = enhancedRows;
    } else {
      followers.value = [];
    }
  } catch (error) {
    console.error('获取粉丝列表失败:', error);
    followers.value = [];
  } finally {
    loading.value = false;
  }
}

// 点击用户头像或名称
function handleUserClick(userId: string | number, userName: string) {
  emit('viewUserPosts', userId, userName);
}

onMounted(() => {
  loadFollowers();
});
</script>

<template>
  <div class="followers-list">
    <List
      :data-source="followers"
      :loading="loading"
      item-layout="horizontal"
      size="small"
    >
      <template #renderItem="{ item: follower }">
        <List.Item class="follower-item">
          <List.Item.Meta>
            <template #avatar>
              <Avatar
                :src="follower.userAvatar"
                :size="40"
                class="cursor-pointer transition-opacity hover:opacity-80"
                @click="handleUserClick(follower.id, follower.userName)"
                @error="() => {
                  console.log('头像加载失败:', follower.userAvatar);
                }"
              >
                <template #icon>
                  <UserOutlined />
                </template>
              </Avatar>
            </template>
            <template #title>
              <span
                class="cursor-pointer text-blue-600 hover:text-blue-800 hover:underline"
                @click="handleUserClick(follower.id, follower.userName)"
              >
                {{ follower.userName }}
              </span>
            </template>
            <template #description>
              <span class="text-gray-500 text-sm">
                关注时间：{{ formatDateTime(follower.createTime) }}
              </span>
            </template>
          </List.Item.Meta>
        </List.Item>
      </template>

      <template #empty>
        <Empty
          description="暂无粉丝"
          :image="Empty.PRESENTED_IMAGE_SIMPLE"
        />
      </template>
    </List>
  </div>
</template>

<style scoped>
.followers-list {
  max-height: 600px;
  overflow-y: auto;
}

.follower-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.follower-item:last-child {
  border-bottom: none;
}
</style>
