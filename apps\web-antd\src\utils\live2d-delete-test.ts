/**
 * Live2D删除功能测试工具
 * 用于测试和调试Live2D删除功能
 */

import { live2dList2, live2dRemove, live2dRemoveByNames } from '#/api/noval/live2d';
import { deleteLive2DByName, extractLive2DUrls } from './file-delete-helper';

/**
 * 测试Live2D删除功能
 * @param modelName 要测试删除的模型名称
 */
export async function testLive2DDelete(modelName: string) {
  console.log('🧪 开始测试Live2D删除功能:', modelName);
  
  try {
    // 1. 查询同名记录
    console.log('📋 步骤1: 查询同名记录...');
    const records = await live2dList2({ name: modelName, type: undefined });
    console.log(`📊 找到 "${modelName}" 的记录数:`, records.length);
    
    if (records.length === 0) {
      console.log('⚠️ 没有找到记录，测试结束');
      return;
    }
    
    // 2. 提取文件URLs
    console.log('📁 步骤2: 提取文件URLs...');
    const allFileUrls: string[] = [];
    records.forEach(record => {
      const urls = extractLive2DUrls(record);
      allFileUrls.push(...urls);
      console.log(`📄 记录 ${record.id} 的文件:`, urls);
    });
    console.log(`📁 总文件数: ${allFileUrls.length}`);
    
    // 3. 测试批量删除API
    console.log('🗑️ 步骤3: 测试批量删除API...');
    try {
      await live2dRemoveByNames([modelName]);
      console.log('✅ 批量删除API测试成功');
    } catch (error) {
      console.log('❌ 批量删除API测试失败:', error);
      
      // 4. 如果批量删除失败，测试逐个删除
      console.log('🔄 步骤4: 测试逐个删除...');
      for (const record of records) {
        if (record.id) {
          try {
            await live2dRemove(record.id);
            console.log(`✅ 成功删除记录 ${record.id}`);
          } catch (deleteError) {
            console.log(`❌ 删除记录 ${record.id} 失败:`, deleteError);
          }
        }
      }
    }
    
    console.log('🎉 Live2D删除功能测试完成');
    
  } catch (error) {
    console.error('❌ Live2D删除功能测试失败:', error);
  }
}

/**
 * 测试删除辅助函数
 * @param modelName 要测试删除的模型名称
 */
export async function testDeleteHelper(modelName: string) {
  console.log('🧪 开始测试删除辅助函数:', modelName);
  
  try {
    const queryByName = (name: string) => live2dList2({ name, type: undefined });
    const removeByName = (name: string) => live2dRemoveByNames([name]);
    
    const result = await deleteLive2DByName(modelName, queryByName, removeByName);
    console.log('✅ 删除辅助函数测试成功:', result);
    
  } catch (error) {
    console.error('❌ 删除辅助函数测试失败:', error);
  }
}

/**
 * 在浏览器控制台中使用的测试函数
 * 使用方法: 
 * 1. 在浏览器控制台中输入: window.testLive2DDelete('模型名称')
 * 2. 或者: window.testDeleteHelper('模型名称')
 */
if (typeof window !== 'undefined') {
  (window as any).testLive2DDelete = testLive2DDelete;
  (window as any).testDeleteHelper = testDeleteHelper;
  console.log('🔧 Live2D删除测试工具已加载到window对象');
  console.log('使用方法: window.testLive2DDelete("模型名称")');
}
