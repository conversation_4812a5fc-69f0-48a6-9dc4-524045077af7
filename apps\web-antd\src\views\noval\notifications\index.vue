<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';
import { formatDateTime } from '@vben/utils';

import { Avatar, Card, List, Badge, Tag, Empty, Tabs, message } from 'ant-design-vue';
import {
  BellOutlined,
  UserOutlined,
  MessageOutlined,
  NotificationOutlined,
  EyeOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';

import { newsList, newsRemove } from '#/api/noval/news';
import { commentInfo } from '#/api/noval/comment';
import { findUserInfo } from '#/api/system/user';
import type { NewsVO } from '#/api/noval/news/model';
import type { CommentVO } from '#/api/noval/comment/model';
import { getDictOptions } from '#/utils/dict';

const { TabPane } = Tabs;

interface NotificationWithDetails extends NewsVO {
  senderName?: string;
  senderAvatar?: string;
  commentDetails?: CommentVO;
  isRead?: boolean;
}

const userStore = useUserStore();
const router = useRouter();
const currentUser = computed(() => userStore.userInfo);

// 通知数据
const notifications = ref<NotificationWithDetails[]>([]);
const loading = ref(false);
const activeTab = ref('all');

// 字典数据
const newsTypeOptions = ref<any[]>([]);

// 默认头像
const defaultAvatar = 'https://unpkg.com/@vbenjs/static-source@0.1.7/source/avatar-v1.webp';

// 获取字典选项
onMounted(() => {
  newsTypeOptions.value = getDictOptions('new_type');
  loadNotifications();
});

// 获取通知类型标签
function getTypeTag(type: string | number) {
  const typeStr = String(type);
  const option = newsTypeOptions.value.find(opt => opt.value === typeStr);

  const configs = {
    '0': { color: 'blue', text: '管理员信息', icon: NotificationOutlined },
    '1': { color: 'green', text: '论坛信息', icon: MessageOutlined },
    '2': { color: 'red', text: '点赞消息', icon: UserOutlined },
    '3': { color: 'orange', text: '私聊信息', icon: UserOutlined },
    '4': { color: 'purple', text: '公告', icon: BellOutlined },
    '5': { color: 'cyan', text: '通知', icon: BellOutlined },
  };

  const config = configs[typeStr] || { color: 'default', text: option?.label || '未知', icon: BellOutlined };

  return {
    color: config.color,
    text: config.text,
    icon: config.icon,
  };
}

// 获取用户信息
async function getUserInfo(userId: string | number) {
  try {
    const userInfo = await findUserInfo(userId);
    return {
      name: userInfo.user?.nickName || userInfo.user?.userName || '未知用户',
      avatar: userInfo.user?.avatar || defaultAvatar,
    };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      name: '未知用户',
      avatar: defaultAvatar,
    };
  }
}

// 获取评论详情
async function getCommentDetails(commentId: string | number) {
  try {
    return await commentInfo(commentId);
  } catch (error) {
    console.error('获取评论详情失败:', error);
    return null;
  }
}

// 加载通知列表
async function loadNotifications() {
  if (!currentUser.value?.userId) return;

  loading.value = true;
  try {
    const response = await newsList({
      pageNum: 1,
      pageSize: 100,
      otherId: currentUser.value.userId, // 查询发给当前用户的消息
    });

    if (response.rows) {
      const enhancedNotifications = await Promise.all(
        response.rows.map(async (notification: NewsVO) => {
          const enhanced: NotificationWithDetails = {
            ...notification,
            isRead: notification.isread === 1, // 使用实际的isread字段
          };

          // 获取发送者信息
          if (notification.id) {
            const senderInfo = await getUserInfo(notification.id);
            enhanced.senderName = senderInfo.name;
            enhanced.senderAvatar = senderInfo.avatar;
          }

          // 如果是论坛信息，获取评论详情
          if (notification.type === 1 || notification.type === '1') {
            const commentDetails = await getCommentDetails(notification.id);
            if (commentDetails) {
              enhanced.commentDetails = commentDetails;
            }
          }

          return enhanced;
        })
      );

      notifications.value = enhancedNotifications;
    }
  } catch (error) {
    console.error('加载通知失败:', error);
    message.error('加载通知失败');
  } finally {
    loading.value = false;
  }
}

// 筛选通知
const filteredNotifications = computed(() => {
  if (activeTab.value === 'all') {
    return notifications.value;
  }
  return notifications.value.filter(n => String(n.type) === activeTab.value);
});

// 未读通知数量
const unreadCount = computed(() => {
  return notifications.value.filter(n => !n.isRead).length;
});

// 删除通知
async function handleDelete(notification: NotificationWithDetails) {
  try {
    await newsRemove(notification.id);
    notifications.value = notifications.value.filter(n => n.id !== notification.id);
    message.success('删除成功');
  } catch (error) {
    console.error('删除通知失败:', error);
    message.error('删除失败');
  }
}

// 标记为已读
function markAsRead(notification: NotificationWithDetails) {
  notification.isRead = true;
}

// 查看通知详情
function viewNotificationDetail(notification: NotificationWithDetails) {
  markAsRead(notification);

  // 根据通知类型跳转到不同页面
  if (notification.type === 1 || notification.type === '1') {
    // 论坛信息，跳转到论坛界面
    router.push('/CommentForYh/comment-section');
  } else if (notification.type === 3 || notification.type === '3') {
    // 私聊信息，跳转到私聊页面
    router.push({
      path: '/noval/news',
      query: {
        userId: notification.id,
      },
    });
  }
  // 其他类型暂时不跳转
}

// 获取通知图标
function getNotificationIcon(notification: NotificationWithDetails) {
  const typeTag = getTypeTag(notification.type);
  return typeTag.icon;
}

// 获取P3R风格的类型颜色
function getP3RTypeColor(type: string | number) {
  const typeStr = String(type);
  const colors = {
    '0': 'bg-blue-500/20 border-blue-400/50 text-blue-300',          // 管理员 - 蓝色
    '1': 'bg-green-500/20 border-green-400/50 text-green-300',       // 论坛 - 绿色
    '2': 'bg-red-500/20 border-red-400/50 text-red-300',             // 点赞 - 红色
    '3': 'bg-orange-500/20 border-orange-400/50 text-orange-300',    // 私聊 - 橙色
    '4': 'bg-purple-500/20 border-purple-400/50 text-purple-300',    // 公告 - 紫色
    '5': 'bg-cyan-500/20 border-cyan-400/50 text-cyan-300',          // 通知 - 青色
  };
  return colors[typeStr] || 'bg-slate-500/20 border-slate-400/50 text-slate-300';
}

// 保持原有函数兼容性
function getP4TypeStyle(type: string | number) {
  return getP3RTypeColor(type);
}
</script>

<template>
  <Page :auto-content-height="true">
    <div class="p3r-notifications-container p-6 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 min-h-screen relative overflow-hidden">
      <!-- P3R风格背景效果 -->
      <div class="absolute inset-0">
        <!-- 网格背景 -->
        <div class="absolute inset-0 p3r-grid-bg opacity-20"></div>
        <!-- 光效 -->
        <div class="absolute top-0 left-1/4 w-96 h-96 bg-blue-500 rounded-full filter blur-3xl opacity-10 animate-pulse"></div>
        <div class="absolute bottom-0 right-1/4 w-96 h-96 bg-cyan-500 rounded-full filter blur-3xl opacity-10 animate-pulse animation-delay-2000"></div>
        <!-- 数据流效果 -->
        <div class="absolute inset-0 p3r-data-stream"></div>
      </div>

      <!-- 页面标题 -->
      <div class="relative z-10 mb-8">
        <div class="flex items-center gap-6 mb-6">
          <div class="relative">
            <!-- P3R风格图标容器 -->
            <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-lg flex items-center justify-center shadow-2xl border border-cyan-400/50 backdrop-blur-sm">
              <BellOutlined class="text-white text-3xl" />
              <!-- 发光效果 -->
              <div class="absolute inset-0 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-lg blur opacity-50 -z-10"></div>
            </div>
            <!-- 扫描线效果 -->
            <div class="absolute inset-0 bg-gradient-to-b from-transparent via-cyan-400/30 to-transparent h-1 animate-scan"></div>
          </div>
          <div class="flex-1">
            <h1 class="text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 mb-2 p3r-title p5r-title-animation">
              消息通知中心
            </h1>
            <p class="text-cyan-300 text-lg font-light tracking-wider p5r-subtitle-animation">
              // 系统消息界面
            </p>
          </div>
          <div class="relative">
            <Badge :count="unreadCount" class="ml-auto">
              <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-pink-500 rounded-lg flex items-center justify-center shadow-lg border border-red-400/50 backdrop-blur-sm">
                <BellOutlined class="text-white text-2xl" />
              </div>
            </Badge>
            <div v-if="unreadCount > 0" class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full animate-ping"></div>
          </div>
        </div>
      </div>

      <!-- 通知列表 -->
      <div class="relative z-10">
        <!-- P3R风格容器 -->
        <div class="bg-slate-900/80 backdrop-blur-xl border border-cyan-400/30 rounded-2xl shadow-2xl overflow-hidden">
          <!-- 顶部装饰条 -->
          <div class="h-1 bg-gradient-to-r from-blue-500 via-cyan-400 to-purple-500"></div>

          <div class="relative">
            <Tabs v-model:activeKey="activeTab" class="p3r-notification-tabs">
              <TabPane key="all" tab="全部消息">
                <template #tab>
                  <div class="p3r-tab-button p5r-tab-animation">
                    <BellOutlined class="text-lg" />
                    <span class="font-semibold">全部消息</span>
                    <div class="p3r-badge">
                      {{ notifications.length }}
                    </div>
                  </div>
                </template>
              </TabPane>
              <TabPane key="0" tab="管理员信息">
                <template #tab>
                  <div class="p3r-tab-button p5r-tab-animation">
                    <NotificationOutlined class="text-lg" />
                    <span class="font-semibold">系统消息</span>
                  </div>
                </template>
              </TabPane>
              <TabPane key="1" tab="论坛信息">
                <template #tab>
                  <div class="p3r-tab-button p5r-tab-animation">
                    <MessageOutlined class="text-lg" />
                    <span class="font-semibold">论坛消息</span>
                  </div>
                </template>
              </TabPane>
              <TabPane key="2" tab="点赞消息">
                <template #tab>
                  <div class="p3r-tab-button p5r-tab-animation">
                    <UserOutlined class="text-lg" />
                    <span class="font-semibold">点赞消息</span>
                  </div>
                </template>
              </TabPane>
              <TabPane key="3" tab="私聊信息">
                <template #tab>
                  <div class="p3r-tab-button p5r-tab-animation">
                    <UserOutlined class="text-lg" />
                    <span class="font-semibold">私聊消息</span>
                  </div>
                </template>
              </TabPane>
              <TabPane key="4" tab="公告">
                <template #tab>
                  <div class="p3r-tab-button p5r-tab-animation">
                    <BellOutlined class="text-lg" />
                    <span class="font-semibold">公告</span>
                  </div>
                </template>
              </TabPane>
              <TabPane key="5" tab="通知">
                <template #tab>
                  <div class="p3r-tab-button p5r-tab-animation">
                    <BellOutlined class="text-lg" />
                    <span class="font-semibold">通知</span>
                  </div>
                </template>
              </TabPane>
            </Tabs>

            <div class="notification-content p-6 bg-slate-900/50">
              <!-- 加载状态 -->
              <div v-if="loading" class="flex items-center justify-center py-16">
                <div class="relative">
                  <div class="w-16 h-16 border-4 border-slate-600 border-t-cyan-400 rounded-full animate-spin"></div>
                  <div class="absolute inset-0 w-16 h-16 border-4 border-slate-700 border-t-blue-400 rounded-full animate-spin animation-reverse"></div>
                </div>
                <span class="ml-4 text-cyan-300 text-xl font-light">正在加载数据...</span>
              </div>

              <!-- 空状态 -->
              <div v-else-if="filteredNotifications.length === 0" class="text-center py-16">
                <div class="mb-6">
                  <div class="w-24 h-24 bg-slate-800 border border-cyan-400/50 rounded-lg flex items-center justify-center mx-auto backdrop-blur-sm">
                    <BellOutlined class="text-4xl text-cyan-400" />
                  </div>
                </div>
                <h3 class="text-2xl font-bold text-cyan-300 mb-2">暂无数据</h3>
                <p class="text-slate-400 font-light">// 暂无通知消息</p>
              </div>

              <!-- 通知列表 -->
              <div v-else class="space-y-4">
                <div
                  v-for="notification in filteredNotifications"
                  :key="notification.id"
                  class="p3r-notification-card cursor-pointer group p5r-card-animation"
                  :class="{ 'unread': !notification.isRead }"
                  @click="viewNotificationDetail(notification)"
                >
                  <!-- P3R风格卡片 -->
                  <div class="relative bg-slate-800/90 backdrop-blur-sm border border-cyan-400/30 rounded-xl shadow-2xl transition-all duration-500 group-hover:border-cyan-400/60 group-hover:shadow-cyan-400/20">
                    <!-- 发光边框效果 -->
                    <div class="absolute -inset-0.5 bg-gradient-to-r from-blue-500 via-cyan-400 to-purple-500 rounded-xl blur opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>

                    <!-- 未读指示器 -->
                    <div v-if="!notification.isRead" class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 border-2 border-slate-800 rounded-full flex items-center justify-center animate-pulse">
                      <div class="w-2 h-2 bg-white rounded-full"></div>
                    </div>

                    <!-- 类型标签 -->
                    <div class="absolute top-4 right-4">
                      <div class="px-3 py-1 rounded-full text-xs font-semibold border backdrop-blur-sm"
                           :class="getP3RTypeColor(notification.type)">
                        {{ getTypeTag(notification.type).text }}
                      </div>
                    </div>

                    <div class="relative p-6">
                      <div class="flex items-start gap-4">
                        <!-- 头像区域 -->
                        <div class="relative flex-shrink-0">
                          <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-xl flex items-center justify-center border border-cyan-400/50 backdrop-blur-sm">
                            <Avatar
                              :src="notification.senderAvatar"
                              :size="48"
                              class="border border-cyan-400/30"
                            >
                              <template #icon>
                                <UserOutlined />
                              </template>
                            </Avatar>
                          </div>
                          <!-- 类型图标 -->
                          <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-slate-700 border border-cyan-400/50 rounded-lg flex items-center justify-center backdrop-blur-sm">
                            <component :is="getNotificationIcon(notification)" class="text-cyan-400 text-sm" />
                          </div>
                          <!-- 扫描线效果 -->
                          <div class="absolute inset-0 bg-gradient-to-b from-transparent via-cyan-400/20 to-transparent h-0.5 animate-scan-slow"></div>
                        </div>

                        <!-- 内容区域 -->
                        <div class="flex-1 min-w-0">
                          <!-- 发送者名称 -->
                          <h3 class="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-400 mb-2">
                            {{ notification.senderName }}
                          </h3>

                          <!-- 消息内容 -->
                          <div class="notification-content text-slate-300 leading-relaxed mb-4 font-light">
                            {{ notification.news }}
                          </div>

                          <!-- 底部信息 -->
                          <div class="flex items-center justify-between">
                            <div class="notification-time text-sm text-slate-500">
                              {{ formatDateTime(notification.createTime) }}
                            </div>
                            <div class="flex items-center gap-2">
                              <div class="p-2 bg-blue-500/20 border border-blue-400/30 rounded-lg hover:bg-blue-500/30 transition-colors backdrop-blur-sm">
                                <EyeOutlined class="text-blue-400" />
                              </div>
                              <div
                                class="p-2 bg-red-500/20 border border-red-400/30 rounded-lg hover:bg-red-500/30 transition-colors backdrop-blur-sm"
                                @click.stop="handleDelete(notification)"
                              >
                                <DeleteOutlined class="text-red-400" />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Page>
</template>

<style scoped>
/* P5R风格字体 */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&family=Exo+2:ital,wght@0,400;0,600;0,700;1,400;1,600&display=swap');

/* P3R风格动画 */
@keyframes scan {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100vh); }
}

@keyframes scan-slow {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(200%); }
}

@keyframes data-flow {
  0% { transform: translateX(-100%); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateX(100%); opacity: 0; }
}

@keyframes glow-pulse {
  0%, 100% { box-shadow: 0 0 20px rgba(34, 211, 238, 0.3); }
  50% { box-shadow: 0 0 40px rgba(34, 211, 238, 0.6); }
}

@keyframes float-up {
  0%, 100% { transform: translateY(0) skew(-1deg); }
  50% { transform: translateY(-10px) skew(-1deg); }
}

@keyframes p5r-slide-in {
  0% {
    transform: translateX(-100%) skew(-10deg);
    opacity: 0;
  }
  100% {
    transform: translateX(0) skew(-10deg);
    opacity: 1;
  }
}

@keyframes p5r-text-reveal {
  0% {
    transform: skew(-5deg) translateY(20px);
    opacity: 0;
  }
  100% {
    transform: skew(-5deg) translateY(0);
    opacity: 1;
  }
}

/* 容器样式 */
.p3r-notifications-container {
  font-family: 'Rajdhani', 'Orbitron', sans-serif;
  background-attachment: fixed;
}

/* P5R风格标题样式 */
.p3r-title {
  font-family: 'Orbitron', sans-serif;
  text-shadow: 0 0 20px rgba(34, 211, 238, 0.5);
  letter-spacing: 0.1em;
  font-weight: 900;
  transform: skew(-5deg);
  text-transform: uppercase;
  position: relative;
}

.p3r-title::before {
  content: '';
  position: absolute;
  top: 0;
  left: -10px;
  right: -10px;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(34, 211, 238, 0.1) 50%, transparent 70%);
  transform: skew(5deg);
  z-index: -1;
}

/* P5R动画类 */
.p5r-title-animation {
  animation: p5r-text-reveal 1s ease-out;
}

.p5r-subtitle-animation {
  animation: p5r-text-reveal 1s ease-out 0.3s both;
}

.p5r-tab-animation {
  animation: p5r-slide-in 0.6s ease-out;
}

.p5r-card-animation {
  animation: p5r-slide-in 0.8s ease-out;
}

/* P3R背景效果 */
.p3r-grid-bg {
  background-image:
    linear-gradient(rgba(34, 211, 238, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(34, 211, 238, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

.p3r-data-stream {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(34, 211, 238, 0.1),
    transparent
  );
  animation: data-flow 3s linear infinite;
}

/* P5R风格标签按钮 */
.p3r-tab-button {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 24px;
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(34, 211, 238, 0.3);
  border-radius: 0;
  color: rgba(34, 211, 238, 0.9);
  font-family: 'Rajdhani', sans-serif;
  font-weight: 700;
  text-transform: uppercase;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  letter-spacing: 0.1em;
  transform: skew(-10deg);
  position: relative;
  clip-path: polygon(10px 0%, 100% 0%, calc(100% - 10px) 100%, 0% 100%);
}

.p3r-tab-button:hover {
  background: rgba(34, 211, 238, 0.1);
  border-color: rgba(34, 211, 238, 0.6);
  color: #22d3ee;
  box-shadow: 0 0 20px rgba(34, 211, 238, 0.3);
  transform: skew(-10deg) scale(1.05);
}

.p3r-tab-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(34, 211, 238, 0.1) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.p3r-tab-button:hover::before {
  opacity: 1;
}

/* P5R风格徽章 */
.p3r-badge {
  background: linear-gradient(135deg, #3b82f6, #22d3ee);
  color: white;
  border: 1px solid rgba(34, 211, 238, 0.5);
  border-radius: 0;
  padding: 2px 8px;
  font-size: 11px;
  font-weight: 700;
  font-family: 'Orbitron', monospace;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transform: skew(-5deg);
  clip-path: polygon(3px 0%, 100% 0%, calc(100% - 3px) 100%, 0% 100%);
}

/* P5R风格标签页 */
:deep(.p3r-notification-tabs .ant-tabs-nav) {
  background: rgba(15, 23, 42, 0.8);
  margin: 0;
  padding: 20px;
  border-bottom: 3px solid rgba(34, 211, 238, 0.3);
  backdrop-filter: blur(10px);
  position: relative;
}

:deep(.p3r-notification-tabs .ant-tabs-nav::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(34, 211, 238, 0.05) 50%, transparent 70%);
  pointer-events: none;
}

:deep(.p3r-notification-tabs .ant-tabs-tab) {
  border: none;
  background: transparent;
  margin-right: 16px;
  padding: 0;
  position: relative;
}

:deep(.p3r-notification-tabs .ant-tabs-tab-btn) {
  padding: 0;
}

:deep(.p3r-notification-tabs .ant-tabs-tab:hover .p3r-tab-button) {
  background: rgba(34, 211, 238, 0.1);
  border-color: rgba(34, 211, 238, 0.6);
  color: #22d3ee;
  box-shadow: 0 0 20px rgba(34, 211, 238, 0.3);
  transform: skew(-10deg) scale(1.05);
}

:deep(.p3r-notification-tabs .ant-tabs-tab-active .p3r-tab-button) {
  background: rgba(34, 211, 238, 0.2);
  border-color: #22d3ee;
  color: #22d3ee;
  box-shadow: 0 0 30px rgba(34, 211, 238, 0.5);
  transform: skew(-10deg) scale(1.1);
}

:deep(.p3r-notification-tabs .ant-tabs-ink-bar) {
  background: linear-gradient(90deg, #3b82f6, #22d3ee, #8b5cf6);
  height: 4px;
  border-radius: 0;
  box-shadow: 0 0 15px rgba(34, 211, 238, 0.8);
  clip-path: polygon(5px 0%, 100% 0%, calc(100% - 5px) 100%, 0% 100%);
}

:deep(.p3r-notification-tabs .ant-tabs-content-holder) {
  background: transparent;
}

/* P5R风格通知卡片 */
.p3r-notification-card {
  position: relative;
  font-family: 'Rajdhani', sans-serif;
}

.p3r-notification-card:hover {
  animation: float-up 0.6s ease-in-out;
  transform: skew(-1deg);
}

.p3r-notification-card.unread {
  animation: glow-pulse 2s ease-in-out infinite;
}

/* P5R风格卡片内容 */
.p3r-notification-card h3 {
  font-family: 'Exo 2', sans-serif;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transform: skew(-3deg);
}

.p3r-notification-card .notification-content {
  font-family: 'Rajdhani', sans-serif;
  font-weight: 500;
  letter-spacing: 0.02em;
}

.p3r-notification-card .notification-time {
  font-family: 'Orbitron', monospace;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* 动画延迟 */
.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-reverse {
  animation-direction: reverse;
}

/* P5R风格徽章样式 */
:deep(.ant-badge-count) {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border: 1px solid rgba(239, 68, 68, 0.5);
  color: white;
  font-weight: 700;
  font-family: 'Orbitron', monospace;
  box-shadow: 0 0 15px rgba(239, 68, 68, 0.5);
  border-radius: 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  clip-path: polygon(2px 0%, 100% 0%, calc(100% - 2px) 100%, 0% 100%);
}

/* P5R风格头像样式 */
:deep(.ant-avatar) {
  border: 2px solid rgba(34, 211, 238, 0.3);
  box-shadow: 0 0 15px rgba(34, 211, 238, 0.2);
  border-radius: 0;
  clip-path: polygon(5px 0%, 100% 0%, calc(100% - 5px) 100%, 0% 100%);
}

/* 滚动条样式 */
.p3r-notifications-container ::-webkit-scrollbar {
  width: 8px;
}

.p3r-notifications-container ::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5);
  border-radius: 4px;
}

.p3r-notifications-container ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #22d3ee);
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(34, 211, 238, 0.3);
}

.p3r-notifications-container ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #22d3ee, #8b5cf6);
  box-shadow: 0 0 20px rgba(34, 211, 238, 0.6);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .p3r-notifications-container {
    padding: 12px;
  }

  .p3r-tab-button {
    padding: 8px 16px;
    font-size: 12px;
    gap: 8px;
  }

  .p3r-title {
    font-size: 2.5rem;
  }

  .p3r-notification-card {
    margin-bottom: 12px;
  }
}

/* 扫描动画 */
@keyframes scan {
  0% { transform: translateY(-100%); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateY(100%); opacity: 0; }
}

@keyframes scan-slow {
  0% { transform: translateY(-100%); opacity: 0; }
  50% { opacity: 0.3; }
  100% { transform: translateY(200%); opacity: 0; }
}

.animate-scan {
  animation: scan 2s linear infinite;
}

.animate-scan-slow {
  animation: scan-slow 4s linear infinite;
}

/* 全局科技感效果 */
.p3r-notifications-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(34, 211, 238, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.02) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}
</style>
