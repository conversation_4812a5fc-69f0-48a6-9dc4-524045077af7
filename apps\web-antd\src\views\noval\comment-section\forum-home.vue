<script setup lang="ts">
import type { CommentVO, CommentForm } from '#/api/noval/comment/model';

import { computed, onMounted, ref } from 'vue';

import { useUserStore } from '@vben/stores';
import { formatDateTime } from '@vben/utils';

import {
  Avatar,
  Button,
  Card,
  Input,
  List,
  message,
  Space,
  Spin,
  Badge,
  Divider,
  Modal,
  Tag
} from 'ant-design-vue';
import {
  UserOutlined,
  PlusOutlined,
  EyeOutlined,
  MessageOutlined,
  LikeOutlined,
  DislikeOutlined,
  EditOutlined,
  FireOutlined,
  ClockCircleOutlined,
  HeartOutlined,
  HeartFilled
} from '@ant-design/icons-vue';

import { commentAdd, commentList, commentIncreaseLook, commentInfo } from '#/api/noval/comment';
import { findUserInfo } from '#/api/system/user';
import { todayList, todayUpdate, todayAdd } from '#/api/noval/today';
import { goodbadInfo, goodbadAdd, goodbadRemove, goodbadBatchStats } from '#/api/noval/goodbad';
import type { GoodbadForm } from '#/api/noval/goodbad/model';
import { attentionList, attentionAdd, attentionRemove } from '#/api/noval/attention';
import type { AttentionForm } from '#/api/noval/attention/model';
import { useNotifyStore } from '#/store/notify';
import { ImageUpload } from '#/components/upload';
import UserProfileModal from '#/components/User/UserProfileModal.vue';

interface PostWithUser extends CommentVO {
  userName?: string;
  userAvatar?: string;
  createTime?: string;
  replyCount?: number;
  look?: number; // 曝光数，只有楼主帖子才有
  likeCount?: number; // 点赞数（从GoodbadVO统计）
  dislikeCount?: number; // 点踩数（从GoodbadVO统计）
  lastReplyTime?: string;
  lastReplyUser?: string;
  isHot?: boolean;
  isTop?: boolean;
  isFollowed?: boolean; // 是否已关注该用户
}

interface Props {
  // 贴吧名称
  forumName?: string;
  // 是否只读模式
  readonly?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  forumName: '小说讨论吧',
  readonly: false,
});

const emit = defineEmits<{
  viewPost: [postId: string | number];
}>();

const userStore = useUserStore();
const notifyStore = useNotifyStore();
const loading = ref(false);
const submitting = ref(false);
const posts = ref<PostWithUser[]>([]);
const totalPosts = ref(0);
const showCreateModal = ref(false);
const newPostTitle = ref('');
const newPostContent = ref('');
const newPostImages = ref<string[]>([]);
const todayNewComments = ref(0);
const showUserProfileModal = ref(false);
const selectedUserId = ref<string | number>('');
const selectedUserName = ref('');

// 用户信息缓存
const userCache = ref<Map<string | number, { userName: string; userAvatar: string }>>(new Map());

// 用户点赞/踩状态缓存 - key: commentId, value: { isLiked: boolean, isDisliked: boolean }
const userVoteCache = ref<Map<string | number, { isLiked: boolean; isDisliked: boolean }>>(new Map());

// 用户关注状态缓存 - key: userId, value: boolean
const userFollowCache = ref<Map<string | number, boolean>>(new Map());

// 当前用户信息
const currentUser = computed(() => userStore.userInfo);

// 默认头像 - 使用多个备选方案
const defaultAvatars = [
  'https://unpkg.com/@vbenjs/static-source@0.1.7/source/avatar-v1.webp',
  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiMzYjgyZjYiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMTRDOC42ODYyOSAxNCA2IDE2LjY4NjMgNiAyMEg2VjIwSDZIMThIMThWMjBDMTggMTYuNjg2MyAxNS4zMTM3IDE0IDEyIDE0WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cjwvc3ZnPgo=',
];

// 获取有效的头像URL
function getValidAvatarUrl(userAvatar?: string): string {
  // 如果用户头像存在且有效，返回用户头像
  if (userAvatar && userAvatar.trim() && userAvatar !== 'null' && userAvatar !== 'undefined') {
    return userAvatar.trim();
  }
  // 否则返回第一个默认头像
  return defaultAvatars[0];
}

// 获取用户信息
async function getUserInfo(userId: string | number) {
  if (userCache.value.has(userId)) {
    return userCache.value.get(userId);
  }

  try {
    const userInfo = await findUserInfo(userId);
    const userData = {
      userName: userInfo.user?.nickName || userInfo.user?.userName || '未知用户',
      userAvatar: getValidAvatarUrl(userInfo.user?.avatar),
    };
    userCache.value.set(userId, userData);
    return userData;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      userName: '未知用户',
      userAvatar: getValidAvatarUrl(),
    };
  }
}

// 获取用户对评论的点赞/踩状态
async function getUserVoteStatus(commentId: string | number) {
  if (!currentUser.value?.userId) {
    return { isLiked: false, isDisliked: false };
  }

  if (userVoteCache.value.has(commentId)) {
    return userVoteCache.value.get(commentId)!;
  }

  try {
    // 查询点赞记录
    const likeRecord = await goodbadInfo(commentId, currentUser.value.userId);
    const status = {
      isLiked: likeRecord && likeRecord.goodBad === 1,
      isDisliked: likeRecord && likeRecord.goodBad === 0,
    };
    userVoteCache.value.set(commentId, status);
    return status;
  } catch (error) {
    // 如果没有记录，返回默认状态
    const status = { isLiked: false, isDisliked: false };
    userVoteCache.value.set(commentId, status);
    return status;
  }
}

// 获取用户关注状态
async function getUserFollowStatus(userId: string | number) {
  if (!currentUser.value?.userId || userId === currentUser.value.userId) {
    return false; // 不能关注自己
  }

  if (userFollowCache.value.has(userId)) {
    return userFollowCache.value.get(userId)!;
  }

  try {
    // 查询关注记录
    const response = await attentionList({
      id: currentUser.value.userId,
      ortherId: userId,
      pageNum: 1,
      pageSize: 1
    });
    const isFollowed = response.rows && response.rows.length > 0;
    userFollowCache.value.set(userId, isFollowed);
    return isFollowed;
  } catch (error) {
    // 如果没有记录，返回默认状态
    userFollowCache.value.set(userId, false);
    return false;
  }
}

// 加载帖子列表
async function loadPosts() {
  loading.value = true;
  try {
    const response = await commentList({
      pageNum: 1,
      pageSize: 50,
      commerntType: 0, // 只获取帖子（楼主发的内容，commerntType=0）
    });

    const postData = response.rows || [];

    // 获取所有用户信息
    const userIds = [...new Set(postData.map(item => item.myId))];
    await Promise.all(userIds.map(userId => getUserInfo(userId)));

    // 批量获取所有帖子的点赞统计数据
    const commentIds = postData.map(post => post.commerntId);
    const statsMap = await goodbadBatchStats(commentIds);

    // 为每个帖子计算回复数和最后回复信息
    const postsWithUser: PostWithUser[] = await Promise.all(
      postData.map(async (post) => {
        // 获取该帖子的所有评论（commerntType=0, responseId=post.commerntId）
        let replyCount = 0;
        let lastReplyTime = '';
        let lastReplyUser = '';

        try {
          const repliesResponse = await commentList({
            pageNum: 1,
            pageSize: 1000,
            commerntType: 1, // 获取评论（commerntType=1）
            responseId: post.commerntId, // 直接通过API筛选属于当前帖子的回复
          });

          // 获取属于当前帖子的回复
          const postReplies = repliesResponse.rows || [];
          replyCount = postReplies.length;

          if (postReplies.length > 0) {
            // 找到最新的回复
            const latestReply = postReplies.sort((a, b) =>
              new Date(b.createTime || 0).getTime() - new Date(a.createTime || 0).getTime()
            )[0];

            lastReplyTime = latestReply.createTime || '';
            const replyUserInfo = userCache.value.get(latestReply.myId);
            lastReplyUser = replyUserInfo?.userName || '未知用户';
          }
        } catch (error) {
          console.error('获取回复数据失败:', error);
        }

        const userInfo = userCache.value.get(post.myId);
        const stats = statsMap.get(post.commerntId) || { likeCount: 0, dislikeCount: 0 };

        // 获取关注状态
        const isFollowed = await getUserFollowStatus(post.myId);

        const postWithUser = {
          ...post,
          userName: userInfo?.userName || '未知用户',
          userAvatar: userInfo?.userAvatar || getValidAvatarUrl(),
          createTime: post.createTime || new Date().toISOString(),
          replyCount,
          look: post.look || 0, // 使用数据库中的曝光数
          likeCount: stats.likeCount, // 从GoodbadVO统计的点赞数
          dislikeCount: stats.dislikeCount, // 从GoodbadVO统计的点踩数
          lastReplyTime,
          lastReplyUser,
          isHot: replyCount > 10, // 根据回复数判断是否热门
          isTop: false, // 可以从数据库字段获取
          isFollowed, // 关注状态
        };
        return postWithUser;
      })
    );

    // 排序：置顶在前，然后按时间倒序
    posts.value = postsWithUser.sort((a, b) => {
      if (a.isTop && !b.isTop) return -1;
      if (!a.isTop && b.isTop) return 1;
      return new Date(b.createTime || 0).getTime() - new Date(a.createTime || 0).getTime();
    });

    totalPosts.value = response.total || 0;

    // 预加载所有帖子的点赞/踩状态
    if (currentUser.value?.userId) {
      await Promise.all(
        postsWithUser.map(async (post) => {
          try {
            await getUserVoteStatus(post.commerntId);
          } catch (error) {
            // 忽略单个帖子的状态加载错误
            console.warn(`加载帖子 ${post.commerntId} 的点赞状态失败:`, error);
          }
        }),
      );
    }
  } catch (error) {
    console.error('加载帖子失败:', error);
    message.error('加载帖子失败');
  } finally {
    loading.value = false;
  }
}

// 加载今日新增数据
async function loadTodayData() {
  try {
    const todayResponse = await todayList({ pageNum: 1, pageSize: 1 });
    const todayData = todayResponse.rows?.[0];
    todayNewComments.value = todayData?.newCommernt || 0;
  } catch (error) {
    console.error('加载今日数据失败:', error);
    todayNewComments.value = 0;
  }
}

// 更新今日新增评论数
async function updateTodayNewComment() {
  try {
    // 获取今日数据
    const todayResponse = await todayList({ pageNum: 1, pageSize: 1 });
    const todayData = todayResponse.rows?.[0];

    if (todayData) {
      // 如果存在今日数据，更新newCommernt字段
      await todayUpdate({
        ...todayData,
        newCommernt: (todayData.newCommernt || 0) + 1
      });
      // 更新本地显示
      todayNewComments.value = (todayData.newCommernt || 0) + 1;
    } else {
      // 如果不存在今日数据，创建新的记录
      await todayAdd({
        newCommernt: 1
      });
      // 更新本地显示
      todayNewComments.value = 1;
    }
  } catch (error) {
    console.error('更新今日新增评论数失败:', error);
    // 这里不抛出错误，避免影响主要的发帖流程
  }
}

// 发表新帖子
async function submitPost() {
  if (!newPostTitle.value.trim()) {
    message.warning('请输入帖子标题');
    return;
  }

  if (!newPostContent.value.trim()) {
    message.warning('请输入帖子内容');
    return;
  }

  if (!currentUser.value?.userId) {
    message.error('请先登录');
    return;
  }

  submitting.value = true;
  try {
    const postData: CommentForm = {
      // 不传递 commerntId，让后端自动生成
      content: `${newPostTitle.value.trim()}\n\n${newPostContent.value.trim()}`, // 标题和内容合并
      myId: currentUser.value.userId,
      commerntType: 0, // 0代表发帖主人（楼主）
      isResponse: 0, // 0代表不是回复
      responseId: 0, // 主帖没有回复对象
      // 添加图片字段
      icon1: newPostImages.value[0] || '',
      icon2: newPostImages.value[1] || '',
      icon3: newPostImages.value[2] || '',
      icon4: newPostImages.value[3] || '',
      icon5: newPostImages.value[4] || '',
    };

    await commentAdd(postData);

    // 更新今日新增评论数
    await updateTodayNewComment();

    // 更新全局评论数量
    await notifyStore.fetchCommentCount();

    message.success('帖子发表成功');
    newPostTitle.value = '';
    newPostContent.value = '';
    newPostImages.value = [];
    showCreateModal.value = false;
    await loadPosts(); // 重新加载帖子
  } catch (error) {
    console.error('发表帖子失败:', error);
    message.error('发表帖子失败');
  } finally {
    submitting.value = false;
  }
}

// 查看帖子详情
function viewPost(post: PostWithUser) {
  // 直接跳转到详情页，浏览量在详情页加载时增加
  emit('viewPost', post.commerntId);
}

// 点赞
async function handleLike(post: PostWithUser, event: Event) {
  event.stopPropagation(); // 阻止事件冒泡，避免触发查看帖子

  if (!currentUser.value?.userId) {
    message.error('请先登录');
    return;
  }

  try {
    const commentId = post.commerntId;
    const userId = currentUser.value.userId;

    // 获取当前状态
    const currentStatus = await getUserVoteStatus(commentId);

    if (currentStatus.isLiked) {
      // 已经点赞，取消点赞
      await goodbadRemove(commentId, userId);
      userVoteCache.value.set(commentId, { isLiked: false, isDisliked: false });
      post.likeCount = Math.max(0, (post.likeCount || 0) - 1);
    } else if (currentStatus.isDisliked) {
      // 已经点踩，先取消踩再点赞
      await goodbadRemove(commentId, userId);
      const likeData: GoodbadForm = {
        commerntId: commentId,
        whoId: userId,
        goodBad: 1, // 1代表赞
      };
      await goodbadAdd(likeData);
      userVoteCache.value.set(commentId, { isLiked: true, isDisliked: false });
      post.likeCount = (post.likeCount || 0) + 1;
      post.dislikeCount = Math.max(0, (post.dislikeCount || 0) - 1);
    } else {
      // 没有记录，新增点赞
      const likeData: GoodbadForm = {
        commerntId: commentId,
        whoId: userId,
        goodBad: 1, // 1代表赞
      };
      await goodbadAdd(likeData);
      userVoteCache.value.set(commentId, { isLiked: true, isDisliked: false });
      post.likeCount = (post.likeCount || 0) + 1;
    }
  } catch (error) {
    console.error('点赞操作失败:', error);
    message.error('操作失败，请重试');
  }
}

// 点踩
async function handleDislike(post: PostWithUser, event: Event) {
  event.stopPropagation(); // 阻止事件冒泡，避免触发查看帖子

  if (!currentUser.value?.userId) {
    message.error('请先登录');
    return;
  }

  try {
    const commentId = post.commerntId;
    const userId = currentUser.value.userId;

    // 获取当前状态
    const currentStatus = await getUserVoteStatus(commentId);

    if (currentStatus.isDisliked) {
      // 已经点踩，取消踩
      await goodbadRemove(commentId, userId);
      userVoteCache.value.set(commentId, { isLiked: false, isDisliked: false });
      post.dislikeCount = Math.max(0, (post.dislikeCount || 0) - 1);
    } else if (currentStatus.isLiked) {
      // 已经点赞，先取消赞再点踩
      await goodbadRemove(commentId, userId);
      const dislikeData: GoodbadForm = {
        commerntId: commentId,
        whoId: userId,
        goodBad: 0, // 0代表踩
      };
      await goodbadAdd(dislikeData);
      userVoteCache.value.set(commentId, { isLiked: false, isDisliked: true });
      post.dislikeCount = (post.dislikeCount || 0) + 1;
      post.likeCount = Math.max(0, (post.likeCount || 0) - 1);
    } else {
      // 没有记录，新增踩
      const dislikeData: GoodbadForm = {
        commerntId: commentId,
        whoId: userId,
        goodBad: 0, // 0代表踩
      };
      await goodbadAdd(dislikeData);
      userVoteCache.value.set(commentId, { isLiked: false, isDisliked: true });
      post.dislikeCount = (post.dislikeCount || 0) + 1;
    }
  } catch (error) {
    console.error('点踩操作失败:', error);
    message.error('操作失败，请重试');
  }
}

// 查看我的帖子
function viewMyPosts() {
  if (!currentUser.value?.userId) {
    message.error('请先登录');
    return;
  }
  // 这里可以筛选当前用户的帖子
  const myPosts = posts.value.filter(post => post.myId === currentUser.value?.userId);
  message.info(`您共发表了 ${myPosts.length} 个帖子`);
}

// 关注/取消关注用户
async function handleFollow(post: PostWithUser, event: Event) {
  event.stopPropagation(); // 阻止事件冒泡

  if (!currentUser.value?.userId) {
    message.error('请先登录');
    return;
  }

  if (post.myId === currentUser.value.userId) {
    message.warning('不能关注自己');
    return;
  }

  try {
    const userId = post.myId;
    const currentUserId = currentUser.value.userId;
    const isCurrentlyFollowed = await getUserFollowStatus(userId);

    if (isCurrentlyFollowed) {
      // 取消关注
      const response = await attentionList({
        id: currentUserId,
        ortherId: userId,
        pageNum: 1,
        pageSize: 1
      });

      if (response.rows && response.rows.length > 0) {
        const attentionRecord = response.rows[0];
        await attentionRemove(attentionRecord.id);
        userFollowCache.value.set(userId, false);
        post.isFollowed = false;
        message.success('已取消关注');
      }
    } else {
      // 添加关注
      const followData: AttentionForm = {
        id: currentUserId,
        ortherId: userId,
      };
      await attentionAdd(followData);
      userFollowCache.value.set(userId, true);
      post.isFollowed = true;
      message.success('关注成功');
    }
  } catch (error) {
    console.error('关注操作失败:', error);
    message.error('操作失败，请重试');
  }
}

// 查看用户信息
function viewUserPosts(userId: string | number, userName: string, event: Event) {
  event.stopPropagation(); // 阻止事件冒泡

  selectedUserId.value = userId;
  selectedUserName.value = userName;
  showUserProfileModal.value = true;
}

// 开始私聊
function startPrivateChat(userId: string | number, userName: string) {
  // 这里可以添加私聊逻辑，比如跳转到私聊页面或打开私聊对话框
  console.log('开始与用户私聊:', userId, userName);
  // 可以添加路由跳转或其他私聊逻辑
}



// 组件挂载时加载帖子和今日数据
onMounted(() => {
  loadPosts();
  loadTodayData();
});
</script>

<template>
  <div class="forum-home">
    <!-- 贴吧头部 -->
    <Card class="forum-header mb-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
          <div class="forum-avatar">
            <Avatar :size="64" class="forum-logo">
              <template #icon>
                <FireOutlined />
              </template>
            </Avatar>
          </div>
          <div>
            <h1 class="forum-title">{{ props.forumName }}</h1>
            <div class="forum-stats">
              <span class="stat-item">帖子 {{ totalPosts }}</span>
              <span class="stat-item">今日新增 {{ todayNewComments }}</span>
            </div>
          </div>
        </div>
        <div class="forum-actions">
          <Space>
            <Button @click="viewMyPosts">
              <template #icon>
                <EditOutlined />
              </template>
              我的帖子
            </Button>
            <Button
              v-if="!readonly"
              type="primary"
              @click="showCreateModal = true"
            >
              <template #icon>
                <PlusOutlined />
              </template>
              发表帖子
            </Button>
          </Space>
        </div>
      </div>
    </Card>

    <!-- 帖子列表 -->
    <Card class="posts-container">
      <template #title>
        <div class="flex items-center justify-between">
          <span>帖子列表</span>
          <Button type="text" @click="loadPosts">
            刷新
          </Button>
        </div>
      </template>

      <Spin :spinning="loading">
        <List
          v-if="posts.length > 0"
          :data-source="posts"
          item-layout="vertical"
          class="post-list"
        >
          <template #renderItem="{ item: post }">
            <List.Item class="post-item" @click="viewPost(post)">
              <div class="post-content">
                <!-- 帖子头部 -->
                <div class="post-header">
                  <div class="flex items-start gap-3">
                    <div class="user-avatar-section">
                      <Avatar
                        :src="getValidAvatarUrl(post.userAvatar)"
                        :size="40"
                        class="clickable-avatar"
                        @click="viewUserPosts(post.myId, post.userName || '未知用户', $event)"
                        @error="() => {
                          console.log('头像加载失败:', post.userAvatar);
                        }"
                      >
                        <template #icon>
                          <UserOutlined />
                        </template>
                      </Avatar>
                    </div>
                    <div class="flex-1">
                      <div class="post-title-row">
                        <div class="flex items-center gap-2">
                          <Tag v-if="post.isTop" color="red">置顶</Tag>
                          <Tag v-if="post.isHot" color="orange">热门</Tag>
                          <h3 class="post-title">{{ getPostTitle(post.content || '') }}</h3>
                        </div>
                      </div>
                      <div class="post-meta">
                        <span class="author">{{ post.userName }}</span>
                        <!-- 关注按钮 -->
                        <Button
                          v-if="currentUser?.userId && post.myId !== currentUser.userId"
                          :type="post.isFollowed ? 'default' : 'primary'"
                          size="small"
                          class="follow-btn-inline"
                          @click="handleFollow(post, $event)"
                        >
                          <template #icon>
                            <HeartFilled v-if="post.isFollowed" />
                            <HeartOutlined v-else />
                          </template>
                          {{ post.isFollowed ? '已关注' : '关注' }}
                        </Button>
                        <span class="time">{{ formatDateTime(post.createTime) }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 帖子预览内容 -->
                <div class="post-preview">
                  {{ getPostPreview(post.content || '') }}
                </div>

                <!-- 帖子统计 -->
                <div class="post-stats">
                  <div class="stats-left">
                    <span class="stat-item">
                      <EyeOutlined />
                      曝光 {{ post.look || 0 }}
                    </span>
                    <span class="stat-item">
                      <MessageOutlined />
                      回复 {{ post.replyCount || 0 }}
                    </span>
                    <span
                      class="stat-item clickable-stat"
                      :class="{ 'liked': userVoteCache.get(post.commerntId)?.isLiked }"
                      @click="handleLike(post, $event)"
                    >
                      <LikeOutlined />
                      {{ post.likeCount || 0 }}
                    </span>
                    <span
                      class="stat-item clickable-stat"
                      :class="{ 'disliked': userVoteCache.get(post.commerntId)?.isDisliked }"
                      @click="handleDislike(post, $event)"
                    >
                      <DislikeOutlined />
                      {{ post.dislikeCount || 0 }}
                    </span>
                  </div>
                </div>
              </div>
            </List.Item>
          </template>
        </List>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <div class="text-center py-12">
            <div class="text-gray-400 mb-4">
              <MessageOutlined class="text-6xl" />
            </div>
            <div class="text-lg text-gray-500 mb-2">还没有帖子</div>
            <div class="text-sm text-gray-400">快来发表第一个帖子吧！</div>
          </div>
        </div>
      </Spin>
    </Card>

    <!-- 发表帖子弹窗 -->
    <Modal
      v-model:open="showCreateModal"
      title="发表新帖子"
      width="600px"
      :confirm-loading="submitting"
      @ok="submitPost"
      @cancel="showCreateModal = false"
    >
      <div class="create-post-form">
        <div class="mb-4">
          <label class="form-label">帖子标题</label>
          <Input
            v-model:value="newPostTitle"
            placeholder="请输入帖子标题..."
            :maxlength="50"
            show-count
          />
        </div>
        <div class="mb-4">
          <label class="form-label">帖子内容</label>
          <Input.TextArea
            v-model:value="newPostContent"
            :rows="8"
            placeholder="请输入帖子内容..."
            :maxlength="2000"
            show-count
          />
        </div>
        <div class="mb-4">
          <label class="form-label">上传图片（最多5张）</label>
          <ImageUpload
            v-model:value="newPostImages"
            :max-number="5"
            :max-size="10"
            :multiple="true"
            accept="jpg,jpeg,png,gif,webp"
            help-text="支持jpg、png、gif等格式，单张图片不超过10MB"
          />
        </div>
      </div>
    </Modal>

    <!-- 用户信息弹窗 -->
    <UserProfileModal
      v-model:visible="showUserProfileModal"
      :user-id="selectedUserId"
      :user-name="selectedUserName"
      @start-chat="startPrivateChat"
      @close="showUserProfileModal = false"
    />
  </div>
</template>

<style scoped>
/* P5R风格样式 - 蓝色主题 */
.forum-home {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, #0a0a0a 0%, #0a0a1a 25%, #050a2a 50%, #0a0a1a 75%, #0a0a0a 100%);
  min-height: 100vh;
  font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
  position: relative;
  overflow-x: hidden;
}

.forum-home::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
    linear-gradient(45deg, transparent 48%, rgba(59, 130, 246, 0.03) 49%, rgba(59, 130, 246, 0.03) 51%, transparent 52%),
    linear-gradient(-45deg, transparent 48%, rgba(0, 0, 0, 0.1) 49%, rgba(0, 0, 0, 0.1) 51%, transparent 52%);
  background-size: 100% 100%, 100% 100%, 60px 60px, 60px 60px;
  pointer-events: none;
  z-index: 0;
  animation: p5rBgShift 20s infinite linear;
}

@keyframes p5rBgShift {
  0% { background-position: 0% 0%, 0% 0%, 0px 0px, 0px 0px; }
  100% { background-position: 0% 0%, 0% 0%, 60px 60px, -60px -60px; }
}

.forum-header {
  background: linear-gradient(135deg, #1a0a0a 0%, #050a2a 25%, #3b82f6 50%, #050a2a 75%, #1a0a0a 100%);
  border: 2px solid #3b82f6;
  border-radius: 0;
  box-shadow:
    0 0 30px rgba(59, 130, 246, 0.4),
    0 8px 32px rgba(0, 0, 0, 0.6),
    inset 0 1px 0 rgba(59, 130, 246, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.5);
  position: relative;
  z-index: 1;
  overflow: hidden;
  backdrop-filter: blur(10px);
  clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 20px, 100% 100%, 20px 100%, 0 calc(100% - 20px));
}

.forum-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
  animation: p5rShine 6s infinite;
}

.forum-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 48%, rgba(59, 130, 246, 0.1) 49%, rgba(59, 130, 246, 0.1) 51%, transparent 52%),
    linear-gradient(-45deg, transparent 48%, rgba(0, 0, 0, 0.2) 49%, rgba(0, 0, 0, 0.2) 51%, transparent 52%);
  background-size: 20px 20px, 20px 20px;
  animation: p5rPattern 10s infinite linear;
  pointer-events: none;
}

@keyframes p5rShine {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes p5rPattern {
  0% { background-position: 0px 0px, 0px 0px; }
  100% { background-position: 20px 20px, -20px -20px; }
}

.forum-header :deep(.ant-card-body) {
  padding: 32px;
  background: transparent;
  position: relative;
  z-index: 2;
}

.forum-logo {
  background: linear-gradient(45deg, #3b82f6, #1e40af);
  border: 2px solid #3b82f6;
  box-shadow:
    0 0 25px rgba(59, 130, 246, 0.6),
    0 4px 15px rgba(30, 64, 175, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  animation: p5rPulse 4s infinite;
  position: relative;
  overflow: hidden;
}

.forum-logo::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: p5rLogoShine 3s infinite;
}

@keyframes p5rPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 25px rgba(59, 130, 246, 0.6), 0 4px 15px rgba(30, 64, 175, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 35px rgba(59, 130, 246, 0.8), 0 6px 20px rgba(30, 64, 175, 0.6);
  }
}

@keyframes p5rLogoShine {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.forum-title {
  font-size: 32px;
  font-weight: 900;
  margin: 0;
  color: #ffffff;
  text-shadow:
    0 0 10px rgba(59, 130, 246, 0.8),
    0 2px 4px rgba(0, 0, 0, 0.8),
    0 4px 8px rgba(59, 130, 246, 0.4);
  letter-spacing: 2px;
  text-transform: uppercase;
  position: relative;
}

.forum-title::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, transparent);
  animation: p5rTitleLine 2s infinite alternate;
}

@keyframes p5rTitleLine {
  0% { width: 0%; }
  100% { width: 100%; }
}

.forum-stats {
  margin-top: 16px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.stat-item {
  margin-right: 20px;
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(30, 64, 175, 0.3));
  border: 1px solid #3b82f6;
  border-radius: 0;
  backdrop-filter: blur(10px);
  clip-path: polygon(0 0, calc(100% - 8px) 0, 100% 8px, 100% 100%, 8px 100%, 0 calc(100% - 8px));
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.4), rgba(30, 64, 175, 0.5));
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.forum-actions :deep(.ant-btn) {
  border-radius: 0;
  font-weight: 700;
  letter-spacing: 1px;
  text-transform: uppercase;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  clip-path: polygon(0 0, calc(100% - 8px) 0, 100% 8px, 100% 100%, 8px 100%, 0 calc(100% - 8px));
}

.forum-actions :deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  border: 2px solid #3b82f6;
  color: white;
  box-shadow:
    0 0 20px rgba(59, 130, 246, 0.4),
    0 4px 15px rgba(30, 64, 175, 0.2);
}

.forum-actions :deep(.ant-btn-primary:hover) {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 0 30px rgba(59, 130, 246, 0.6),
    0 8px 25px rgba(30, 64, 175, 0.3);
}

.forum-actions :deep(.ant-btn:not(.ant-btn-primary)) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(30, 64, 175, 0.05));
  border: 1px solid #3b82f6;
  color: #ffffff;
}

.forum-actions :deep(.ant-btn:not(.ant-btn-primary):hover) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(30, 64, 175, 0.2));
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.posts-container {
  background: rgba(255, 255, 255, 0.02);
  border: 2px solid #3b82f6;
  border-radius: 0;
  box-shadow:
    0 0 30px rgba(59, 130, 246, 0.4),
    0 8px 32px rgba(0, 0, 0, 0.6),
    inset 0 1px 0 rgba(59, 130, 246, 0.2);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(20px);
  clip-path: polygon(0 0, calc(100% - 15px) 0, 100% 15px, 100% 100%, 15px 100%, 0 calc(100% - 15px));
}

.posts-container :deep(.ant-card-head) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(30, 64, 175, 0.6));
  border-bottom: 2px solid #3b82f6;
  border-radius: 0;
  backdrop-filter: blur(10px);
}

.posts-container :deep(.ant-card-head-title) {
  color: white;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.posts-container :deep(.ant-card-body) {
  background: transparent;
  padding: 0;
}

.post-list :deep(.ant-list-item) {
  border-bottom: 1px solid #333333;
  padding: 0;
  background: transparent;
}

.post-item {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(59, 130, 246, 0.3);
  margin: 8px;
  padding: 20px;
  border-radius: 0;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  clip-path: polygon(0 0, calc(100% - 10px) 0, 100% 10px, 100% 100%, 10px 100%, 0 calc(100% - 10px));
}

.post-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.08), transparent);
  transition: left 0.6s ease;
}

.post-item:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(30, 64, 175, 0.05));
  border-color: #3b82f6;
  transform: translateY(-4px) scale(1.02);
  box-shadow:
    0 0 25px rgba(59, 130, 246, 0.4),
    0 8px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(59, 130, 246, 0.2);
}

.post-item:hover::before {
  left: 100%;
}

.post-content {
  width: 100%;
  position: relative;
  z-index: 1;
}

.post-title {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 12px 0;
  cursor: pointer;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  line-height: 1.4;
}

.post-title:hover {
  color: #60a5fa;
  text-shadow:
    0 0 8px rgba(96, 165, 250, 0.4),
    0 1px 2px rgba(0, 0, 0, 0.5);
}

.post-meta {
  font-size: 12px;
  color: #888888;
  margin-bottom: 12px;
}

.author {
  font-weight: 600;
  color: #60a5fa;
  margin-right: 12px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.time {
  color: #94a3b8;
}

.post-preview {
  font-size: 14px;
  color: #cbd5e1;
  line-height: 1.6;
  margin: 16px 0;
  padding-left: 0;
}

.post-stats {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 0;
  font-size: 12px;
  color: #888888;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #333333;
}

.stats-left .stat-item {
  margin-right: 16px;
  padding: 6px 12px;
  background: rgba(59, 130, 246, 0.08);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  color: #e2e8f0;
  font-weight: 500;
  backdrop-filter: blur(5px);
}

.stats-left .stat-item :deep(.anticon) {
  margin-right: 6px;
  color: #60a5fa;
}

/* 可点击的统计项样式 */
.clickable-stat {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
}

.clickable-stat:hover {
  background: rgba(59, 130, 246, 0.15) !important;
  border-color: rgba(59, 130, 246, 0.4) !important;
  transform: translateY(-1px) scale(1.05);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 点赞状态样式 */
.clickable-stat.liked {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.3)) !important;
  border-color: rgba(16, 185, 129, 0.4) !important;
  color: #10b981 !important;
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
}

.clickable-stat.liked :deep(.anticon) {
  color: #10b981 !important;
}

.clickable-stat.liked:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.3), rgba(5, 150, 105, 0.4)) !important;
  border-color: rgba(16, 185, 129, 0.6) !important;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

/* 点踩状态样式 */
.clickable-stat.disliked {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 38, 0.3)) !important;
  border-color: rgba(239, 68, 68, 0.4) !important;
  color: #ef4444 !important;
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.3);
}

/* 用户头像样式 */
.clickable-avatar {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid rgba(59, 130, 246, 0.3);
}

.clickable-avatar:hover {
  transform: scale(1.1);
  border-color: #3b82f6;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
}

/* 关注按钮样式 */
.follow-section {
  display: flex;
  align-items: center;
}

.follow-btn {
  border-radius: 0;
  font-size: 12px;
  height: 28px;
  padding: 0 12px;
  font-weight: 600;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  clip-path: polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px));
}

.follow-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

/* 内联关注按钮样式 */
.follow-btn-inline {
  border-radius: 0;
  font-size: 10px;
  height: 20px;
  padding: 0 8px;
  font-weight: 600;
  letter-spacing: 0.3px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  clip-path: polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px));
  margin: 0 8px;
  vertical-align: middle;
}

.follow-btn-inline:hover {
  transform: translateY(-1px) scale(1.05);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* 用户帖子弹窗样式 */
.user-posts-modal {
  max-height: 500px;
  overflow-y: auto;
}

.user-posts-list :deep(.ant-list-item) {
  border-bottom: 1px solid #e5e7eb;
  padding: 12px 0;
}

.user-post-item {
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.user-post-item:hover {
  background: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.user-post-content {
  width: 100%;
}

.user-post-title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.user-post-preview {
  font-size: 14px;
  color: #cbd5e1;
  line-height: 1.5;
  margin-bottom: 8px;
}

.user-post-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #94a3b8;
}

.user-post-time {
  color: #94a3b8;
}

.user-post-stats {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #60a5fa;
}

.user-posts-empty {
  text-align: center;
  padding: 40px 20px;
  color: #94a3b8;
}

.clickable-stat.disliked :deep(.anticon) {
  color: #ef4444 !important;
}

.clickable-stat.disliked:hover {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.3), rgba(220, 38, 38, 0.4)) !important;
  border-color: rgba(239, 68, 68, 0.6) !important;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}



/* 标签样式 */
:deep(.ant-tag) {
  border-radius: 6px;
  font-weight: 600;
  letter-spacing: 0.3px;
  border: 1px solid;
  backdrop-filter: blur(5px);
}

:deep(.ant-tag-red) {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.8), rgba(220, 38, 38, 0.9));
  border-color: rgba(239, 68, 68, 0.4);
  color: white;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
}

:deep(.ant-tag-orange) {
  background: linear-gradient(135deg, rgba(251, 146, 60, 0.8), rgba(249, 115, 22, 0.9));
  border-color: rgba(251, 146, 60, 0.4);
  color: white;
  box-shadow: 0 2px 8px rgba(251, 146, 60, 0.2);
}

/* 模态框样式 */
:deep(.ant-modal) {
  .ant-modal-content {
    background: rgba(15, 20, 25, 0.95);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 16px;
    box-shadow:
      0 20px 60px rgba(0, 0, 0, 0.6),
      0 8px 32px rgba(30, 58, 138, 0.1);
    backdrop-filter: blur(20px);
  }

  .ant-modal-header {
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.8), rgba(30, 64, 175, 0.6));
    border-bottom: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 16px 16px 0 0;
    backdrop-filter: blur(10px);
  }

  .ant-modal-title {
    color: white;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .ant-modal-body {
    background: transparent;
    color: #ffffff;
  }

  .ant-modal-footer {
    background: transparent;
    border-top: 1px solid rgba(59, 130, 246, 0.1);
    border-radius: 0 0 16px 16px;
  }
}

.create-post-form .form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 输入框样式 */
:deep(.ant-input),
:deep(.ant-input-affix-wrapper),
:deep(.ant-select-selector) {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  color: #ffffff;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

:deep(.ant-input:focus),
:deep(.ant-input-affix-wrapper:focus),
:deep(.ant-input-affix-wrapper-focused),
:deep(.ant-select-focused .ant-select-selector) {
  border-color: #3b82f6;
  box-shadow:
    0 0 0 3px rgba(59, 130, 246, 0.1),
    0 0 12px rgba(59, 130, 246, 0.2);
}

:deep(.ant-input::placeholder) {
  color: #94a3b8;
}

.empty-state {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 16px;
  color: #94a3b8;
  text-align: center;
  padding: 48px;
  backdrop-filter: blur(10px);
}

.empty-state :deep(.anticon) {
  color: #60a5fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .forum-home {
    padding: 12px;
  }

  .forum-header :deep(.ant-card-body) {
    padding: 20px;
  }

  .forum-title {
    font-size: 22px;
  }

  .post-item {
    margin: 4px;
    padding: 16px;
  }

  .post-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .stats-left {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
}

/* 头像样式 */
:deep(.ant-avatar) {
  border: 2px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.2);
  position: relative;
  z-index: 10;
  background: rgba(255, 255, 255, 0.1);
}

:deep(.ant-avatar img) {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

:deep(.ant-avatar .ant-avatar-icon) {
  color: #60a5fa;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 20, 25, 0.5);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
}
</style>
