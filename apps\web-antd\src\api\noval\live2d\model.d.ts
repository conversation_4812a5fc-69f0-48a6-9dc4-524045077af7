import type { PageQuery, BaseEntity } from '#/api/common';

export interface Live2dVO {
  /**
   * id
   */
  id: string | number;

  /**
   * Live2d名字
   */
  name: string;

  /**
   * Live2d路径
   */
  icon: string;

  /**
   * Live2d类型
   */
  type: number;

  /**
   * live2d原名
   */
  original_name: string;
}

export interface Live2dForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * Live2d名字
   */
  name?: string;

  /**
   * Live2d路径 (支持单个字符串或字符串数组)
   */
  icon?: string | string[];

  /**
   * Live2d类型
   */
  type?: number;

  /**
   * Live2d模型文件夹（前端上传用）
   */
  modelFolder?: any[];

  /**
   * live2d原名 (支持单个字符串或字符串数组)
   */
  originalName?: string | string[];

}

export interface Live2dQuery extends PageQuery {
  /**
   * id
   */
  id?: string | number;

  /**
   * Live2d名字
   */
  name?: string;

  /**
   * Live2d路径
   */
  icon?: string;

  /**
   * Live2d类型
   */
  type?: number;

  /**
    * 日期范围参数
    */
  params?: any;

  /**
   * live2d原名
   */
  originalName?: string;
}
