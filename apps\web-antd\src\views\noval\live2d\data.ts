import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getDictOptions } from '#/utils/dict';

// 全局类型声明
declare global {
  interface Window {
    live2dFileNameMap: Record<string, string>;
  }
}
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'name',
    label: '名字',
  },
  {
    component: 'Select',
    componentProps: {
      dictType: 'live_type',
      placeholder: '请选择Live2d类型',
    },
    fieldName: 'type',
    label: '类型',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 100,
    align: 'center',
  },
  {
    title: 'Live2D名称',
    field: 'name',
    width: 250,
    showOverflow: 'tooltip',
    align: 'left',
  },

  {
    title: '模型类型',
    field: 'type',
    width: 220,
    align: 'center',
    slots: {
      default: ({ row }) => {
        return renderDict(row.type, 'live_type');
      },
    },
  },
  {
    title: '文件数量',
    field: 'fileCount',
    width: 220,
    align: 'center',
    slots: {
      default: ({ row }) => {
        console.log('📊 表格行数据:', row);
        const count = row.recordCount || row.fileCount || 1;
        return `${count} 个`;
      },
    },
  },

  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 220,
    align: 'center',
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '名字',
    fieldName: 'name',
    component: 'Input',
    rules: 'required',
  },
  {
    label: 'Live2D模型文件',
    fieldName: 'icon',
    component: 'FileUpload',
    /**
     * 注意这里获取为数组 需要自行定义回显/提交
     */
    componentProps: {
      accept: [
        'json',
        'moc3',
        'png',
        'txt',
        '8192',
        'motion3.json',
        'exp3.json',
        'physics3.json',
        'cdi3.json',
        'model3.json',
        'vtube.json',
      ], // 支持Live2D相关文件格式
      maxNumber: 200, // 最大文件数量
      directory: true, // 启用文件夹上传
      maxSize: 50, // 单个文件最大50MB
      multiple: true, // 支持多文件上传
      resultField: 'url', // 返回URL字段
      // 自定义上传成功回调，保存原始文件名
      onUploadSuccess: (file: any, response: any) => {
        if (!window.live2dFileNameMap) {
          window.live2dFileNameMap = {};
        }
        const fileUrl = response.url || response.data?.url || response;

        // 尝试多种方式获取原始文件名
        let originalName = 'unknown';
        if (file.originFileObj && file.originFileObj.name) {
          originalName = file.originFileObj.name;
        } else if (file.name) {
          originalName = file.name;
        } else if (file.webkitRelativePath) {
          // 如果是文件夹上传，获取相对路径中的文件名
          const pathParts = file.webkitRelativePath.split('/');
          originalName = pathParts[pathParts.length - 1];
        }

        if (fileUrl) {
          window.live2dFileNameMap[fileUrl] = originalName;
          console.log('💾 保存文件名映射:', fileUrl, '->', originalName);
        }
      },
      helpText:
        '支持拖拽整个Live2D模型文件夹上传，单个文件最大50MB，最多200个文件',
    },
    rules: 'required',
    // 在编辑模式下隐藏此字段
    dependencies: {
      show: (values) => !values.id, // 只在新增时显示
      triggerFields: ['id'],
    },
  },
  {
    label: '类型',
    fieldName: 'type',
    component: 'Select',
    componentProps: {
      options: getDictOptions('live_type'),
    },
    rules: 'selectRequired',
  },
];
