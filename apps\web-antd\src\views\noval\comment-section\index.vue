<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';

import ForumHome from './forum-home.vue';
import PostDetail from './post-detail-p3r.vue';

interface Props {
  // 贴吧名称
  forumName?: string;
  // 是否只读模式
  readonly?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  forumName: '小说讨论吧',
  readonly: false,
});

const route = useRoute();

// 当前视图状态
const currentView = ref<'home' | 'detail'>('home');
const selectedPostId = ref<string | number>('');

// 查看帖子详情
function viewPostDetail(postId: string | number) {
  selectedPostId.value = postId;
  currentView.value = 'detail';
}

// 返回主页
function backToHome() {
  currentView.value = 'home';
  selectedPostId.value = '';
}

// 初始化页面状态
onMounted(() => {
  // 检查URL参数，如果有postId和view=detail，直接显示帖子详情
  const postId = route.query.postId as string;
  const view = route.query.view as string;

  if (postId && view === 'detail') {
    selectedPostId.value = postId;
    currentView.value = 'detail';
  }
});
</script>

<template>
  <div class="tieba-forum">
    <!-- 贴吧主页 -->
    <ForumHome
      v-if="currentView === 'home'"
      :forum-name="props.forumName"
      :readonly="props.readonly"
      @view-post="viewPostDetail"
    />

    <!-- 帖子详情页 -->
    <PostDetail
      v-else-if="currentView === 'detail'"
      :post-id="selectedPostId"
      :forum-name="props.forumName"
      :readonly="props.readonly"
      @back="backToHome"
    />
  </div>
</template>

<style scoped>
.tieba-forum {
  width: 100%;
  height: 100%;
}
</style>
