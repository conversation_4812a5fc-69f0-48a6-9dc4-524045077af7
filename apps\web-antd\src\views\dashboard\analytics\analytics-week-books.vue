<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import { manageWeekAdd } from '#/api/noval/manage';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

onMounted(async () => {
  try {
    // 获取本周新增书籍数据
    console.log('开始获取本周新增书籍数据...');
    const response = await manageWeekAdd();
    console.log('API响应:', response);

    // 处理可能的数据格式
    let weekBooks = response;
    if (response && typeof response === 'object' && 'data' in response) {
      weekBooks = response.data;
    }
    if (response && typeof response === 'object' && 'rows' in response) {
      weekBooks = response.rows;
    }

    console.log('处理后的数据:', weekBooks);

    // 按日期分组统计
    const dateMap = new Map<string, number>();
    const today = new Date();

    // 初始化本周7天的数据
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      const dateStr = date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
      dateMap.set(dateStr, 0);
    }

    // 统计每天的新增书籍数量
    if (Array.isArray(weekBooks)) {
      weekBooks.forEach(book => {
        if (book.createTime) {
          const bookDate = new Date(book.createTime);
          const dateStr = bookDate.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
          if (dateMap.has(dateStr)) {
            dateMap.set(dateStr, (dateMap.get(dateStr) || 0) + 1);
          }
        }
      });
    } else {
      console.warn('weekBooks不是数组:', weekBooks);
    }

    const dates = Array.from(dateMap.keys());
    const values = Array.from(dateMap.values());

    renderEcharts({
      backgroundColor: 'transparent',
      grid: {
        bottom: '10%',
        containLabel: true,
        left: '5%',
        right: '5%',
        top: '10%',
      },
      series: [
        {
          barMaxWidth: 60,
          data: values,
          type: 'bar',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: '#3b82f6', // P3R蓝色
                },
                {
                  offset: 1,
                  color: '#1e40af', // 深蓝色
                },
              ],
            },
            borderRadius: [4, 4, 0, 0],
            shadowBlur: 10,
            shadowColor: 'rgba(59, 130, 246, 0.3)',
            shadowOffsetY: 2,
          },
          emphasis: {
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#60a5fa',
                  },
                  {
                    offset: 1,
                    color: '#3b82f6',
                  },
                ],
              },
              shadowBlur: 15,
              shadowColor: 'rgba(59, 130, 246, 0.5)',
            },
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(15, 23, 42, 0.9)',
        borderColor: '#3b82f6',
        borderWidth: 1,
        textStyle: {
          color: '#e2e8f0',
          fontFamily: 'Rajdhani, sans-serif',
        },
        formatter: (params: any) => {
          const data = params[0];
          return `
            <div style="font-family: 'Rajdhani', sans-serif; font-weight: 600;">
              <div style="color: #3b82f6; margin-bottom: 4px;">${data.name}</div>
              <div style="color: #e2e8f0;">新增书籍: ${data.value} 本</div>
            </div>
          `;
        },
        axisPointer: {
          type: 'shadow',
          shadowStyle: {
            color: 'rgba(59, 130, 246, 0.1)',
          },
        },
      },
      xAxis: {
        data: dates,
        type: 'category',
        axisLine: {
          lineStyle: {
            color: '#334155',
          },
        },
        axisLabel: {
          color: '#64748b',
          fontFamily: 'Rajdhani, sans-serif',
          fontWeight: 600,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false,
        },
        axisLabel: {
          color: '#64748b',
          fontFamily: 'Rajdhani, sans-serif',
          fontWeight: 600,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: '#334155',
            type: 'dashed',
          },
        },
      },
    });
  } catch (error) {
    console.error('加载本周新增书籍数据失败:', error);

    // 显示默认数据
    renderEcharts({
      backgroundColor: 'transparent',
      grid: {
        bottom: '10%',
        containLabel: true,
        left: '5%',
        right: '5%',
        top: '10%',
      },
      series: [
        {
          barMaxWidth: 60,
          data: [5, 8, 12, 6, 15, 10, 7],
          type: 'bar',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: '#3b82f6',
                },
                {
                  offset: 1,
                  color: '#1e40af',
                },
              ],
            },
            borderRadius: [4, 4, 0, 0],
            shadowBlur: 10,
            shadowColor: 'rgba(59, 130, 246, 0.3)',
            shadowOffsetY: 2,
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(15, 23, 42, 0.9)',
        borderColor: '#3b82f6',
        borderWidth: 1,
        textStyle: {
          color: '#e2e8f0',
          fontFamily: 'Rajdhani, sans-serif',
        },
      },
      xAxis: {
        data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        type: 'category',
        axisLine: {
          lineStyle: {
            color: '#334155',
          },
        },
        axisLabel: {
          color: '#64748b',
          fontFamily: 'Rajdhani, sans-serif',
          fontWeight: 600,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false,
        },
        axisLabel: {
          color: '#64748b',
          fontFamily: 'Rajdhani, sans-serif',
          fontWeight: 600,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: '#334155',
            type: 'dashed',
          },
        },
      },
    });
  }
});
</script>

<template>
  <EchartsUI ref="chartRef" class="p3r-chart" />
</template>

<style scoped>
.p3r-chart {
  width: 100%;
  height: 300px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 20px rgba(59, 130, 246, 0.1);
  position: relative;
  overflow: hidden;
}

.p3r-chart::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 48%, rgba(59, 130, 246, 0.02) 49%, rgba(59, 130, 246, 0.02) 51%, transparent 52%),
    linear-gradient(-45deg, transparent 48%, rgba(0, 0, 0, 0.05) 49%, rgba(0, 0, 0, 0.05) 51%, transparent 52%);
  background-size: 20px 20px, 20px 20px;
  pointer-events: none;
  z-index: 0;
}

.p3r-chart :deep(.echarts-container) {
  position: relative;
  z-index: 1;
}
</style>
