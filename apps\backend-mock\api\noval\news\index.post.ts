import { unAuthorizedResponse, verifyAccessToken } from '../../utils';
import { getNextNewsId, addNews } from './data';

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const body = await readBody(event);
  
  // 生成新的ID
  const newId = getNextNewsId();
  
  // 创建新的消息
  const newNews = {
    id: body.id || userinfo.userId, // 消息发起人id，默认为当前用户
    otherId: body.otherId, // 消息接收者id
    news: body.news, // 消息内容
    type: body.type || 2, // 消息类型，默认为私聊信息
    createTime: new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    }).replace(/\//g, '-'),
  };

  // 添加到数据中
  addNews(newNews);

  return useResponseSuccess(null, '发送成功');
});
