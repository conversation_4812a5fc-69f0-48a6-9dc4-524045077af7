import type { PageQuery, BaseEntity } from '#/api/common';

export interface NovalForYhVO {
  /**
   * 小说id
   */
  id: string | number;

  /**
   * 小说名称
   */
  name: string;

  /**
   * 小说作者
   */
  author: string;

  /**
   * 小说状态
   */
  flag: string;

  /**
   * 小说平台
   */
  platform: string;

  /**
   * 小说成绩
   */
  grades: number;

  /**
   * 小说类型
   */
  type: string;

  /**
   * 小说简介
   */
  summary: string;

  /**
   * 小说图标
   */
  icon: string;

}

export interface NovalForYhForm extends BaseEntity {
}

export interface NovalForYhQuery extends PageQuery {
  /**
   * 小说名称
   */
  name?: string;

  /**
   * 小说作者
   */
  author?: string;

  /**
   * 小说平台
   */
  platform?: string;

  /**
   * 小说成绩
   */
  grades?: number;

  /**
   * 小说类型
   */
  type?: string;

  /**
    * 日期范围参数
    */
  params?: any;
}
