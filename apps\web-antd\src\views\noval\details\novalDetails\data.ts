import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getDictOptions } from '#/utils/dict';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'id',
    label: 'ID',
  },
  {
    component: 'Input',
    fieldName: 'type',
    label: '类型',
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: 'ID',
    field: 'id',
  },
  {
    title: '类型',
    field: 'type',
  },
  {
    title: '成绩',
    field: 'grades',
  },
  {
    title: '平台',
    field: 'platform',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'id',
    label: 'ID',
  },
  {
    component: 'Input',
    fieldName: 'grades',
    label: '成绩',
  },
  {
    component: 'ImageUpload',
    fieldName: 'icon',
    label: '图标',
    componentProps: {
      // accept: ['jpg'], // 不支持type/*的写法 支持拓展名(不带.) 文件头(image/png这种)
      maxNumber: 1, // 最大上传文件数 默认为1 为1会绑定为string而非string[]类型
      resultField: 'url', // 上传成功后返回的字段名 默认url 可选['ossId', 'url', 'fileName']
    },
    rules: 'required',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.SYS_NOVAL_TYPE 便于维护
      options: getDictOptions('sys_noval_type'),
    },
    fieldName: 'type',
    label: '小说分类',
  },
  {
    label: '小说平台',
    fieldName: 'platform',
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.SYS_NOVAL_PLATFORM 便于维护
      options: getDictOptions('sys_noval_platform'),
    },
  },
  {
    component: 'Input',
    label: '简介',
    fieldName: 'summary',
    componentProps: {
      type: 'textarea', // 强制转换为文本域
      rows: 3, // 设置初始可见行数为3行，可根据需要调整
      style: {
        minHeight: '60px', // 确保最小高度为60px
      },
    },
  },
];
