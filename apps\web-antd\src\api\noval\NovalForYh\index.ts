import type { NovalForYhVO, NovalForYhForm, NovalForYhQuery } from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询用户浏览书库列表
* @param params
* @returns 用户浏览书库列表
*/
export function NovalForYhList(params?: NovalForYhQuery) {
  return requestClient.get<PageResult<NovalForYhVO>>('/noval/NovalForYh/list', { params });
}

/**
 * 导出用户浏览书库列表
 * @param params
 * @returns 用户浏览书库列表
 */
export function NovalForYhExport(params?: NovalForYhQuery) {
  return commonExport('/noval/NovalForYh/export', params ?? {});
}

/**
 * 查询用户浏览书库详情
 * @param id id
 * @returns 用户浏览书库详情
 */
export function NovalForYhInfo(id: ID) {
  return requestClient.get<NovalForYhVO>(`/noval/NovalForYh/${id}`);
}

/**
 * 新增用户浏览书库
 * @param data
 * @returns void
 */
export function NovalForYhAdd(data: NovalForYhForm) {
  return requestClient.postWithMsg<void>('/noval/NovalForYh', data);
}

/**
 * 更新用户浏览书库
 * @param data
 * @returns void
 */
export function NovalForYhUpdate(data: NovalForYhForm) {
  return requestClient.putWithMsg<void>('/noval/NovalForYh', data);
}

/**
 * 删除用户浏览书库
 * @param id id
 * @returns void
 */
export function NovalForYhRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/noval/NovalForYh/${id}`);
}
