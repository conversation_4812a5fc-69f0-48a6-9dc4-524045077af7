import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'name',
    label: '小说名称',
  },
  {
    component: 'Input',
    fieldName: 'author',
    label: '小说作者',
  },
  {
    component: 'Input',
    fieldName: 'platform',
    label: '小说平台',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.SYS_NOVAL_TYPE 便于维护
      options: getDictOptions('sys_noval_type'),
    },
    fieldName: 'type',
    label: '小说类型',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '小说id',
    field: 'id',
  },
  {
    title: '小说名称',
    field: 'name',
  },
  {
    title: '小说作者',
    field: 'author',
  },
  {
    title: '小说状态',
    field: 'flag',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.SYS_NOVAL_STATE 便于维护
        return renderDict(row.flag, 'sys_noval_state');
      },
    },
  },
  {
    title: '小说平台',
    field: 'platform',
  },
  {
    title: '小说成绩',
    field: 'grades',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.SYS_NOVAL_PLATFORM 便于维护
        return renderDict(row.grades, 'sys_noval_platform');
      },
    },
  },
  {
    title: '小说类型',
    field: 'type',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.SYS_NOVAL_TYPE 便于维护
        return renderDict(row.type, 'sys_noval_type');
      },
    },
  },
  {
    title: '小说简介',
    field: 'summary',
  },
  {
    title: '小说图标',
    field: 'icon',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
];
