<script setup lang="ts">
import { ref } from 'vue';

import { Card, Switch, Input, Space } from 'ant-design-vue';

import TiebaForum from './index.vue';

// 演示配置
const forumName = ref('小说讨论吧');
const readonly = ref(false);
</script>

<template>
  <div class="demo-container">
    <Card title="百度贴吧风格论坛演示" class="demo-config">
      <Space direction="vertical" class="w-full">
        <div class="flex items-center gap-4">
          <label>贴吧名称:</label>
          <Input v-model:value="forumName" placeholder="贴吧名称" style="width: 200px" />
        </div>
        <div class="flex items-center gap-4">
          <label>只读模式:</label>
          <Switch v-model:checked="readonly" />
        </div>
      </Space>
    </Card>

    <!-- 贴吧论坛组件 -->
    <TiebaForum
      :forum-name="forumName"
      :readonly="readonly"
    />
  </div>
</template>

<style scoped>
/* P5X风格演示页面 */
.demo-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a0a0a 100%);
  position: relative;
  overflow: hidden;
}

.demo-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 0, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.demo-config {
  margin: 16px;
  flex-shrink: 0;
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border: 2px solid #444444;
  border-radius: 0;
  transform: skewX(-1deg);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.demo-config::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(220, 20, 60, 0.1), transparent);
  animation: shine 3s infinite;
}

.demo-config :deep(.ant-card-head) {
  background: linear-gradient(135deg, #dc143c, #8b0000);
  border-bottom: 2px solid #ff0000;
  transform: skewX(1deg);
  margin: -1px -1px 0 -1px;
}

.demo-config :deep(.ant-card-head-title) {
  color: white;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.demo-config :deep(.ant-card-body) {
  transform: skewX(1deg);
  background: transparent;
  color: #ffffff;
  position: relative;
  z-index: 1;
}

.demo-config label {
  min-width: 100px;
  font-weight: 600;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

/* 输入框样式 */
.demo-config :deep(.ant-input),
.demo-config :deep(.ant-switch) {
  background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
  border: 2px solid #444444;
  border-radius: 0;
  color: #ffffff;
  transition: all 0.3s ease;
}

.demo-config :deep(.ant-input:focus) {
  border-color: #dc143c;
  box-shadow: 0 0 10px rgba(220, 20, 60, 0.3);
}

.demo-config :deep(.ant-input::placeholder) {
  color: #666666;
}

/* 开关样式 */
.demo-config :deep(.ant-switch) {
  background: #333333;
  border: 2px solid #555555;
}

.demo-config :deep(.ant-switch-checked) {
  background: linear-gradient(135deg, #dc143c, #8b0000);
  border-color: #ff0000;
}

.demo-config :deep(.ant-switch:focus) {
  box-shadow: 0 0 10px rgba(220, 20, 60, 0.3);
}

/* 动画 */
@keyframes shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #dc143c, #8b0000);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #ff0000, #dc143c);
}
</style>

<style scoped>
label {
  min-width: 80px;
  font-weight: 500;
}
</style>
