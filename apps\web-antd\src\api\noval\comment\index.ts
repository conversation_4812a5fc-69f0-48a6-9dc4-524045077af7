import type { CommentVO, CommentForm, CommentQuery } from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询评论列表
* @param params
* @returns 评论列表
*/
export function commentList(params?: CommentQuery) {
  return requestClient.get<PageResult<CommentVO>>('/noval/comment/list', { params });
}

/**
 * 导出评论列表
 * @param params
 * @returns 评论列表
 */
export function commentExport(params?: CommentQuery) {
  return commonExport('/noval/comment/export', params ?? {});
}

/**
 * 查询评论详情
 * @param commerntId id
 * @returns 评论详情
 */
export function commentInfo(commerntId: ID) {
  return requestClient.get<CommentVO>(`/noval/comment/${commerntId}`);
}

/**
 * 新增评论
 * @param data
 * @returns void
 */
export function commentAdd(data: CommentForm) {
  return requestClient.postWithMsg<void>('/noval/comment', data);
}

/**
 * 更新评论
 * @param data
 * @returns void
 */
export function commentUpdate(data: CommentForm) {
  return requestClient.putWithMsg<void>('/noval/comment', data);
}

/**
 * 删除评论
 * @param commerntId id
 * @returns void
 */
export function commentRemove(commerntId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/noval/comment/${commerntId}`);
}

/**
 * 增加评论浏览量
 * @param commentData 评论数据（包含更新后的look值）
 * @returns void
 */
export function commentIncreaseLook(commentData: CommentForm) {
  return requestClient.putWithMsg<void>('/noval/comment', commentData);
}
