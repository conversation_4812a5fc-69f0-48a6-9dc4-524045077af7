import { unAuthorizedResponse, verifyAccessToken } from '../../utils';
import { updateNews } from './data';

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const body = await readBody(event);
  
  // 更新消息
  const success = updateNews(body.id, {
    otherId: body.otherId,
    news: body.news,
    type: body.type,
  });
  
  if (!success) {
    throw createError({
      statusCode: 404,
      statusMessage: '消息不存在',
    });
  }

  return useResponseSuccess(null, '更新成功');
});
