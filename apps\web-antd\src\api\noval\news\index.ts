import type { NewsVO, NewsForm, NewsQuery } from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询聊天列表
* @param params
* @returns 聊天列表
*/
export function newsList(params?: NewsQuery) {
  return requestClient.get<PageResult<NewsVO>>('/noval/news/list', { params });
}

/**
 * 导出聊天列表
 * @param params
 * @returns 聊天列表
 */
export function newsExport(params?: NewsQuery) {
  return commonExport('/noval/news/export', params ?? {});
}

/**
 * 查询聊天详情
 * @param id id
 * @returns 聊天详情
 */
export function newsInfo(id: ID) {
  return requestClient.get<NewsVO>(`/noval/news/${id}`);
}

/**
 * 新增聊天
 * @param data
 * @returns void
 */
export function newsAdd(data: NewsForm) {
  return requestClient.postWithMsg<void>('/noval/news', data);
}

/**
 * 更新聊天
 * @param data
 * @returns void
 */
export function newsUpdate(data: NewsForm) {
  return requestClient.putWithMsg<void>('/noval/news', data);
}

/**
 * 删除聊天
 * @param id id
 * @returns void
 */
export function newsRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/noval/news/${id}`);
}
