import type { AttentionVO, AttentionForm, AttentionQuery } from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询关注列表
* @param params
* @returns 关注列表
*/
export function attentionList(params?: AttentionQuery) {
  return requestClient.get<PageResult<AttentionVO>>('/noval/attention/list', { params });
}

/**
 * 导出关注列表
 * @param params
 * @returns 关注列表
 */
export function attentionExport(params?: AttentionQuery) {
  return commonExport('/noval/attention/export', params ?? {});
}

/**
 * 查询关注详情
 * @param id id
 * @returns 关注详情
 */
export function attentionInfo(id: ID) {
  return requestClient.get<AttentionVO>(`/noval/attention/${id}`);
}

/**
 * 新增关注
 * @param data
 * @returns void
 */
export function attentionAdd(data: AttentionForm) {
  return requestClient.postWithMsg<void>('/noval/attention', data);
}

/**
 * 更新关注
 * @param data
 * @returns void
 */
export function attentionUpdate(data: AttentionForm) {
  return requestClient.putWithMsg<void>('/noval/attention', data);
}

/**
 * 删除关注
 * @param id id
 * @returns void
 */
export function attentionRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/noval/attention/${id}`);
}
