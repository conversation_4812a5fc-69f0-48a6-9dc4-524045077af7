import { unAuthorizedResponse, verifyAccessToken } from '../../utils';
import { findNews } from './data';

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const newsId = Number(getRouterParam(event, 'id'));
  const news = findNews(newsId);

  if (!news) {
    throw createError({
      statusCode: 404,
      statusMessage: '消息不存在',
    });
  }

  return useResponseSuccess(news);
});
