import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';


export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'id',
    label: '消息发起人id',
  },
  {
    component: 'Input',
    fieldName: 'otherId',
    label: '消息接收者id',
  },
  {
    component: 'Input',
    fieldName: 'news',
    label: '消息内容',
  },
  {
    component: 'Select',
    fieldName: 'type',
    label: '消息类型',
    componentProps: {
      dictType: 'new_type',
      placeholder: '请选择消息类型',
    },
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '消息发起人id',
    field: 'id',
    width: 120,
  },
  {
    title: '消息接收者id',
    field: 'otherId',
    width: 120,
  },
  {
    title: '消息内容',
    field: 'news',
    minWidth: 200,
  },
  {
    title: '消息类型',
    field: 'type',
    width: 100,
    slots: { default: 'type' },
  },
  {
    title: '创建时间',
    field: 'createTime',
    width: 160,
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '消息发起人id',
    fieldName: 'id',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '消息接收者id',
    fieldName: 'otherId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '消息内容',
    fieldName: 'news',
    component: 'Textarea',
    rules: 'required',
    componentProps: {
      rows: 4,
      placeholder: '请输入消息内容',
    },
  },
  {
    label: '消息类型',
    fieldName: 'type',
    component: 'Select',
    rules: 'required',
    componentProps: {
      dictType: 'new_type',
      placeholder: '请选择消息类型',
    },
  },
];
