{"Version": 3, "Meta": {"PhysicsSettingCount": 35, "TotalInputCount": 113, "TotalOutputCount": 91, "VertexCount": 139, "Fps": 60, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "z"}, {"Id": "PhysicsSetting2", "Name": "x"}, {"Id": "PhysicsSetting3", "Name": "y"}, {"Id": "PhysicsSetting4", "Name": "hair"}, {"Id": "PhysicsSetting5", "Name": "eye"}, {"Id": "PhysicsSetting6", "Name": "hx"}, {"Id": "PhysicsSetting7", "Name": "hair2"}, {"Id": "PhysicsSetting8", "Name": "eye1"}, {"Id": "PhysicsSetting9", "Name": "eye1(2)"}, {"Id": "PhysicsSetting10", "Name": "后发"}, {"Id": "PhysicsSetting11", "Name": "鬓角"}, {"Id": "PhysicsSetting12", "Name": "鬓角(2)"}, {"Id": "PhysicsSetting13", "Name": "hand"}, {"Id": "PhysicsSetting14", "Name": "hand(2)"}, {"Id": "PhysicsSetting15", "Name": "项链"}, {"Id": "PhysicsSetting16", "Name": "外套裙子x"}, {"Id": "PhysicsSetting17", "Name": "外套裙子y"}, {"Id": "PhysicsSetting18", "Name": "hdj"}, {"Id": "PhysicsSetting19", "Name": "坠子"}, {"Id": "PhysicsSetting20", "Name": "ry"}, {"Id": "PhysicsSetting21", "Name": "ry(2)"}, {"Id": "PhysicsSetting22", "Name": "蝴蝶结"}, {"Id": "PhysicsSetting23", "Name": "耳朵铃铛"}, {"Id": "PhysicsSetting24", "Name": "花边"}, {"Id": "PhysicsSetting25", "Name": "花边(y)"}, {"Id": "PhysicsSetting26", "Name": "蝴蝶"}, {"Id": "PhysicsSetting27", "Name": "蝴蝶(2)"}, {"Id": "PhysicsSetting28", "Name": "马尾"}, {"Id": "PhysicsSetting29", "Name": "马尾(2)"}, {"Id": "PhysicsSetting30", "Name": "外套"}, {"Id": "PhysicsSetting31", "Name": "外套(2)"}, {"Id": "PhysicsSetting32", "Name": "衬衫"}, {"Id": "PhysicsSetting33", "Name": "衬衫(2)"}, {"Id": "PhysicsSetting34", "Name": "耳朵"}, {"Id": "PhysicsSetting35", "Name": "耳朵(2)"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 20, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "VertexIndex": 1, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "ParamAngleZ"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleZ2"}, "VertexIndex": 1, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.65, "Delay": 1.25, "Acceleration": 0.8, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -40, "Default": 0, "Maximum": 40}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 20, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "ParamAngleX"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.65, "Delay": 1.25, "Acceleration": 0.8, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -40, "Default": 0, "Maximum": 40}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "VertexIndex": 1, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamAngleY"}, "VertexIndex": 1, "Scale": 35, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.65, "Delay": 1.25, "Acceleration": 0.8, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -40, "Default": 0, "Maximum": 40}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairFront"}, "VertexIndex": 1, "Scale": 2.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairFront3"}, "VertexIndex": 2, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param16"}, "VertexIndex": 1, "Scale": 3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param17"}, "VertexIndex": 2, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param18"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param19"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting7", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairFront2"}, "VertexIndex": 1, "Scale": 2.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairFront4"}, "VertexIndex": 2, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting8", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting9", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting10", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 5, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBack9"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack10"}, "VertexIndex": 2, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 38}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting11", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 5, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 38}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting12", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 5, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 38}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting13", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param"}, "VertexIndex": 1, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param3"}, "VertexIndex": 2, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param2"}, "VertexIndex": 3, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param4"}, "VertexIndex": 4, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 38}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting14", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 5, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param5"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param7"}, "VertexIndex": 2, "Scale": 8, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param6"}, "VertexIndex": 3, "Scale": 5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param8"}, "VertexIndex": 4, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 38}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 48}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting15", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 5, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.88, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.88, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.88, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting16", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBack13"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack22"}, "VertexIndex": 2, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting17", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 100, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBack20"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack21"}, "VertexIndex": 2, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting18", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting19", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 5, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting20", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting21", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting22", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh124"}, "VertexIndex": 1, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh124"}, "VertexIndex": 2, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh124"}, "VertexIndex": 3, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh124"}, "VertexIndex": 4, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh124"}, "VertexIndex": 5, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh127"}, "VertexIndex": 1, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh127"}, "VertexIndex": 2, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh127"}, "VertexIndex": 3, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh127"}, "VertexIndex": 4, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh127"}, "VertexIndex": 5, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh126"}, "VertexIndex": 1, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh126"}, "VertexIndex": 2, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh126"}, "VertexIndex": 3, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh126"}, "VertexIndex": 4, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh126"}, "VertexIndex": 5, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh125"}, "VertexIndex": 1, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh125"}, "VertexIndex": 2, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh125"}, "VertexIndex": 3, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh125"}, "VertexIndex": 4, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh125"}, "VertexIndex": 5, "Scale": 18, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting23", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 33, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 33, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 5, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 33, "Type": "X", "Reflect": false}], "Output": [], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -20, "Default": 0, "Maximum": 20}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting24", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting25", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting26", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh189"}, "VertexIndex": 1, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh189"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh189"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh189"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh189"}, "VertexIndex": 5, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh188"}, "VertexIndex": 1, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh188"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh188"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh188"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh188"}, "VertexIndex": 5, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting27", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh187"}, "VertexIndex": 1, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh187"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh187"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh187"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh187"}, "VertexIndex": 5, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh186"}, "VertexIndex": 1, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh186"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh186"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh186"}, "VertexIndex": 4, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh186"}, "VertexIndex": 5, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting28", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBack"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack3"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack2"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack4"}, "VertexIndex": 4, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting29", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBack5"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack6"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack7"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack8"}, "VertexIndex": 4, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting30", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBack14"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack15"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.88, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.88, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.88, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting31", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBack16"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack17"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.88, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.88, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.88, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting32", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBack11"}, "VertexIndex": 1, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack12"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting33", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBack18"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack19"}, "VertexIndex": 2, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.85, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting34", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 33, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 33, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 5, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 34, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param10"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param11"}, "VertexIndex": 2, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param12"}, "VertexIndex": 3, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -20, "Default": 0, "Maximum": 20}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}, {"Id": "PhysicsSetting35", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 33, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 33, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 5, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 30, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param13"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param14"}, "VertexIndex": 2, "Scale": 13, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param15"}, "VertexIndex": 3, "Scale": 13, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -20, "Default": 0, "Maximum": 20}, "Angle": {"Minimum": -20, "Default": 0, "Maximum": 20}}}]}