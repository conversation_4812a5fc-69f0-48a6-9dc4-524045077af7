# Live2D文件原名功能实现

## 🎯 功能概述

为Live2D模型管理系统添加了**文件原名**功能，在上传Live2D模型文件夹时会自动获取并保存每个文件的原始文件名。

**功能说明**：
- 📝 **原名保存**: 自动记录每个文件的原始文件名（如：`Idle.motion3.json`、`悲伤表情.exp3.json`）
- 📊 **数组传输**: 前端将文件URL数组和原名数组一起传递给后端
- 🔄 **后端循环**: 后端接收数组参数，循环为每个文件创建数据库记录
- 🚫 **不可编辑**: 用户无法手动修改原名，确保数据准确性
- 👁️ **表格显示**: 在列表中显示每个文件的原名，便于识别和管理

**示例文件名**：
- `ariu.model3.json` (主模型文件)
- `Idle.motion3.json` (动作文件)
- `悲伤表情.exp3.json` (表情文件)
- `physics.physics3.json` (物理文件)

## 📋 数据模型

### **接口定义已完成**

在 `apps/web-antd/src/api/noval/live2d/model.d.ts` 中：

```typescript
export interface Live2dVO {
  id: string | number;
  name: string;           // Live2D显示名称
  icon: string;           // Live2D路径
  type: number;           // Live2D类型
  original_name: string;  // ✅ Live2D原名（必填）
}

export interface Live2dForm extends BaseEntity {
  id?: string | number;
  name?: string;
  icon?: string;
  type?: number;
  modelFolder?: any[];           // 前端上传用
  original_name?: string;        // ✅ Live2D原名（可选）
}

export interface Live2dQuery extends PageQuery {
  id?: string | number;
  name?: string;
  icon?: string;
  type?: number;
  params?: any;
  original_name?: string;        // ✅ Live2D原名查询（可选）
}
```

## 🔧 功能实现

### **1. 自定义上传API保存原名**

在 `apps/web-antd/src/views/noval/live2d/upload-api.ts` 中：

```typescript
// 全局存储文件名映射
declare global {
  interface Window {
    live2dFileNameMap: Record<string, string>;
  }
}

export function live2dUploadApi(file: Blob | File, onUploadProgress?: AxiosProgressEvent) {
  return new Promise((resolve, reject) => {
    const formData = new FormData();
    formData.append('file', file);

    // 保存原始文件名
    const originalName = file instanceof File ? file.name : 'unknown';

    requestClient.post('/resource/oss/upload', formData, { /* ... */ })
    .then((response) => {
      // 保存文件名映射到全局变量
      const fileUrl = response.data?.url || response.url;
      if (fileUrl) {
        window.live2dFileNameMap[fileUrl] = originalName;  // ✅ 保存原名映射
        console.log('保存文件名映射:', fileUrl, '->', originalName);
      }
      resolve(response);
    });
  });
}
```

### **2. 表单配置使用自定义API**

在 `apps/web-antd/src/views/noval/live2d/data.ts` 中：

```typescript
{
  label: 'Live2D模型文件',
  fieldName: 'icon',
  component: 'FileUpload',
  componentProps: {
    // ... 其他配置
    api: live2dUploadApi,  // ✅ 使用自定义上传API
    resultField: 'url',
  },
}
```

### **3. 数组参数提交**

在 `apps/web-antd/src/views/noval/live2d/live2d-modal.vue` 中：

```typescript
// 从全局存储的文件信息中获取原始文件名数组
const originalNames = data.icon.map((fileUrl: string) => {
  let originalName = fileUrl.split('/').pop(); // 默认使用URL中的文件名

  // 从全局映射中获取真实的原始文件名
  if (window.live2dFileNameMap && window.live2dFileNameMap[fileUrl]) {
    originalName = window.live2dFileNameMap[fileUrl];  // ✅ 使用保存的原名
  }

  return originalName;
});

// 准备提交数据，包含icon数组和original_name数组
const submitData = {
  name: data.name,
  type: data.type,
  icon: data.icon,                    // ✅ 文件URL数组
  original_name: originalNames        // ✅ 原始文件名数组
};

// 调用后端API，传递数组参数
await live2dAdd(submitData);
```

### **4. 接口类型定义**

在 `apps/web-antd/src/api/noval/live2d/model.d.ts` 中：

```typescript
export interface Live2dForm extends BaseEntity {
  name?: string;
  type?: number;

  /**
   * Live2d路径 (支持单个字符串或字符串数组)
   */
  icon?: string | string[];

  /**
   * live2d原名 (支持单个字符串或字符串数组)
   */
  original_name?: string | string[];
}
```

### **3. 表格显示**

在表格列中添加了原名显示：

```typescript
export const columns: VxeGridProps['columns'] = [
  // ... 其他列
  {
    title: 'Live2D名称',
    field: 'name',
    width: 250,              // 调整宽度为原名让出空间
    showOverflow: 'tooltip',
    align: 'left',
  },
  {
    title: '原名',             // ✅ 新增原名列
    field: 'original_name',
    width: 200,
    showOverflow: 'tooltip',
    align: 'left',
    slots: {
      default: ({ row }) => {
        return row.original_name || '-';
      },
    },
  },
  // ... 其他列
];
```

### **4. 查询功能**

在查询表单中添加了原名搜索：

```typescript
export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'name',
    label: '名字',
  },
  {
    component: 'Input',        // ✅ 新增原名查询
    fieldName: 'original_name',
    label: '原名',
  },
  {
    component: 'Select',
    componentProps: {
      dictType: 'live_type',
      placeholder: '请选择Live2d类型',
    },
    fieldName: 'type',
    label: '类型',
  },
];
```

## 🎯 功能特点

### **1. 自动填充**
- **上传时自动获取**: 从文件夹的 `webkitRelativePath` 自动提取文件夹名称
- **总是设置**: 每次上传都会自动设置原名，确保数据完整性
- **日志记录**: 控制台输出自动设置的原名，便于调试

### **2. 用户友好**
- **界面简洁**: 表单中不显示原名字段，避免界面复杂化
- **数据准确**: 不允许手动编辑，确保原名的真实性
- **容错处理**: 如果无法获取文件夹名称，不会影响上传流程

### **3. 完整功能**
- **表格显示**: 在列表中显示原名，便于识别
- **搜索功能**: 支持按原名搜索Live2D模型
- **数据完整性**: 所有相关接口都支持原名字段

## 🔄 工作流程

### **上传流程**:
```
1. 用户选择Live2D文件夹 (如: ariu/)
   ↓
2. 自定义上传API逐个上传文件
   ↓
3. 保存文件URL与原名的映射关系
   ↓
4. 表单提交时传递icon数组和original_name数组
   ↓
5. 后端循环创建数据库记录 (每个文件一条记录)
```

### **显示流程**:
```
1. 列表页面显示所有文件记录
   ↓
2. 每条记录显示文件的原名
   ↓
3. 支持按原名搜索特定文件
   ↓
4. 可以按模型名称查看同一模型的所有文件
```

## 📊 示例数据

### **上传前**:
```
文件夹结构:
ariu/
├── ariu.model3.json
├── ariu.moc3
├── Idle.motion3.json
├── 悲伤表情.exp3.json
├── physics.physics3.json
├── textures/
│   └── default.png
└── motions/
    └── Idle.motion3.json
```

### **上传后** (每个文件一条记录):
```typescript
[
  {
    id: 1,
    name: "阿留",                           // 用户输入的模型名称
    original_name: "ariu.model3.json",      // ✅ 文件原名
    icon: "/live2d/ariu/ariu.model3.json",
    type: 1
  },
  {
    id: 2,
    name: "阿留",                           // 相同的模型名称
    original_name: "Idle.motion3.json",     // ✅ 文件原名
    icon: "/live2d/ariu/Idle.motion3.json",
    type: 1
  },
  {
    id: 3,
    name: "阿留",                           // 相同的模型名称
    original_name: "悲伤表情.exp3.json",     // ✅ 文件原名
    icon: "/live2d/ariu/悲伤表情.exp3.json",
    type: 1
  }
  // ... 更多文件记录
]
```

### **表格显示**:
| Live2D名称 | 原名 | 模型类型 | 创建时间 |
|-----------|------|----------|----------|
| 阿留      | ariu.model3.json | 看板娘   | 2024-01-01 |
| 阿留      | Idle.motion3.json | 看板娘   | 2024-01-01 |
| 阿留      | 悲伤表情.exp3.json | 看板娘   | 2024-01-01 |
| 阿留      | physics.physics3.json | 看板娘   | 2024-01-01 |

## 🚀 优势

### **1. 数据追溯**
- 保留文件夹的原始名称，便于追溯和管理
- 避免因重命名导致的混乱

### **2. 批量管理**
- 可以按原名批量查找和管理相关模型
- 便于识别同一系列的不同版本

### **3. 用户体验**
- 自动设置减少用户操作复杂度
- 界面简洁，不显示不必要的字段
- 保持数据的一致性和完整性

### **4. 数据准确性**
- 不允许手动编辑，确保原名的真实性
- 自动获取，避免人为错误

### **5. 系统完整性**
- 前端、后端、数据库全链路支持
- 查询、显示功能完备

## 🎊 总结

通过添加原名功能：

1. **✅ 数据模型完整**: 所有接口都支持原名字段
2. **✅ 自动化处理**: 上传时自动获取文件夹名称
3. **✅ 界面简洁**: 表单不显示原名字段，避免复杂化
4. **✅ 数据准确**: 不可手动编辑，确保原名真实性
5. **✅ 功能完备**: 显示、搜索功能完整
6. **✅ 数据追溯**: 保留原始信息，便于管理

现在Live2D模型管理系统具备了完整的原名功能，提升了数据管理的便利性和可追溯性！
