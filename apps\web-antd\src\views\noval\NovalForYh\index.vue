<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { Pagination, Spin, Empty } from 'ant-design-vue';
import {NovalForYhList} from '#/api/noval/NovalForYh';
import { novalDirectoryList } from '#/api/noval/details/noval-directory';
import { getDictOptions } from '#/utils/dict';
import NovalForYhModal from './NovalForYh-modal.vue';

const router = useRouter();
const route = useRoute();

// 响应式数据
const novelList = ref<any[]>([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 10, // 每页10个
  total: 0,
});
const searchForm = reactive({
  name: '',
  author: '',
  platform: '',
  type: '',
});

// 存储每本小说的最新章节信息
const latestChapters = ref<Record<string, string>>({});

// 获取字典数据
const platformOptions = getDictOptions('sys_noval_platform');
const stateOptions = getDictOptions('sys_noval_state');
const typeOptions = getDictOptions('sys_noval_type');



// 字典值转换为标签
const getDictLabel = (dictOptions: any[], value: string | number) => {
  if (!dictOptions || dictOptions.length === 0) {
    return value?.toString() || '未知';
  }

  // 尝试精确匹配
  let option = dictOptions.find(item => item.value === value);

  // 如果精确匹配失败，尝试类型转换匹配
  if (!option) {
    option = dictOptions.find(item => item.value == value);
  }

  // 如果还是没找到，尝试字符串匹配
  if (!option && value !== undefined && value !== null) {
    option = dictOptions.find(item => item.value?.toString() === value.toString());
  }

  return option ? option.label : value?.toString() || '未知';
};

// URL参数转换为字典值的映射表
const urlToTypeMapping: Record<string, string> = {
  'a0': '0',  // 玄幻
  'a1': '1',  // 都市
  'a2': '2',  // 科幻
  'a3': '3',  // 历史
  'a4': '4',  // 军事
  'a5': '5',  // 游戏
  'a6': '6',  // 体育
  'a7': '7',  // 灵异
  'a8': '8',  // 同人
  'a9': '9',  // 其他
};

// URL参数转换为字典值
const convertUrlParamToTypeValue = (urlParam: string): string => {
  // 优先使用映射表
  if (urlToTypeMapping[urlParam]) {
    return urlToTypeMapping[urlParam];
  }

  // 如果是 a0, a1, a2 格式，转换为 0, 1, 2
  if (urlParam.startsWith('a')) {
    return urlParam.replace(/^a/, '');
  }

  // 直接返回原值
  return urlParam;
};

// 验证字典值是否有效
const isValidTypeValue = (value: string): boolean => {
  const validTypeValues = typeOptions.map(option => option.value);
  return validTypeValues.includes(value);
};





// 页面加载时获取数据
onMounted(() => {
  // 处理 URL 参数
  const platform = route.params.platform as string;
  const type = route.params.type as string;



  if (platform) {
    searchForm.platform = platform;
  }

  if (type) {
    const convertedType = convertUrlParamToTypeValue(type);
    if (isValidTypeValue(convertedType)) {
      searchForm.type = convertedType;
    } else {
      searchForm.type = '';
    }
  }



  // 如果没有路由参数，检查是否是直接路径访问
  if (!platform && !type) {
    // 检查路径是否匹配 /noval/NovalForYh/a/a0 这样的格式
    const pathMatch = route.path.match(/\/noval\/NovalForYh\/([^\/]+)\/([^\/]+)/);
    if (pathMatch && pathMatch[1] && pathMatch[2]) {
      const pathPlatform = pathMatch[1];
      const pathType = pathMatch[2];
      searchForm.platform = pathPlatform;

      const convertedType = convertUrlParamToTypeValue(pathType);
      if (isValidTypeValue(convertedType)) {
        searchForm.type = convertedType;
      } else {
        searchForm.type = '';
      }
    }
  }

  loadData();
});

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;
    const params = {
      ...searchForm,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };

    const response = await NovalForYhList(params);
    novelList.value = response.rows || [];
    pagination.total = response.total || 0;



    // 加载完小说列表后，获取最新章节信息
    if (novelList.value.length > 0) {
      await loadLatestChapters();
    }
  } catch (error) {
    console.error('加载数据失败:', error);
    novelList.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

// 重置处理
const handleReset = () => {
  searchForm.name = '';
  searchForm.author = '';
  searchForm.platform = '';
  searchForm.type = '';
  pagination.current = 1;
  loadData();
};

// 分页处理
const handlePageChange = (page: number, pageSize: number) => {
  pagination.current = page;
  pagination.pageSize = pageSize;
  loadData();
};

const handlePageSizeChange = (_current: number, size: number) => {
  pagination.current = 1;
  pagination.pageSize = size;
  loadData();
};

// 点击小说卡片跳转到详情页
const handleNovelClick = (novel: any) => {
  if (novel.id) {
    router.push(`/noval/detail/${novel.id}`);
  }
};



// 格式化排名
const formatRanking = (ranking: number) => {
  if (!ranking) return '暂无排名';
  return `排名 ${ranking}`;
};

// 获取最新章节
const getLatestChapter = async (novelId: string) => {
  try {
    const response = await novalDirectoryList({
      id: novelId,
      pageNum: 1,
      pageSize: 10, // 获取前10章，然后找到最新的
    });

    if (response.rows && response.rows.length > 0) {
      // 按章节序号降序排列，获取最新章节
      const sortedChapters = response.rows.sort((a, b) => {
        const chapterA = Number(a.chapter) || 0;
        const chapterB = Number(b.chapter) || 0;
        return chapterB - chapterA;
      });
      const latestChapter = sortedChapters[0];
      latestChapters.value[novelId] = latestChapter?.chaptername || '暂无章节';
    } else {
      latestChapters.value[novelId] = '暂无章节';
    }
  } catch (error) {
    console.error(`获取小说${novelId}最新章节失败:`, error);
    latestChapters.value[novelId] = '获取失败';
  }
};

// 批量获取所有小说的最新章节
const loadLatestChapters = async () => {
  const promises = novelList.value.map(novel => {
    if (novel.id) {
      return getLatestChapter(novel.id);
    }
    return Promise.resolve();
  });

  await Promise.all(promises);
};

// 修复图片链接（处理重复的https://问题）
const fixImageUrl = (url: string) => {
  if (!url) return 'https://via.placeholder.com/120x160/f0f0f0/999999?text=暂无封面';

  // 修复重复的 https:// 问题
  if (url.startsWith('https://https://')) {
    return url.replace('https://https://', 'https://');
  }

  // 修复重复的 http:// 问题
  if (url.startsWith('http://https://')) {
    return url.replace('http://https://', 'https://');
  }

  if (url.startsWith('http://http://')) {
    return url.replace('http://http://', 'http://');
  }

  return url;
};

// 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.src = 'https://via.placeholder.com/120x160/f0f0f0/999999?text=暂无封面';
};

// 重新加载数据
const handleReload = () => {
  loadData();
};

</script>

<template>
  <div class="novel-page">
    <!-- 搜索表单 -->
    <div class="search-section">
      <div class="search-form">
        <div class="search-inputs">
          <div class="input-group">
            <label class="input-label">小说名称</label>
            <input
              v-model="searchForm.name"
              placeholder="请输入小说名称"
              class="search-input"
              @keyup.enter="handleSearch"
            />
          </div>
          <div class="input-group">
            <label class="input-label">作者</label>
            <input
              v-model="searchForm.author"
              placeholder="请输入作者"
              class="search-input"
              @keyup.enter="handleSearch"
            />
          </div>
        </div>
        <div class="search-buttons">
          <button
            type="button"
            @click="handleSearch"
            :disabled="loading"
            class="search-btn primary"
          >
            {{ loading ? '搜索中...' : '搜索' }}
          </button>
          <button
            type="button"
            @click="handleReset"
            class="search-btn secondary"
          >
            重置
          </button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <Spin size="large" />
    </div>

    <!-- 小说卡片列表 -->
    <div v-else class="novels-container">


      <div class="novels-grid">
        <div
          v-for="novel in novelList"
          :key="novel.id"
          class="novel-card"
          @click="handleNovelClick(novel)"
        >
          <!-- 左侧封面 -->
          <div class="book-cover">
            <img
              :src="fixImageUrl(novel.icon)"
              :alt="novel.name"
              class="cover-img"
              @error="handleImageError"
            />
          </div>

          <!-- 右侧信息 -->
          <div class="book-info">
            <!-- 书名 -->
            <h3 class="book-title">{{ novel.name || '未知书名' }}</h3>

            <!-- 作者和标签 -->
            <div class="book-meta">
              <span class="author">{{ novel.author || '未知作者' }}</span>
              <span class="dot">·</span>
              <span class="platform">{{ getDictLabel(platformOptions, novel.platform) }}</span>
              <span class="dot">·</span>
              <span class="category">{{ getDictLabel(typeOptions, novel.type) }}</span>
              <span class="dot">·</span>
              <span class="status">{{ getDictLabel(stateOptions, novel.flag) }}</span>
            </div>

            <!-- 简介 -->
            <p class="book-desc">{{ novel.summary || '暂无简介，这是一本精彩的小说，值得一读。' }}</p>

            <!-- 底部信息 -->
            <div class="book-stats">
              <span class="ranking">{{ formatRanking(novel.grades) }}</span>
              <span class="divider">|</span>
              <span class="latest">{{ latestChapters[novel.id] || '加载中...' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!novelList.length && !loading" class="empty-state">
        <Empty description="暂无数据" />
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <Pagination
        v-model:current="pagination.current"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :show-size-changer="true"
        :show-quick-jumper="true"
        :show-total="(total: number, range: number[]) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
        @change="handlePageChange"
        @show-size-change="handlePageSizeChange"
      />
    </div>

    <!-- 外链提示 -->
    <div class="external-link-notice">
      <div class="notice-content">
        <div class="notice-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M13 3L13.7071 2.29289C14.0976 1.90237 14.0976 1.26921 13.7071 0.878679C13.3166 0.488155 12.6834 0.488155 12.2929 0.878679L13 3ZM9 7L8.29289 7.70711C8.68342 8.09763 9.31658 8.09763 9.70711 7.70711L9 7ZM17 9L17.7071 8.29289C18.0976 7.90237 18.0976 7.26921 17.7071 6.87868C17.3166 6.48816 16.6834 6.48816 16.2929 6.87868L17 9ZM21 13L21.7071 12.2929C22.0976 11.9024 22.0976 11.2692 21.7071 10.8787C21.3166 10.4882 20.6834 10.4882 20.2929 10.8787L21 13ZM12.2929 2.29289L8.29289 6.29289L9.70711 7.70711L13.7071 3.70711L12.2929 2.29289ZM16.2929 8.29289L20.2929 12.2929L21.7071 13.7071L17.7071 9.70711L16.2929 8.29289Z" fill="currentColor"/>
            <path d="M21 13V19C21 20.1046 20.1046 21 19 21H5C3.89543 21 3 20.1046 3 19V5C3 3.89543 3.89543 3 5 3H11" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </div>
        <div class="notice-text">
          <h4 class="notice-title">外链提示</h4>
          <p class="notice-desc">所有章节链接皆为网址外链，点击后将跳转到外部网站进行阅读</p>
        </div>
      </div>
    </div>

    <NovalForYhModal @reload="handleReload" />
  </div>
</template>

<style scoped>
/* 页面容器 - P5X风格 */
.novel-page {
  padding: 24px;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

.novel-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(147, 51, 234, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(6, 182, 212, 0.04) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

.novel-page::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 48%, rgba(59, 130, 246, 0.02) 49%, rgba(59, 130, 246, 0.02) 51%, transparent 52%),
    linear-gradient(-45deg, transparent 48%, rgba(147, 51, 234, 0.02) 49%, rgba(147, 51, 234, 0.02) 51%, transparent 52%);
  background-size: 60px 60px;
  pointer-events: none;
  z-index: 0;
}

/* 搜索区域 - P5X风格 */
.search-section {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
  backdrop-filter: blur(20px);
  padding: 32px;
  border-radius: 16px;
  margin-bottom: 24px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.search-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.05) 50%, transparent 70%),
    radial-gradient(circle at 30% 30%, rgba(147, 51, 234, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.search-section::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  animation: rotate 20s linear infinite;
  pointer-events: none;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.search-form {
  display: flex;
  align-items: flex-end;
  gap: 24px;
  flex-wrap: wrap;
  position: relative;
  z-index: 2;
}

.search-inputs {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  flex: 1;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
}

.input-label {
  font-size: 14px;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 4px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 0.5px;
  position: relative;
}

.input-label::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transition: width 0.3s ease;
}

.input-group:focus-within .input-label::after {
  width: 100%;
}

.search-input {
  width: 280px;
  height: 48px;
  padding: 12px 20px;
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  font-size: 14px;
  color: #f1f5f9;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  font-weight: 500;
  letter-spacing: 0.5px;
  position: relative;
}

.search-input:focus {
  border-color: #3b82f6;
  box-shadow:
    0 0 0 3px rgba(59, 130, 246, 0.2),
    0 8px 32px rgba(59, 130, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  background: rgba(15, 23, 42, 0.95);
  transform: translateY(-2px);
}

.search-input::placeholder {
  color: #64748b;
  font-style: normal;
  transition: color 0.3s ease;
}

.search-input:focus::placeholder {
  color: #94a3b8;
}

/* 下拉选择框样式 */
.search-input select,
select.search-input {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2394a3b8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  padding-right: 40px;
  cursor: pointer;
}

.search-input select:focus,
select.search-input:focus {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

.search-input option {
  background: rgba(15, 23, 42, 0.95);
  color: #f1f5f9;
  padding: 8px 12px;
}

.search-buttons {
  display: flex;
  gap: 12px;
}

.search-btn {
  height: 48px;
  padding: 12px 32px;
  border: none;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.search-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.search-btn:hover::before {
  left: 100%;
}

.search-btn.primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  border: 2px solid rgba(59, 130, 246, 0.5);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.search-btn.primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #1d4ed8 0%, #3b82f6 100%);
  transform: translateY(-3px);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.4);
  border-color: #60a5fa;
}

.search-btn.primary:disabled {
  background: rgba(71, 85, 105, 0.5);
  color: #94a3b8;
  cursor: not-allowed;
  border-color: rgba(71, 85, 105, 0.3);
  box-shadow: none;
}

.search-btn.secondary {
  background: rgba(15, 23, 42, 0.8);
  color: #e2e8f0;
  border: 2px solid rgba(147, 51, 234, 0.5);
  box-shadow: 0 4px 16px rgba(147, 51, 234, 0.2);
}

.search-btn.secondary:hover {
  background: rgba(147, 51, 234, 0.2);
  border-color: #a855f7;
  transform: translateY(-3px);
  box-shadow: 0 8px 32px rgba(147, 51, 234, 0.3);
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 小说容器 - P5X风格 */
.novels-container {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(147, 51, 234, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(147, 51, 234, 0.3);
  position: relative;
  z-index: 1;
}

/* 搜索结果信息 */
.search-result-info {
  margin-bottom: 24px;
  padding: 16px 20px;
  background: #f6f8fa;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
}

.result-count {
  font-size: 18px;
  font-weight: 700;
  color: #60a5fa;
  margin-right: 20px;
  text-shadow: 0 2px 8px rgba(96, 165, 250, 0.5);
  letter-spacing: 0.5px;
}

.search-condition {
  font-size: 14px;
  color: #e2e8f0;
  font-weight: 500;
}

.condition-tag {
  display: inline-block;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: #ffffff;
  padding: 6px 16px;
  margin-right: 12px;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.5px;
  border-radius: 8px;
  border: 1px solid rgba(139, 92, 246, 0.5);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

/* 小说网格 - 每行2个 */
.novels-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  width: 100%;
}

/* 单个小说卡片 - P5X风格 */
.novel-card {
  display: flex;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 200px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.novel-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.05) 50%, transparent 70%),
    radial-gradient(circle at 30% 30%, rgba(147, 51, 234, 0.1) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.novel-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  animation: rotate 20s linear infinite;
  opacity: 0;
  z-index: -1;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.novel-card:hover {
  box-shadow:
    0 32px 64px rgba(59, 130, 246, 0.4),
    0 0 0 1px rgba(139, 92, 246, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(139, 92, 246, 0.6);
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.98) 100%);
}

.novel-card:hover::before {
  opacity: 1;
}

.novel-card:hover::after {
  opacity: 1;
}



/* 封面区域 */
.book-cover {
  flex-shrink: 0;
  width: 135px;
  height: 180px;
  overflow: hidden;
  background: rgba(30, 41, 59, 0.8);
  border-radius: 8px 0 0 8px;
}

.cover-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.novel-card:hover .cover-img {
  transform: scale(1.03);
}

/* 书籍信息区域 */
.book-info {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
  background: transparent;
}

/* 书名 - P5X风格 */
.book-title {
  font-size: 18px;
  font-weight: 700;
  color: #60a5fa;
  margin: 0 0 12px 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-shadow: 0 2px 8px rgba(96, 165, 250, 0.5);
  letter-spacing: 0.5px;
}

/* 元信息 - P5X风格 */
.book-meta {
  font-size: 13px;
  color: #cbd5e1;
  margin-bottom: 12px;
  line-height: 1.6;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.dot {
  margin: 0 8px;
  color: #3b82f6;
  font-weight: bold;
}

.author {
  color: #a78bfa;
  font-weight: 600;
  text-shadow: 0 1px 4px rgba(167, 139, 250, 0.5);
}

.platform {
  color: #34d399;
  font-weight: 600;
}

.category {
  color: #f472b6;
  font-weight: 600;
}

.status {
  color: #fbbf24;
  font-weight: 600;
}

/* 简介 - P5X风格 */
.book-desc {
  font-size: 14px;
  color: #94a3b8;
  line-height: 1.6;
  margin: 0;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  flex: 1;
  font-weight: 400;
}

/* 底部统计 - P5X风格 */
.book-stats {
  font-size: 13px;
  color: #64748b;
  margin-top: 12px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.ranking {
  color: #06b6d4;
  font-weight: 600;
  text-shadow: 0 1px 4px rgba(6, 182, 212, 0.5);
}

.divider {
  margin: 0 12px;
  color: #3b82f6;
  font-weight: bold;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 0;
}

/* 分页区域 - P5X风格 */
.pagination-section {
  display: flex;
  justify-content: center;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
  backdrop-filter: blur(20px);
  padding: 24px;
  border-radius: 16px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(6, 182, 212, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(6, 182, 212, 0.3);
  position: relative;
  z-index: 1;
  margin-bottom: 24px;
}

/* Ant Design 分页组件样式覆盖 - P5X风格 */
.pagination-section :deep(.ant-pagination) {
  color: #cbd5e1;
}

.pagination-section :deep(.ant-pagination-item) {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.8) 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.pagination-section :deep(.ant-pagination-item a) {
  color: #cbd5e1;
  font-weight: 500;
  transition: color 0.3s ease;
}

.pagination-section :deep(.ant-pagination-item:hover) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(139, 92, 246, 0.2) 100%);
  border-color: rgba(139, 92, 246, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.pagination-section :deep(.ant-pagination-item:hover a) {
  color: #60a5fa;
}

.pagination-section :deep(.ant-pagination-item-active) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3) 0%, rgba(139, 92, 246, 0.3) 100%);
  border-color: rgba(139, 92, 246, 0.6);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

.pagination-section :deep(.ant-pagination-item-active a) {
  color: #60a5fa;
  font-weight: 600;
  text-shadow: 0 1px 4px rgba(96, 165, 250, 0.5);
}

.pagination-section :deep(.ant-pagination-prev),
.pagination-section :deep(.ant-pagination-next) {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.8) 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.pagination-section :deep(.ant-pagination-prev:hover),
.pagination-section :deep(.ant-pagination-next:hover) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(139, 92, 246, 0.2) 100%);
  border-color: rgba(139, 92, 246, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.pagination-section :deep(.ant-pagination-prev .ant-pagination-item-link),
.pagination-section :deep(.ant-pagination-next .ant-pagination-item-link) {
  color: #cbd5e1;
  background: transparent;
  border: none;
  transition: color 0.3s ease;
}

.pagination-section :deep(.ant-pagination-prev:hover .ant-pagination-item-link),
.pagination-section :deep(.ant-pagination-next:hover .ant-pagination-item-link) {
  color: #60a5fa;
}

.pagination-section :deep(.ant-pagination-options) {
  color: #cbd5e1;
}

.pagination-section :deep(.ant-select) {
  color: #cbd5e1;
}

.pagination-section :deep(.ant-select-selector) {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.8) 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.pagination-section :deep(.ant-select-selector:hover) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(139, 92, 246, 0.2) 100%);
  border-color: rgba(139, 92, 246, 0.5);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.pagination-section :deep(.ant-select-selection-item) {
  color: #cbd5e1;
  font-weight: 500;
}

.pagination-section :deep(.ant-select-arrow) {
  color: #cbd5e1;
}

/* 分页下拉菜单样式 - P5X风格 */
:deep(.ant-select-dropdown) {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

:deep(.ant-select-item) {
  color: #cbd5e1;
  background: transparent;
  transition: all 0.3s ease;
}

:deep(.ant-select-item:hover) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(139, 92, 246, 0.2) 100%);
  color: #60a5fa;
}

:deep(.ant-select-item-option-selected) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3) 0%, rgba(139, 92, 246, 0.3) 100%);
  color: #60a5fa;
  font-weight: 600;
}

/* 外链提示区域 - P5X风格 */
.external-link-notice {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.9) 100%);
  backdrop-filter: blur(15px);
  border-radius: 12px;
  padding: 20px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(251, 191, 36, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(251, 191, 36, 0.3);
  position: relative;
  overflow: hidden;
}

.external-link-notice::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(251, 191, 36, 0.03) 50%, transparent 70%);
  pointer-events: none;
}

.notice-content {
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  z-index: 1;
}

.notice-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.2) 0%, rgba(245, 158, 11, 0.2) 100%);
  border-radius: 12px;
  border: 1px solid rgba(251, 191, 36, 0.4);
  color: #fbbf24;
}

.notice-icon svg {
  width: 24px;
  height: 24px;
}

.notice-text {
  flex: 1;
}

.notice-title {
  font-size: 16px;
  font-weight: 600;
  color: #fbbf24;
  margin: 0 0 4px 0;
  text-shadow: 0 1px 4px rgba(251, 191, 36, 0.5);
  letter-spacing: 0.5px;
}

.notice-desc {
  font-size: 14px;
  color: #cbd5e1;
  margin: 0;
  line-height: 1.5;
  font-weight: 400;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .novels-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .novel-card {
    height: 180px;
  }

  .book-cover {
    width: 135px;
    height: 180px;
  }

  .book-info {
    padding: 12px;
  }

  .book-title {
    font-size: 14px;
  }

  .book-meta {
    font-size: 11px;
  }

  .book-desc {
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
    align-items: stretch;
  }

  .search-inputs {
    flex-direction: column;
    width: 100%;
  }

  .search-input {
    width: 100%;
  }

  .search-buttons {
    justify-content: center;
    margin-top: 16px;
  }
}

@media (max-width: 480px) {
  .novel-page {
    padding: 10px;
  }

  .search-section,
  .novels-container,
  .pagination-section,
  .external-link-notice {
    padding: 15px;
  }

  .search-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .search-btn {
    width: 100%;
  }

  .notice-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .notice-icon {
    width: 40px;
    height: 40px;
  }

  .notice-icon svg {
    width: 20px;
    height: 20px;
  }

  .notice-title {
    font-size: 14px;
  }

  .notice-desc {
    font-size: 13px;
  }
}
</style>
