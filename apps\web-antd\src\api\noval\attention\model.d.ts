import type { PageQuery, BaseEntity } from '#/api/common';

export interface AttentionVO {
  /**
   * 我的id
   */
  id: string | number;

  /**
   * 我关注的id
   */
  ortherId: string | number;

  /**
   * 关注时间
   */
  create_time: string;

}

export interface AttentionForm extends BaseEntity {
  /**
   * 我的id
   */
  id?: string | number;

  /**
   * 我关注的id
   */
  ortherId?: string | number;

    /**
   * 关注时间
   */
  create_time?: string;

}

export interface AttentionQuery extends PageQuery {
  /**
   * 我的id
   */
  id?: string | number;

  /**
   * 我关注的id
   */
  ortherId?: string | number;

    /**
   * 关注时间
   */
  create_time?: string;

  /**
    * 日期范围参数
    */
  params?: any;
}
