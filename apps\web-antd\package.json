{"name": "@vben/web-antd", "version": "1.2.3", "homepage": "https://vben.pro", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "apps/web-antd"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"build": "pnpm vite build", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@tinymce/tinymce-vue": "^6.0.1", "@types/jquery": "^3.5.32", "@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "ant-design-vue": "catalog:", "cropperjs": "^1.6.2", "crypto-js": "^4.2.0", "dayjs": "catalog:", "echarts": "^5.5.1", "jquery": "^3.7.1", "jsencrypt": "^3.3.2", "lodash-es": "^4.17.21", "pinia": "catalog:", "pixi-live2d-display": "^0.4.0", "pixi.js": "6.5.10", "tinymce": "^7.3.0", "unplugin-vue-components": "^0.27.3", "vue": "catalog:", "vue-router": "catalog:"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12"}}