<script setup lang="ts">

import { computed, ref } from 'vue';
import { Page, useVbenModal, type VbenFormProps } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';
import { useUserStore } from '@vben/stores';

import { Avatar, Card, Modal, Popconfirm } from 'ant-design-vue';
import { UserOutlined } from '@ant-design/icons-vue';

import {
  useVbenVxeGrid,
  vxeCheckboxChecked,
  type VxeGridProps
} from '#/adapter/vxe-table';

import { attentionList, attentionRemove } from '#/api/noval/attention';
import type { AttentionForm, AttentionVO } from '#/api/noval/attention/model';
import { findUserInfo } from '#/api/system/user';

import attentionModal from './attention-modal.vue';
import FollowersList from './followers-list.vue';
import PrivateChatDialog from '#/components/Chat/PrivateChatDialog.vue';
import UserProfileModal from '#/components/User/UserProfileModal.vue';
import { columns, querySchema } from './data';

// 用户信息缓存
const userCache = ref<
  Map<number | string, { userAvatar: string; userName: string }>
>(new Map());

// 用户信息弹窗状态
const showUserProfileModal = ref(false);
const selectedUserId = ref<number | string>('');
const selectedUserName = ref('');

// 私聊对话框状态
const showPrivateChatDialog = ref(false);
const chatTargetUserId = ref<number | string>('');
const chatTargetUserName = ref('');

const userStore = useUserStore();
const currentUser = computed(() => userStore.userInfo);

// 默认头像
const defaultAvatars = [
  'https://unpkg.com/@vbenjs/static-source@0.1.7/source/avatar-v1.webp',
  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiMzYjgyZjYiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMTRDOC42ODYyOSAxNCA2IDE2LjY4NjMgNiAyMEg2VjIwSDZIMThIMThWMjBDMTggMTYuNjg2MyAxNS4zMTM3IDE0IDEyIDE0WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cjwvc3ZnPgo=',
];

// 获取有效的头像URL
function getValidAvatarUrl(userAvatar?: string): string {
  if (
    userAvatar &&
    userAvatar.trim() &&
    userAvatar !== 'null' &&
    userAvatar !== 'undefined'
  ) {
    return userAvatar.trim();
  }
  return defaultAvatars[0] || '';
}

// 获取用户信息
async function getUserInfo(userId: number | string) {
  if (userCache.value.has(userId)) {
    return userCache.value.get(userId);
  }

  try {
    const userInfo = await findUserInfo(userId);
    const userData = {
      userAvatar: getValidAvatarUrl(userInfo.user?.avatar),
      userName:
        userInfo.user?.nickName || userInfo.user?.userName || '未知用户',
    };
    userCache.value.set(userId, userData);
    return userData;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      userName: '未知用户',
      userAvatar: getValidAvatarUrl(),
    };
  }
}

// 查看用户信息
function viewUserPosts(userId: number | string, userName: string) {
  selectedUserId.value = userId;
  selectedUserName.value = userName;
  showUserProfileModal.value = true;
}



// 私聊用户
function chatWithUser(userId: number | string, userName: string) {
  chatTargetUserId.value = userId;
  chatTargetUserName.value = userName;
  showPrivateChatDialog.value = true;
}



const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    // trigger: 'row',
  },
  // 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
  // columns: columns(),
  columns,
  height: 600,
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        // 获取关注列表数据，只显示当前用户的关注
        const result = await attentionList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          id: currentUser.value?.userId, // 只查询当前用户的关注
          ...formValues,
        });

        // 为每个关注记录获取用户信息
        if (result.rows && result.rows.length > 0) {
          const enhancedRows = await Promise.all(
            result.rows.map(async (row: AttentionVO) => {
              try {
                // 根据otherId获取用户信息
                const userInfo = await getUserInfo(row.otherId);
                return {
                  ...row,
                  userName: userInfo?.userName || '未知用户',
                  userAvatar: userInfo?.userAvatar || getValidAvatarUrl(),
                };
              } catch (error) {
                console.error('获取用户信息失败:', error);
                return {
                  ...row,
                  userName: '未知用户',
                  userAvatar: getValidAvatarUrl(),
                };
              }
            }),
          );
          result.rows = enhancedRows;
        }

        return result;
      },
    },
  },
  rowConfig: {
    keyField: 'id',
  },
  // 表格全局唯一表示 保存列配置需要用到
  id: 'noval-attention-index',
};

const [BasicTable, followingTableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [AttentionModal] = useVbenModal({
  connectedComponent: attentionModal,
});

async function handleDeleteFollowing(row: Required<AttentionForm>) {
  await attentionRemove(row.id);
  await followingTableApi.query();
}


</script>

<template>
  <Page :auto-content-height="true">
    <div class="p3r-attention-container">
      <!-- P3R风格背景效果 -->
      <div class="absolute inset-0">
        <div class="absolute inset-0 p3r-grid-bg opacity-20"></div>
        <div class="absolute top-0 left-1/4 w-96 h-96 bg-blue-500 rounded-full filter blur-3xl opacity-10 animate-pulse"></div>
        <div class="absolute bottom-0 right-1/4 w-96 h-96 bg-cyan-500 rounded-full filter blur-3xl opacity-10 animate-pulse animation-delay-2000"></div>
      </div>

      <!-- 页面标题 -->
      <div class="relative z-10 mb-8">
        <div class="flex items-center gap-6 mb-6">
          <div class="relative">
            <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-lg flex items-center justify-center shadow-2xl border border-cyan-400/50 backdrop-blur-sm">
              <UserOutlined class="text-white text-3xl" />
              <div class="absolute inset-0 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-lg blur opacity-50 -z-10"></div>
            </div>
          </div>
          <div class="flex-1">
            <h1 class="text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 mb-2 p3r-title">
              关注管理
            </h1>
            <p class="text-cyan-300 text-lg font-light tracking-wider">
              // 关注与粉丝管理界面
            </p>
          </div>
        </div>
      </div>

      <!-- 左右分栏布局 -->
      <div class="relative z-10 flex gap-6">
        <!-- 左侧：我的关注 (占3/4宽度) -->
        <div class="following-section flex-1">
          <div class="p3r-card">
            <div class="p3r-card-header">
              <h2 class="p3r-card-title">我的关注</h2>
            </div>

            <div class="p3r-table-container">
              <BasicTable :show-header-tools="false" :show-table-title="false" class="p3r-table">
                <!-- 用户头像列 -->
                <template #userAvatar="{ row }">
                  <Avatar
                    :src="row.userAvatar"
                    :size="40"
                    class="p3r-avatar cursor-pointer transition-all hover:scale-110"
                    @click="viewUserPosts(row.otherId, row.userName)"
                    @error="
                      () => {
                        console.log('头像加载失败:', row.userAvatar);
                      }
                    "
                  >
                    <template #icon>
                      <UserOutlined />
                    </template>
                  </Avatar>
                </template>

                <!-- 用户名列 -->
                <template #userName="{ row }">
                  <span
                    class="p3r-username cursor-pointer"
                    @click="viewUserPosts(row.otherId, row.userName)"
                  >
                    {{ row.userName }}
                  </span>
                </template>

                <template #action="{ row }">
                  <div class="flex gap-2">
                    <a-button
                      type="primary"
                      size="small"
                      @click.stop="chatWithUser(row.otherId, row.userName)"
                      class="p3r-button p3r-button-primary"
                    >
                      私聊
                    </a-button>
                    <Popconfirm
                      :get-popup-container="getVxePopupContainer"
                      placement="left"
                      title="确认取消关注？"
                      @confirm="handleDeleteFollowing(row)"
                    >
                      <a-button danger size="small" @click.stop="" class="p3r-button p3r-button-danger">
                        取消关注
                      </a-button>
                    </Popconfirm>
                  </div>
                </template>
              </BasicTable>
            </div>
          </div>
        </div>

        <!-- 右侧：我的粉丝 (占1/4宽度) -->
        <div class="followers-section w-80">
          <div class="p3r-card">
            <div class="p3r-card-header">
              <h2 class="p3r-card-title">我的粉丝</h2>
            </div>
            <div class="p3r-card-content">
              <FollowersList
                :current-user-id="currentUser?.userId"
                @view-user-posts="viewUserPosts"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户信息弹窗 -->
    <UserProfileModal
      v-model:visible="showUserProfileModal"
      :user-id="selectedUserId"
      :user-name="selectedUserName"
      @start-chat="chatWithUser"
      @close="showUserProfileModal = false"
    />

    <AttentionModal @reload="followingTableApi.query()" />

    <!-- 私聊对话框 -->
    <PrivateChatDialog
      v-model:visible="showPrivateChatDialog"
      :target-user-id="chatTargetUserId"
      :target-user-name="chatTargetUserName"
      @close="showPrivateChatDialog = false"
    />
  </Page>
</template>

<style scoped>
/* P5R风格样式 - 蓝色主题 */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

.p3r-attention-container {
  padding: 24px;
  background: linear-gradient(135deg, #0a0a0a 0%, #0a0a1a 25%, #050a2a 50%, #0a0a1a 75%, #0a0a0a 100%);
  min-height: 100vh;
  font-family: 'Rajdhani', sans-serif;
  position: relative;
  overflow-x: hidden;
}

.p3r-attention-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
    linear-gradient(45deg, transparent 48%, rgba(59, 130, 246, 0.03) 49%, rgba(59, 130, 246, 0.03) 51%, transparent 52%),
    linear-gradient(-45deg, transparent 48%, rgba(0, 0, 0, 0.1) 49%, rgba(0, 0, 0, 0.1) 51%, transparent 52%);
  background-size: 100% 100%, 100% 100%, 60px 60px, 60px 60px;
  pointer-events: none;
  z-index: 0;
  animation: p5rBgShift 20s infinite linear;
}

@keyframes p5rBgShift {
  0% { background-position: 0% 0%, 0% 0%, 0px 0px, 0px 0px; }
  100% { background-position: 0% 0%, 0% 0%, 60px 60px, -60px -60px; }
}

/* 网格背景 */
.p3r-grid-bg {
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* P5R标题 */
.p3r-title {
  font-family: 'Orbitron', sans-serif;
  text-shadow:
    0 0 10px rgba(59, 130, 246, 0.8),
    0 2px 4px rgba(0, 0, 0, 0.8),
    0 4px 8px rgba(59, 130, 246, 0.4);
  letter-spacing: 2px;
  font-weight: 900;
  text-transform: uppercase;
  position: relative;
}

.p3r-title::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, transparent);
  animation: p5rTitleLine 2s infinite alternate;
}

@keyframes p5rTitleLine {
  0% { width: 0%; }
  100% { width: 100%; }
}

/* P5R卡片 */
.p3r-card {
  background: rgba(255, 255, 255, 0.02);
  border: 2px solid #3b82f6;
  border-radius: 0;
  backdrop-filter: blur(20px);
  box-shadow:
    0 0 30px rgba(59, 130, 246, 0.4),
    0 8px 32px rgba(0, 0, 0, 0.6),
    inset 0 1px 0 rgba(59, 130, 246, 0.2);
  position: relative;
  z-index: 1;
  overflow: hidden;
  clip-path: polygon(0 0, calc(100% - 15px) 0, 100% 15px, 100% 100%, 15px 100%, 0 calc(100% - 15px));
}

.p3r-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
  animation: p5rShine 6s infinite;
}

@keyframes p5rShine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.p3r-card-header {
  background: linear-gradient(135deg, #1a0a0a 0%, #050a2a 25%, #3b82f6 50%, #050a2a 75%, #1a0a0a 100%);
  border-bottom: 2px solid #3b82f6;
  border-radius: 0;
  padding: 32px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 2;
  backdrop-filter: blur(10px);
}

.p3r-card-title {
  color: #ffffff;
  font-size: 24px;
  font-weight: 900;
  margin: 0;
  font-family: 'Orbitron', sans-serif;
  text-shadow:
    0 0 10px rgba(59, 130, 246, 0.8),
    0 2px 4px rgba(0, 0, 0, 0.8),
    0 4px 8px rgba(59, 130, 246, 0.4);
  letter-spacing: 2px;
  text-transform: uppercase;
}

.p3r-card-content {
  padding: 24px;
  background: transparent;
}

/* P5R按钮 */
.p3r-button {
  border-radius: 0;
  font-weight: 700;
  letter-spacing: 1px;
  text-transform: uppercase;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  clip-path: polygon(0 0, calc(100% - 8px) 0, 100% 8px, 100% 100%, 8px 100%, 0 calc(100% - 8px));
}

.p3r-button-primary {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  border: 2px solid #3b82f6;
  color: white;
  box-shadow:
    0 0 20px rgba(59, 130, 246, 0.4),
    0 4px 15px rgba(30, 64, 175, 0.2);
}

.p3r-button-primary:hover {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 0 30px rgba(59, 130, 246, 0.6),
    0 8px 25px rgba(30, 64, 175, 0.3);
}

.p3r-button-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border: 2px solid #ef4444;
  color: white;
  box-shadow:
    0 0 20px rgba(239, 68, 68, 0.4),
    0 4px 15px rgba(220, 38, 38, 0.2);
}

.p3r-button-danger:hover {
  background: linear-gradient(135deg, #f87171, #ef4444);
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 0 30px rgba(239, 68, 68, 0.6),
    0 8px 25px rgba(220, 38, 38, 0.3);
}

/* P3R表格 */
.p3r-table-container {
  background: transparent;
}

.p3r-table :deep(.ant-table) {
  background: transparent;
  color: #e2e8f0;
}

.p3r-table :deep(.ant-table-thead > tr > th) {
  background: rgba(30, 41, 59, 0.8);
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  color: #3b82f6;
  font-weight: 600;
  font-family: 'Orbitron', sans-serif;
}

.p3r-table :deep(.ant-table-tbody > tr) {
  background: rgba(30, 41, 59, 0.5);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  transition: all 0.3s ease;
}

.p3r-table :deep(.ant-table-tbody > tr:hover) {
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.p3r-table :deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  color: #e2e8f0;
}

/* P3R头像 */
.p3r-avatar {
  border: 2px solid rgba(59, 130, 246, 0.5);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.3);
}

.p3r-avatar:hover {
  border-color: #3b82f6;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

/* P3R用户名 */
.p3r-username {
  color: #3b82f6;
  font-weight: 600;
  transition: all 0.3s ease;
  text-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
}

.p3r-username:hover {
  color: #60a5fa;
  text-shadow: 0 0 10px rgba(96, 165, 250, 0.5);
  text-decoration: underline;
}

/* 动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.p3r-card {
  animation: fadeInUp 0.6s ease-out;
}

.p3r-card:nth-child(2) {
  animation-delay: 0.2s;
}
</style>
