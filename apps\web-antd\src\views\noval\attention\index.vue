<script setup lang="ts">

import { computed, ref, h } from 'vue';
import { useRouter } from 'vue-router';
import { Page, useVbenModal, type VbenFormProps } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';
import { useUserStore } from '@vben/stores';
import { formatDateTime } from '@vben/utils';

import { Avatar, Card, List, message, Modal, Popconfirm, Button, Space } from 'ant-design-vue';
import { UserOutlined, EyeOutlined, MessageOutlined, PlusOutlined } from '@ant-design/icons-vue';

import {
  useVbenVxeGrid,
  vxeCheckboxChecked,
  type VxeGridProps
} from '#/adapter/vxe-table';

import { attentionList, attentionRemove, attentionAdd } from '#/api/noval/attention';
import type { AttentionForm, AttentionVO } from '#/api/noval/attention/model';
import { findUserInfo } from '#/api/system/user';
import { commentList } from '#/api/noval/comment';
import type { CommentVO } from '#/api/noval/comment/model';
import { newsAdd } from '#/api/noval/news';

import attentionModal from './attention-modal.vue';
import FollowersList from './followers-list.vue';
import PrivateChatDialog from '#/components/Chat/PrivateChatDialog.vue';
import { columns, querySchema } from './data';

// 用户信息缓存
const userCache = ref<
  Map<number | string, { userAvatar: string; userName: string }>
>(new Map());

// 用户帖子弹窗状态
const showUserPostsModal = ref(false);
const selectedUserId = ref<number | string>('');
const selectedUserName = ref('');
const userPosts = ref<CommentVO[]>([]);

// 私聊对话框状态
const showPrivateChatDialog = ref(false);
const chatTargetUserId = ref<number | string>('');
const chatTargetUserName = ref('');

const userStore = useUserStore();
const router = useRouter();
const currentUser = computed(() => userStore.userInfo);

// 默认头像
const defaultAvatars = [
  'https://unpkg.com/@vbenjs/static-source@0.1.7/source/avatar-v1.webp',
  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiMzYjgyZjYiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMTRDOC42ODYyOSAxNCA2IDE2LjY4NjMgNiAyMEg2VjIwSDZIMThIMThWMjBDMTggMTYuNjg2MyAxNS4zMTM3IDE0IDEyIDE0WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cjwvc3ZnPgo=',
];

// 获取有效的头像URL
function getValidAvatarUrl(userAvatar?: string): string {
  if (
    userAvatar &&
    userAvatar.trim() &&
    userAvatar !== 'null' &&
    userAvatar !== 'undefined'
  ) {
    return userAvatar.trim();
  }
  return defaultAvatars[0] || '';
}

// 获取用户信息
async function getUserInfo(userId: number | string) {
  if (userCache.value.has(userId)) {
    return userCache.value.get(userId);
  }

  try {
    const userInfo = await findUserInfo(userId);
    const userData = {
      userAvatar: getValidAvatarUrl(userInfo.user?.avatar),
      userName:
        userInfo.user?.nickName || userInfo.user?.userName || '未知用户',
    };
    userCache.value.set(userId, userData);
    return userData;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      userName: '未知用户',
      userAvatar: getValidAvatarUrl(),
    };
  }
}

// 查看用户帖子
async function viewUserPosts(userId: number | string, userName: string) {
  try {
    selectedUserId.value = userId;
    selectedUserName.value = userName;

    // 获取该用户的所有帖子
    const response = await commentList({
      pageNum: 1,
      pageSize: 20,
      commerntType: 0, // 只获取帖子
      myId: userId, // 根据用户ID筛选
    });

    userPosts.value = response.rows || [];
    showUserPostsModal.value = true;
  } catch (error) {
    console.error('获取用户帖子失败:', error);
    message.error('获取用户帖子失败');
  }
}

// 获取帖子标题
function getPostTitle(content: string) {
  const lines = content.split('\n');
  return lines[0] || '无标题';
}

// 获取帖子预览内容
function getPostPreview(content: string) {
  const lines = content.split('\n');
  const contentLines = lines.slice(1).join('\n').trim();
  return contentLines.length > 100
    ? `${contentLines.slice(0, 100)}...`
    : contentLines;
}

// 跳转到帖子详情
function viewPostDetail(postId: number | string) {
  // 关闭用户帖子弹窗
  showUserPostsModal.value = false;

  // 跳转到评论区页面，并传递帖子ID作为查询参数
  router.push({
    path: '/CommentForYh/comment-section',
    query: {
      postId: postId.toString(),
      view: 'detail',
    },
  });
}

// 关注用户
async function followUser(userId: number | string) {
  try {
    await attentionAdd({
      id: currentUser.value?.userId,
      otherId: userId,
    });
    message.success('关注成功');
    // 刷新关注列表
    await followingTableApi.query();
  } catch (error) {
    console.error('关注失败:', error);
    message.error('关注失败');
  }
}

// 私聊用户
function chatWithUser(userId: number | string, userName: string) {
  chatTargetUserId.value = userId;
  chatTargetUserName.value = userName;
  showPrivateChatDialog.value = true;
}

// 检查是否已关注用户
async function checkIfFollowing(userId: number | string): Promise<boolean> {
  try {
    const result = await attentionList({
      pageNum: 1,
      pageSize: 1,
      id: currentUser.value?.userId,
      otherId: userId,
    });
    return result.rows && result.rows.length > 0;
  } catch (error) {
    console.error('检查关注状态失败:', error);
    return false;
  }
}

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    // trigger: 'row',
  },
  // 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
  // columns: columns(),
  columns,
  height: 600,
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        // 获取关注列表数据，只显示当前用户的关注
        const result = await attentionList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          id: currentUser.value?.userId, // 只查询当前用户的关注
          ...formValues,
        });

        // 为每个关注记录获取用户信息
        if (result.rows && result.rows.length > 0) {
          const enhancedRows = await Promise.all(
            result.rows.map(async (row: AttentionVO) => {
              try {
                // 根据otherId获取用户信息
                const userInfo = await getUserInfo(row.otherId);
                return {
                  ...row,
                  userName: userInfo?.userName || '未知用户',
                  userAvatar: userInfo?.userAvatar || getValidAvatarUrl(),
                };
              } catch (error) {
                console.error('获取用户信息失败:', error);
                return {
                  ...row,
                  userName: '未知用户',
                  userAvatar: getValidAvatarUrl(),
                };
              }
            }),
          );
          result.rows = enhancedRows;
        }

        return result;
      },
    },
  },
  rowConfig: {
    keyField: 'id',
  },
  // 表格全局唯一表示 保存列配置需要用到
  id: 'noval-attention-index',
};

const [BasicTable, followingTableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [AttentionModal] = useVbenModal({
  connectedComponent: attentionModal,
});

async function handleDeleteFollowing(row: Required<AttentionForm>) {
  await attentionRemove(row.id);
  await followingTableApi.query();
}

function handleMultiDeleteFollowing() {
  const rows = followingTableApi.grid.getCheckboxRecords();
  const ids = rows.map((row: Required<AttentionForm>) => row.id);
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认取消关注选中的${ids.length}个用户吗？`,
    onOk: async () => {
      await attentionRemove(ids);
      await followingTableApi.query();
    },
  });
}
</script>

<template>
  <Page :auto-content-height="true">
    <div class="attention-page-container">
      <!-- 左右分栏布局 -->
      <div class="flex gap-6">
        <!-- 左侧：我的关注 (占3/4宽度) -->
        <div class="following-section flex-1">
          <Card title="我的关注" class="h-fit">
            <template #extra>
              <a-button
                :disabled="!vxeCheckboxChecked(followingTableApi)"
                danger
                size="small"
                v-access:code="['noval:attention:remove']"
                @click="handleMultiDeleteFollowing"
              >
                批量取消关注
              </a-button>
            </template>

            <BasicTable :show-header-tools="false" :show-table-title="false">
              <!-- 用户头像列 -->
              <template #userAvatar="{ row }">
                <Avatar
                  :src="row.userAvatar"
                  :size="40"
                  class="cursor-pointer transition-opacity hover:opacity-80"
                  @click="viewUserPosts(row.otherId, row.userName)"
                  @error="
                    () => {
                      console.log('头像加载失败:', row.userAvatar);
                    }
                  "
                >
                  <template #icon>
                    <UserOutlined />
                  </template>
                </Avatar>
              </template>

              <!-- 用户名列 -->
              <template #userName="{ row }">
                <span
                  class="cursor-pointer text-blue-600 hover:text-blue-800 hover:underline"
                  @click="viewUserPosts(row.otherId, row.userName)"
                >
                  {{ row.userName }}
                </span>
              </template>

              <template #action="{ row }">
                <Popconfirm
                  :get-popup-container="getVxePopupContainer"
                  placement="left"
                  title="确认取消关注？"
                  @confirm="handleDeleteFollowing(row)"
                >
                  <ghost-button danger size="small" @click.stop="">
                    取消关注
                  </ghost-button>
                </Popconfirm>
              </template>
            </BasicTable>
          </Card>
        </div>

        <!-- 右侧：我的粉丝 (占1/4宽度) -->
        <div class="followers-section w-80">
          <Card title="我的粉丝" class="h-fit">
            <FollowersList
              :current-user-id="currentUser?.userId"
              @view-user-posts="viewUserPosts"
            />
          </Card>
        </div>
      </div>
    </div>

    <!-- 用户帖子弹窗 -->
    <Modal
      v-model:open="showUserPostsModal"
      :title="`${selectedUserName} 的帖子`"
      width="800px"
      :footer="null"
      @cancel="showUserPostsModal = false"
    >
      <div class="user-posts-modal">
        <!-- 用户操作按钮 -->
        <div class="user-actions mb-4 flex justify-end">
          <Space>
            <Button
              type="primary"
              :icon="h(PlusOutlined)"
              @click="followUser(selectedUserId)"
            >
              关注
            </Button>
            <Button
              type="default"
              :icon="h(MessageOutlined)"
              @click="chatWithUser(selectedUserId, selectedUserName)"
            >
              私聊
            </Button>
          </Space>
        </div>
        <div v-if="userPosts.length > 0" class="user-posts-list">
          <List :data-source="userPosts" item-layout="vertical" size="small">
            <template #renderItem="{ item: userPost }">
              <List.Item
                class="user-post-item cursor-pointer rounded p-3 transition-colors hover:bg-gray-50"
                @click="viewPostDetail(userPost.commerntId)"
              >
                <div class="user-post-content">
                  <h4
                    class="user-post-title mb-2 text-lg font-medium text-blue-600 hover:text-blue-800"
                  >
                    {{ getPostTitle(userPost.content || '') }}
                  </h4>
                  <div class="user-post-preview mb-2 text-gray-600">
                    {{ getPostPreview(userPost.content || '') }}
                  </div>
                  <div
                    class="user-post-meta flex items-center justify-between text-sm text-gray-500"
                  >
                    <span class="user-post-time">
                      {{ formatDateTime(userPost.createTime) }}
                    </span>
                    <span class="user-post-stats flex items-center gap-4">
                      <span class="flex items-center gap-1">
                        <EyeOutlined /> {{ userPost.look || 0 }}
                      </span>
                      <span class="flex items-center gap-1">
                        <MessageOutlined /> 回复数
                      </span>
                    </span>
                  </div>
                </div>
              </List.Item>
            </template>
          </List>
        </div>
        <div v-else class="user-posts-empty">
          <div class="py-8 text-center">
            <div class="mb-4 text-gray-400">
              <MessageOutlined class="text-6xl" />
            </div>
            <div class="mb-2 text-lg text-gray-500">该用户还没有发表帖子</div>
            <div class="text-sm text-gray-400">期待TA的第一个帖子吧！</div>
          </div>
        </div>
      </div>
    </Modal>

    <AttentionModal @reload="followingTableApi.query()" />

    <!-- 私聊对话框 -->
    <PrivateChatDialog
      v-model:visible="showPrivateChatDialog"
      :target-user-id="chatTargetUserId"
      :target-user-name="chatTargetUserName"
      @close="showPrivateChatDialog = false"
    />
  </Page>
</template>
