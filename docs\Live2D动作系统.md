# Live2D丰富动作系统

## 🎭 动作系统概览

我为Live2D角色阿留设计了一套完整的动作表演系统，包含：

### 1. **面部表情系统** (12种表情)
- **基础表情**: 开心、生气、惊讶、眨眼、微笑
- **进阶表情**: 害羞、困惑、得意、无奈、调皮、思考、撒娇

### 2. **身体动作系统** (7种动作)
- **头部动作**: 点头、摇头、歪头、抬头、低头
- **视线动作**: 左看、右看

### 3. **复合动作系统** (4种组合)
- **害羞点头**: 害羞表情 + 点头动作
- **调皮歪头**: 调皮表情 + 歪头动作
- **思考抬头**: 思考表情 + 抬头动作
- **得意左看**: 得意表情 + 左看动作

### 4. **特殊表情系统** (3种特效)
- **眼部特效**: 圈圈眼、爱心眼、黑化

### 5. **服装系统** (7套服装)
- **配饰**: JK包、戴帽子、手柄
- **服装**: 脱外套、裙子
- **发型**: 马尾L隐藏、马尾R隐藏

## 🎨 动作详细说明

### 面部表情参数
```typescript
const faceExpressions = [
  { name: '害羞', params: { 
    'ParamMouthForm': 0.3, 
    'ParamBrowLY': 0.2, 
    'ParamBrowRY': 0.2, 
    'ParamEyeLOpen': 0.7, 
    'ParamEyeROpen': 0.7 
  }},
  { name: '调皮', params: { 
    'ParamMouthForm': 0.5, 
    'ParamEyeLOpen': 0.3, 
    'ParamEyeROpen': 1.0 
  }},
  // ... 更多表情
]
```

### 身体动作参数
```typescript
const bodyActions = [
  { 
    name: '歪头', 
    params: { 'ParamAngleZ': 15 },
    duration: 1500,
    description: '可爱地歪着头思考'
  },
  { 
    name: '点头', 
    params: { 'ParamAngleX': 5 },
    duration: 800,
    description: '轻轻点头表示同意'
  },
  // ... 更多动作
]
```

### 复合动作组合
```typescript
const complexActions = [
  {
    name: '害羞点头',
    faceExpression: { name: '害羞', params: {...} },
    bodyAction: { name: '点头', params: {...} },
    message: '嗯嗯~人家知道了啦~'
  },
  // ... 更多组合
]
```

## 🎯 动作触发机制

### 自动模式 (每30秒)
```typescript
const startAutoMode = () => {
  setInterval(() => {
    const random = Math.random()
    
    if (random < 0.5) {
      // 50% 概率播放各种动作
      playRandomAction()
    } else if (random < 0.8) {
      // 30% 概率说随机台词
      sayRandomMessage()
    }
    // 20% 概率什么都不做，保持自然
  }, 30000)
}
```

### 随机动作分配
```typescript
const playRandomAction = () => {
  const random = Math.random()
  
  if (random < 0.3) {
    // 30% 面部表情
    playFaceExpression(randomFaceExpression)
  } else if (random < 0.5) {
    // 20% 身体动作
    playBodyAction(randomBodyAction)
  } else if (random < 0.7) {
    // 20% 复合动作
    playComplexAction(randomComplexAction)
  } else if (random < 0.9) {
    // 20% 特殊表情
    playSpecialExpression(randomSpecialExpression)
  } else {
    // 10% 换装
    changeOutfit()
  }
}
```

## 💬 动作台词系统

### 表情台词
```typescript
expression: [
  '这个表情怎么样？',
  '我还有很多表情呢~',
  '看我的新表情！',
  '表情管理大师就是我~',
  '这样的我可爱吗？',
  '表情包又增加了呢！'
]
```

### 动作台词
```typescript
action: [
  '让我做个动作给你看~',
  '我会很多有趣的动作哦！',
  '这个动作帅气吗？',
  '看我的特技表演！',
  '动作演示开始！',
  '我的动作库很丰富呢~'
]
```

### 复合动作专属台词
- **害羞点头**: "嗯嗯~人家知道了啦~"
- **调皮歪头**: "嘿嘿~这样看起来怎么样？"
- **思考抬头**: "让我想想看..."
- **得意左看**: "哼哼~我很厉害吧！"

## 🎨 视觉反馈系统

### 消息气泡颜色
| 动作类型 | 气泡颜色 | 用途 |
|----------|----------|------|
| `touch` | 粉紫渐变 | 触摸反应 |
| `expression` | 蓝色渐变 | 表情提示 |
| `action` | 绿色渐变 | 动作说明 |
| `praise` | 蓝紫渐变 | 夸奖反应 |
| `normal` | 黑色半透明 | 普通对话 |

### 动作持续时间
- **短动作**: 800ms (点头)
- **中等动作**: 1000-1200ms (摇头、抬头、低头)
- **长动作**: 1500ms (歪头)
- **自动重置**: 动作完成后自动恢复默认姿态

## 🔧 技术实现

### 参数控制系统
```typescript
// 面部表情控制
'ParamMouthForm': 嘴型变化 (-1.0 到 1.0)
'ParamBrowLY': 左眉毛 (-1.0 到 1.0)
'ParamBrowRY': 右眉毛 (-1.0 到 1.0)
'ParamEyeLOpen': 左眼开合 (0.0 到 1.0)
'ParamEyeROpen': 右眼开合 (0.0 到 1.0)

// 身体动作控制
'ParamAngleX': 头部上下倾斜 (-30 到 30)
'ParamAngleY': 头部左右转动 (-30 到 30)
'ParamAngleZ': 头部左右倾斜 (-30 到 30)
```

### 动作播放流程
1. **重置参数**: 清除之前的动作状态
2. **应用参数**: 设置新的动作参数
3. **显示消息**: 展示对应的动作说明
4. **自动恢复**: 指定时间后恢复默认状态

### 复合动作时序
1. **0ms**: 播放面部表情
2. **200ms**: 播放身体动作 (稍微延迟，更自然)
3. **400ms**: 显示专属台词

## 🎊 动作表演效果

### 日常互动
- **点击触摸**: 随机触摸反应台词
- **自动表演**: 每30秒随机动作展示
- **丰富变化**: 12种表情 × 7种动作 × 4种组合 = 多样化表演

### 情感表达
- **害羞**: 眼睛微闭 + 轻微笑容
- **调皮**: 单眼眨眼 + 嘴角上扬
- **思考**: 眉毛不对称 + 抬头姿态
- **得意**: 眼睛明亮 + 左看姿态

### 动作连贯性
- **平滑过渡**: 参数渐变，避免突兀
- **时间控制**: 不同动作有不同持续时间
- **自然恢复**: 动作结束后平滑回到默认状态

## 🚀 未来扩展

### 可添加的动作类型
- **手部动作**: 挥手、比心、鼓掌
- **身体动作**: 前倾、后仰、左右摆动
- **互动动作**: 指向用户、做鬼脸、飞吻
- **情境动作**: 睡觉、吃东西、学习

### 可添加的表情
- **更多情绪**: 惊喜、疑惑、兴奋、疲惫
- **夸张表情**: 大笑、大哭、震惊、愤怒
- **细微表情**: 眯眼、撅嘴、皱眉、微笑

现在阿留拥有了丰富的动作表演能力，能够进行各种可爱的表情和动作展示，让互动更加生动有趣！🎭✨
