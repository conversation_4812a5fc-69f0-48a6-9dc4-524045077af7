<template>
  <Avatar 
    :src="currentSrc" 
    :size="size"
    @error="handleError"
  >
    <template #icon>
      <UserOutlined />
    </template>
  </Avatar>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { Avatar } from 'ant-design-vue';
import { UserOutlined } from '@ant-design/icons-vue';

interface Props {
  src?: string;
  size?: number | 'large' | 'small' | 'default';
}

const props = withDefaults(defineProps<Props>(), {
  size: 40,
});

// 默认头像列表
const defaultAvatars = [
  'https://unpkg.com/@vbenjs/static-source@0.1.7/source/avatar-v1.webp',
  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiMzYjgyZjYiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA8IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMTRDOC42ODYyOSAxNCA2IDE2LjY4NjMgNiAyMEg2VjIwSDZIMThIMThWMjBDMTggMTYuNjg2MyAxNS4zMTM3IDE0IDEyIDE0WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cjwvc3ZnPgo=',
];

const currentSrc = ref<string>('');
const fallbackIndex = ref(0);

// 获取有效的头像URL
function getValidAvatarUrl(userAvatar?: string): string {
  // 如果用户头像存在且有效，返回用户头像
  if (userAvatar && userAvatar.trim() && userAvatar !== 'null' && userAvatar !== 'undefined') {
    return userAvatar.trim();
  }
  // 否则返回默认头像
  return defaultAvatars[fallbackIndex.value] || defaultAvatars[0];
}

// 处理图片加载错误
function handleError() {
  console.log('头像加载失败，尝试备用头像:', currentSrc.value);
  
  // 如果当前使用的是用户头像，切换到默认头像
  if (currentSrc.value === props.src) {
    fallbackIndex.value = 0;
    currentSrc.value = defaultAvatars[0];
    return;
  }
  
  // 如果当前使用的是默认头像，尝试下一个备用头像
  if (fallbackIndex.value < defaultAvatars.length - 1) {
    fallbackIndex.value++;
    currentSrc.value = defaultAvatars[fallbackIndex.value];
  } else {
    // 所有备用头像都失败了，使用图标
    currentSrc.value = '';
  }
}

// 初始化头像
function initAvatar() {
  fallbackIndex.value = 0;
  currentSrc.value = getValidAvatarUrl(props.src);
}

// 监听src变化
watch(() => props.src, () => {
  initAvatar();
}, { immediate: true });

onMounted(() => {
  initAvatar();
});
</script>
