# Live2D 文件夹上传功能使用说明

## 🎭 功能概述

Live2D 管理系统现在支持直接上传整个模型文件夹，自动按用户ID分类存储，保持完整的文件夹结构。

## 📁 上传路径规则

```
用户ID/live2d/模型文件夹名/
```

**示例：**
- 用户ID：123
- 上传文件夹：ariu
- 最终路径：`123/live2d/ariu/`

## 🚀 使用方法

### 1. 进入管理页面
访问 Live2D 管理页面，点击"添加"按钮

### 2. 填写基本信息
- **Live2d名字**：输入模型名称（如：阿留）
- **Live2d类型**：选择模型类型

### 3. 上传文件夹
在"Live2d文件夹上传"区域：

**方法一：拖拽上传**
- 直接将整个模型文件夹（如 `ariu` 文件夹）拖拽到上传区域

**方法二：点击选择**
- 点击上传区域
- 在文件选择器中选择整个文件夹
- 确保选择的是包含所有模型文件的完整文件夹

### 4. 自动处理
系统会自动：
- 保持原始文件夹结构
- 上传所有相关文件
- 识别主模型文件（.model3.json）
- 设置正确的模型路径

### 5. 提交保存
点击确认按钮完成添加

## 📋 支持的文件格式

- `.model3.json` - Cubism 4.0 主模型文件
- `.model.json` - Cubism 2.1/3.0 主模型文件  
- `.moc3` - 模型数据文件
- `.png` - 纹理图片文件
- `.exp3.json` - 表情文件
- `.motion3.json` - 动作文件
- `.physics3.json` - 物理文件
- `.cdi3.json` - 显示信息文件

## 📂 文件夹结构示例

```
ariu/                          # 模型文件夹
├── ariu.model3.json          # 主模型文件 ⭐
├── ariu.moc3                 # 模型数据
├── ariu.physics3.json        # 物理配置
├── ariu.cdi3.json           # 显示信息
├── ariu.4096/               # 纹理文件夹
│   ├── texture_00.png
│   ├── texture_01.png
│   └── ...
├── expressions/             # 表情文件夹
│   ├── 开心.exp3.json
│   ├── 生气.exp3.json
│   └── ...
└── motions/                # 动作文件夹
    ├── idle.motion3.json
    ├── tap_head.motion3.json
    └── ...
```

## ⚡ 特性优势

1. **完整性**：保持原始文件夹结构，确保模型完整性
2. **自动化**：自动识别主模型文件，无需手动设置路径
3. **隔离性**：按用户ID分类，避免文件冲突
4. **便捷性**：支持拖拽上传，操作简单直观
5. **兼容性**：支持所有Live2D相关文件格式

## 🔧 技术实现

- 使用 `webkitRelativePath` 保持文件夹结构
- 逐个上传文件到指定路径
- 自动识别主模型文件
- 实时显示上传进度

## 💡 使用建议

1. **文件夹命名**：使用英文名称，避免特殊字符
2. **文件大小**：单个文件建议不超过10MB
3. **文件数量**：单次上传建议不超过100个文件
4. **网络环境**：确保网络稳定，避免上传中断

## 🎯 应用场景

- Live2D 模型管理
- 用户自定义看板娘
- 多用户模型隔离
- 模型资源备份
