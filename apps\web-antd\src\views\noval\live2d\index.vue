<script setup lang="ts">
import type { Recordable } from '@vben/types';

import { ref } from 'vue';

import { Page, useVbenModal, type VbenFormProps } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';

import { Modal, Popconfirm, Space, message } from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  useVbenVxeGrid,
  vxeCheckboxChecked,
  type VxeGridProps
} from '#/adapter/vxe-table';

import {
  live2dExport,
  live2dList,
  live2dList2,
  live2dListByName,
  live2dRemove,
  live2dRemoveByName,
  live2dRemoveByNames,
} from '#/api/noval/live2d';
import type { Live2dForm } from '#/api/noval/live2d/model';
import { commonDownloadExcel } from '#/utils/file/download';
import {
  deleteRecordWithFiles,
  deleteRecordsWithFiles,
  deleteLive2DByName,
  extractLive2DUrls
} from '#/utils/file-delete-helper';

// 导入测试工具（开发环境）
if (import.meta.env.DEV) {
  import('#/utils/live2d-delete-test').then(module => {
    console.log('🔧 Live2D删除测试工具已加载');
  }).catch(error => {
    console.warn('⚠️ Live2D删除测试工具加载失败:', error);
  });
}

import live2dModal from './live2d-modal.vue';
import { columns, querySchema } from './data';

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  // 处理区间选择器RangePicker时间格式 将一个字段映射为两个字段 搜索/导出会用到
  // 不需要直接删除
  // fieldMappingTime: [
  //  [
  //    'createTime',
  //    ['params[beginTime]', 'params[endTime]'],
  //    ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
  //  ],
  // ],
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    // trigger: 'row',
  },
  // 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
  // columns: columns(),
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        console.log('🔍 Live2D查询开始:', { page, formValues });

        // 使用原来的list API，设置大的pageSize获取所有数据
        const result = await live2dList({
          pageNum: 1,
          pageSize: 10000, // ✅ 设置大的页面大小获取所有数据
          ...formValues,
        });

        console.log('📊 API返回结果:', result);
        console.log('📊 原始数据总数:', result.total);
        console.log('📋 原始数据条数:', result.rows?.length || 0);

        // 如果没有数据，返回空结果
        if (!result.rows || result.rows.length === 0) {
          console.log('⚠️ 没有数据返回');
          return {
            total: 0,
            rows: []
          };
        }

        // 对结果进行去重处理，相同名字的只显示一个，避免循环引用
        const uniqueRecords = new Map();
        const processedRecords = [];

        result.rows.forEach(record => {
          const name = record.name;
          if (uniqueRecords.has(name)) {
            // 如果已存在同名记录，只增加计数，不保存引用
            const existingRecord = uniqueRecords.get(name);
            existingRecord.recordCount = (existingRecord.recordCount || 1) + 1;
          } else {
            // 新记录，创建简化的记录对象，避免循环引用
            const simplifiedRecord = {
              id: record.id,
              name: record.name,
              type: record.type,
              icon: record.icon,
              originalName: record.originalName,
              createTime: record.createTime,
              recordCount: 1
            };
            uniqueRecords.set(name, simplifiedRecord);
            processedRecords.push(simplifiedRecord);
          }
        });

        console.log('📋 合并处理详情:');
        console.log('- 原始数据条数:', result.rows.length);
        console.log('- 合并后条数:', processedRecords.length);
        console.log('- 合并后数据:', processedRecords.map(r => ({
          name: r.name,
          type: r.type,
          recordCount: r.recordCount
        })));

        console.log('📊 合并后数据总数:', processedRecords.length);

        // 手动分页处理
        const startIndex = (page.currentPage - 1) * page.pageSize;
        const endIndex = startIndex + page.pageSize;
        const paginatedRecords = processedRecords.slice(startIndex, endIndex);

        console.log('📊 分页数据:', {
          total: processedRecords.length,
          currentPage: page.currentPage,
          pageSize: page.pageSize,
          startIndex,
          endIndex,
          paginatedCount: paginatedRecords.length
        });

        console.log('📋 最终返回数据:', {
          total: processedRecords.length,
          rows: paginatedRecords
        });

        // 确保返回正确的数据结构
        const finalResult = {
          total: processedRecords.length,
          rows: paginatedRecords.map(record => ({
            id: record.id,
            name: record.name,
            type: record.type,
            icon: record.icon,
            originalName: record.originalName,
            createTime: record.createTime,
            recordCount: record.recordCount || 1,
            // 确保所有必要字段都存在
            fileCount: record.recordCount || 1
          }))
        };

        console.log('📊 表格数据结构验证:', finalResult);

        // 如果没有合并数据，添加测试数据确保表格能显示
        if (finalResult.rows.length === 0 && result.rows.length > 0) {
          console.log('🧪 添加测试数据');
          finalResult.rows = [{
            id: '1',
            name: '真夜白音',
            type: 4,
            icon: 'test',
            originalName: 'test.json',
            createTime: new Date().toISOString(),
            recordCount: result.rows.length,
            fileCount: result.rows.length
          }];
          finalResult.total = 1;
        }

        return finalResult;
      },
    },
  },
  rowConfig: {
    keyField: 'id',
  },
  // 表格全局唯一表示 保存列配置需要用到
  id: 'noval-live2d-index'
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [Live2dModal, modalApi] = useVbenModal({
  connectedComponent: live2dModal,
});

function handleAdd() {
  modalApi.setData({});
  modalApi.open();
}

async function handleEdit(row: Required<Live2dForm>) {
  modalApi.setData({ id: row.id });
  modalApi.open();
}

async function handleApply(row: Required<Live2dForm>) {
  try {
    console.log('🎯 应用模型:', row.name);

    // 使用统一的list2 API获取同名的所有记录
    const allRecords = await live2dList2({
      name: row.name,
      type: null
    });

    console.log('📊 找到的文件数:', allRecords.length);

    if (!allRecords || allRecords.length === 0) {
      message.error('未找到相关文件');
      return;
    }

    // 应用Live2D模型到页面
    await applyLive2DModel(row.name, allRecords);

    message.success(`已应用 "${row.name}" 模型，包含 ${allRecords.length} 个文件`);
  } catch (error) {
    console.error('应用模型失败:', error);
    message.error('应用模型失败');
  }
}

// 应用Live2D模型的函数
async function applyLive2DModel(modelName: string, records: any[]) {
  // 查找主模型文件（.model3.json）
  const mainModelRecord = records.find(record =>
    record.originalName && record.originalName.endsWith('.model3.json')
  );

  if (!mainModelRecord) {
    throw new Error('未找到主模型文件(.model3.json)');
  }

  // 通知Live2D组件应用新模型
  const event = new CustomEvent('applyLive2DModel', {
    detail: {
      modelName,
      modelUrl: mainModelRecord.icon,
      allFiles: records
    }
  });

  window.dispatchEvent(event);
  console.log('应用Live2D模型:', modelName, mainModelRecord.icon);
}

async function handleDelete(row: Required<Live2dForm>) {
  const recordCount = row.recordCount || 1;
  const confirmText = recordCount > 1
    ? `确认删除 "${row.name}" 吗？\n\n⚠️ 注意：检测到${recordCount}个同名记录，将全部删除，同时删除所有关联的COS文件。`
    : `确认删除 "${row.name}" 吗？\n\n⚠️ 注意：此操作将同时删除数据库记录和关联的COS文件。`;

  Modal.confirm({
    title: '删除确认',
    content: confirmText,
    okText: '确认删除',
    cancelText: '取消',
    okType: 'danger',
    width: 500,
    onOk: async () => {
      try {
        if (recordCount > 1) {
          // 删除所有同名记录，使用统一的list2 API
          console.log(`🗑️ [SINGLE-DEBUG] 开始删除同名记录: "${row.name}", 记录数: ${recordCount}`);
          console.log(`🗑️ [SINGLE-DEBUG] 行数据:`, row);
          const queryByName = async (name: string) => {
            console.log(`🔍 [QUERY-DEBUG] 查询名称: "${name}"`);
            try {
              // 使用标准的分页查询API，设置大的pageSize获取所有数据
              const result = await live2dList({
                pageNum: 1,
                pageSize: 10000,
                name: name,
              });
              console.log(`✅ [QUERY-DEBUG] 查询成功，结果:`, result);
              console.log(`✅ [QUERY-DEBUG] 找到记录数:`, result.rows?.length || 0);
              return result.rows || [];
            } catch (error) {
              console.error(`❌ [QUERY-DEBUG] 查询失败:`, error);
              throw error;
            }
          };
          const result = await deleteLive2DByName(row.name, queryByName, live2dRemoveByName);
          console.log(`✅ [SINGLE-DEBUG] 同名删除完成，结果:`, result);
          message.success(`删除成功，已删除${recordCount}个同名记录及关联文件`);
        } else {
          // 删除单个记录
          console.log(`🗑️ [SINGLE-DEBUG] 开始删除单个记录: "${row.name}", ID: ${row.id}`);
          console.log(`🗑️ [SINGLE-DEBUG] 行数据:`, row);
          const result = await deleteRecordWithFiles(
            row,
            live2dRemove,
            extractLive2DUrls
          );
          console.log(`✅ [SINGLE-DEBUG] 单个删除完成，结果:`, result);
          message.success('删除成功，已同时删除关联文件');
        }
        // 清空搜索条件后重新查询
        tableApi.formApi.resetFields();
        await tableApi.query();
      } catch (error: any) {
        console.error('❌ [SINGLE-DEBUG] 删除失败:', error);
        console.error('❌ [SINGLE-DEBUG] 错误对象详情:', {
          name: error?.name,
          message: error?.message,
          stack: error?.stack,
          response: error?.response,
          status: error?.status,
          statusText: error?.statusText,
        });

        // 提供更详细的错误信息
        let errorMessage = '删除失败';
        if (error?.message) {
          if (error.message.includes('请求出错')) {
            errorMessage = '删除失败：后端API暂时不可用，请联系管理员';
          } else if (error.message.includes('网络')) {
            errorMessage = '删除失败：网络连接异常，请检查网络后重试';
          } else {
            errorMessage = `删除失败：${error.message}`;
          }
        }

        message.error(errorMessage);

        // 在开发环境下提供调试信息
        if (import.meta.env.DEV) {
          console.log('💡 调试提示: 可以在控制台使用以下命令测试删除功能:');
          console.log(`window.testLive2DDelete("${row.name}")`);
        }
      }
    },
  });
}

function handleMultiDelete() {
  const rows = tableApi.grid.getCheckboxRecords();
  const totalRecords = rows.reduce((sum, row) => sum + (row.recordCount || 1), 0);
  const uniqueNames = rows.map(row => row.name);

  Modal.confirm({
    title: '批量删除确认',
    okType: 'danger',
    content: `确认删除选中的${rows.length}个Live2D模型吗？\n\n⚠️ 注意：将删除${totalRecords}条数据库记录和所有关联的COS文件。\n\n删除的模型：${uniqueNames.join(', ')}`,
    okText: '确认删除',
    cancelText: '取消',
    width: 600,
    onOk: async () => {
      try {
        console.log(`🗑️ [UI-DEBUG] 开始批量删除 ${rows.length} 个模型:`, uniqueNames);
        console.log(`🗑️ [UI-DEBUG] 选中的行数据:`, rows);
        console.log(`🗑️ [UI-DEBUG] 预计删除记录数:`, totalRecords);

        // 批量删除所有选中的模型名称
        const result = await live2dRemoveByNames(uniqueNames);
        console.log(`✅ [UI-DEBUG] 删除操作完成，结果:`, result);

        message.success(`成功删除${rows.length}个模型，共${totalRecords}条记录及关联文件`);

        console.log(`🔄 [UI-DEBUG] 开始清空搜索条件并重新查询...`);
        // 清空搜索条件后重新查询
        tableApi.formApi.resetFields();
        await tableApi.query();
        console.log(`✅ [UI-DEBUG] 表格刷新完成`);
      } catch (error: any) {
        console.error('❌ [UI-DEBUG] 批量删除失败:', error);
        console.error('❌ [UI-DEBUG] 错误对象详情:', {
          name: error?.name,
          message: error?.message,
          stack: error?.stack,
          response: error?.response,
          status: error?.status,
          statusText: error?.statusText,
        });

        // 提供更详细的错误信息
        let errorMessage = '批量删除失败';
        if (error?.message) {
          if (error.message.includes('请求出错')) {
            errorMessage = '批量删除失败：后端API暂时不可用，已尝试逐个删除';
          } else if (error.message.includes('网络')) {
            errorMessage = '批量删除失败：网络连接异常，请检查网络后重试';
          } else {
            errorMessage = `批量删除失败：${error.message}`;
          }
        }

        message.error(errorMessage);

        // 清空搜索条件后刷新表格以显示实际删除结果
        tableApi.formApi.resetFields();
        await tableApi.query();

        // 在开发环境下提供调试信息
        if (import.meta.env.DEV) {
          console.log('💡 调试提示: 批量删除可能部分成功，请检查表格数据');
          console.log('可以逐个测试删除:', uniqueNames);
        }
      }
    },
  });
}

function handleDownloadExcel() {
  commonDownloadExcel(live2dExport, 'Live2d数据', tableApi.formApi.form.values, {
    fieldMappingTime: formOptions.fieldMappingTime,
  });
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="Live2d列表">
      <template #toolbar-tools>
        <Space>
          <a-button
            v-access:code="['noval:live2d:export']"
            @click="handleDownloadExcel"
          >
            {{ $t('pages.common.export') }}
          </a-button>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['noval:live2d:remove']"
            @click="handleMultiDelete">
            {{ $t('pages.common.delete') }}
          </a-button>
          <a-button
            type="primary"
            v-access:code="['noval:live2d:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <ghost-button
            type="primary"
            @click.stop="handleApply(row)"
          >
            应用
          </ghost-button>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <ghost-button
              danger
              v-access:code="['noval:live2d:remove']"
              @click.stop=""
            >
              删除
            </ghost-button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <Live2dModal @reload="tableApi.query()" />
  </Page>
</template>
