import type { TodayVO, TodayForm, TodayQuery } from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { requestClient } from '#/api/request';

/**
* 查询今日new列表
* @param params
* @returns 今日new列表
*/
export function todayList(params?: TodayQuery) {
  return requestClient.get<PageResult<TodayVO>>('/noval/today/list', { params });
}


/**
 * 新增今日new
 * @param data
 * @returns void
 */
export function todayAdd(data: TodayForm) {
  return requestClient.postWithMsg<void>('/noval/today', data);
}

/**
 * 更新今日new
 * @param data
 * @returns void
 */
export function todayUpdate(data: TodayForm) {
  return requestClient.putWithMsg<void>('/noval/today', data);
}
