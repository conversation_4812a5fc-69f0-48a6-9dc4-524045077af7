import { fileTypeFromBlob } from '@vben/utils';

/**
 * 不支持txt文件 @see https://github.com/sindresorhus/file-type/issues/55
 * 需要自行修改
 * @param file file对象
 * @param accepts 文件类型数组  包括拓展名(不带点) 文件头(image/png等 不包括泛写法即image/*)
 * @returns 是否通过文件类型校验
 */
export async function checkFileType(file: File, accepts: string[]) {
  if (!accepts || accepts?.length === 0) {
    return true;
  }
  console.log(file);

  // 获取文件扩展名（从文件名中提取）
  const fileName = file.name.toLowerCase();
  const fileExtension = fileName.split('.').pop() || '';

  // 检查是否是目录（通过文件大小和类型判断）
  if (file.size === 0 && file.type === '') {
    console.log('检测到目录，跳过类型检查', { name: fileName });
    return true; // 目录总是允许的
  }

  // 特殊文件类型处理（Live2D相关文件）
  const specialExtensions = ['txt', 'moc3', '8192'];
  if (specialExtensions.includes(fileExtension)) {
    console.log('特殊文件类型', { ext: fileExtension, name: fileName });
    return accepts.includes(fileExtension);
  }

  // 复合扩展名处理（如 .motion3.json, .exp3.json 等）
  const compoundExtensions = ['motion3.json', 'exp3.json', 'physics3.json', 'cdi3.json', 'model3.json', 'vtube.json'];
  for (const compoundExt of compoundExtensions) {
    if (fileName.endsWith(compoundExt)) {
      console.log('复合扩展名文件', { ext: 'json', compound: compoundExt, name: fileName });
      return accepts.includes('json') || accepts.includes(compoundExt);
    }
  }

  const fileType = await fileTypeFromBlob(file);
  if (!fileType) {
    console.error('无法获取文件类型，尝试基于文件名扩展名判断');
    // 如果无法获取文件类型，基于文件扩展名进行判断
    if (accepts.includes(fileExtension)) {
      console.log('基于扩展名通过验证', { ext: fileExtension, name: fileName });
      return true;
    }
    return false;
  }
  console.log('文件类型', fileType);
  // 是否文件拓展名/文件头任意有一个匹配
  return accepts.includes(fileType.ext) || accepts.includes(fileType.mime);
}

/**
 * 默认图片类型
 */
export const defaultImageAccept = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
/**
 * 判断文件类型是否符合要求
 * @param file file对象
 * @param accepts 文件类型数组  包括拓展名(不带点) 文件头(image/png等 不包括泛写法即image/*)
 * @returns 是否通过文件类型校验
 */
export async function checkImageFileType(file: File, accepts: string[]) {
  // 空的accepts 使用默认规则
  if (!accepts || accepts.length === 0) {
    accepts = defaultImageAccept;
  }
  const fileType = await fileTypeFromBlob(file);
  if (!fileType) {
    console.error('无法获取文件类型');
    return false;
  }
  console.log('文件类型', fileType);
  // 是否文件拓展名/文件头任意有一个匹配
  if (accepts.includes(fileType.ext) || accepts.includes(fileType.mime)) {
    return true;
  }
  return false;
}
