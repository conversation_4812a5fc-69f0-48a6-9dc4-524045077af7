import type { PageQuery, BaseEntity } from '#/api/common';

export interface NewsVO {
  /**
   * 消息发起人id
   */
  id: string | number;

  /**
   * 消息接收者id
   */
  otherId: string | number;

  /**
   * 消息内容
   */
  news: string;

   /**
   * 消息类型,0代表管理员信息，1代表论坛信息，2代表点赞消息，3代表私聊信息，4代表公告，5代表通知
   */
  type: string | number;

  /**
   * 是否已读，0为未读，1为已读
   */
  isread: number;

  /**
   * 评论ID，关联的评论记录
   */
  commentid: string | number;

}

export interface NewsForm extends BaseEntity {
  /**
   * 消息发起人id
   */
  id?: string | number;

  /**
   * 消息接收者id
   */
  otherId?: string | number;

  /**
   * 消息内容
   */
  news?: string;

   /**
   * 消息类型,0代表管理员信息，1代表论坛信息，2代表点赞消息，3代表私聊信息，4代表公告，5代表通知
   */
  type?: string | number;

  /**
   * 是否已读，0为未读，1为已读
   */
  isread?: number;

  /**
   * 评论ID，关联的评论记录
   */
  commentid?: string | number;

}

export interface NewsQuery extends PageQuery {
  /**
   * 消息发起人id
   */
  id?: string | number;

  /**
   * 消息接收者id
   */
  otherId?: string | number;

  /**
   * 消息内容
   */
  news?: string;

   /**
   * 消息类型,0代表管理员信息，1代表论坛信息，2代表点赞消息，3代表私聊信息，4代表公告，5代表通知
   */
  type?: string | number;

  /**
   * 是否已读，0为未读，1为已读
   */
  isread?: number;

  /**
   * 评论ID，关联的评论记录
   */
  commentid?: string | number;


  /**
    * 日期范围参数
    */
  params?: any;
}
