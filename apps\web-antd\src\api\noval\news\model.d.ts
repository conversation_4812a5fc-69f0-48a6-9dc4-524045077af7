import type { PageQuery, BaseEntity } from '#/api/common';

export interface NewsVO {
  /**
   * 消息发起人id
   */
  id: string | number;

  /**
   * 消息接收者id
   */
  otherId: string | number;

  /**
   * 消息内容
   */
  news: string;

   /**
   * 消息类型,0代表管理员信息，1代表论坛信息，2代表私聊信息
   */
  type: string | number;

}

export interface NewsForm extends BaseEntity {
  /**
   * 消息发起人id
   */
  id?: string | number;

  /**
   * 消息接收者id
   */
  otherId?: string | number;

  /**
   * 消息内容
   */
  news?: string;

   /**
   * 消息类型
   */
  type?: string | number;

}

export interface NewsQuery extends PageQuery {
  /**
   * 消息发起人id
   */
  id?: string | number;

  /**
   * 消息接收者id
   */
  otherId?: string | number;

  /**
   * 消息内容
   */
  news?: string;

   /**
   * 消息类型
   */
  type?: string | number;


  /**
    * 日期范围参数
    */
  params?: any;
}
