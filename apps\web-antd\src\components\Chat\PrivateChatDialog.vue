<script setup lang="ts">
import { ref, onMounted, nextTick, computed, watch, h } from 'vue';
import { Modal, Input, Button, Avatar, List, message, Empty } from 'ant-design-vue';
import { SendOutlined, UserOutlined } from '@ant-design/icons-vue';
import { useUserStore } from '@vben/stores';
import { formatDateTime } from '@vben/utils';

import { newsList, newsAdd } from '#/api/noval/news';
import { findUserInfo } from '#/api/system/user';
import type { NewsVO } from '#/api/noval/news/model';

interface Props {
  visible: boolean;
  targetUserId: string | number;
  targetUserName?: string;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const userStore = useUserStore();
const currentUser = computed(() => userStore.userInfo);

const messageInput = ref('');
const chatMessages = ref<NewsVO[]>([]);
const loading = ref(false);
const sending = ref(false);
const targetUserInfo = ref<{ userName: string; userAvatar: string }>({
  userName: props.targetUserName || '未知用户',
  userAvatar: '',
});

// 聊天容器引用
const chatContainer = ref<HTMLElement>();

// 默认头像
const defaultAvatar = 'https://unpkg.com/@vbenjs/static-source@0.1.7/source/avatar-v1.webp';

// 获取有效的头像URL
function getValidAvatarUrl(userAvatar?: string): string {
  if (userAvatar && userAvatar.trim() && userAvatar !== 'null' && userAvatar !== 'undefined') {
    return userAvatar.trim();
  }
  return defaultAvatar;
}

// 获取目标用户信息
async function getTargetUserInfo() {
  try {
    const userInfo = await findUserInfo(props.targetUserId);
    targetUserInfo.value = {
      userName: userInfo.user?.nickName || userInfo.user?.userName || '未知用户',
      userAvatar: getValidAvatarUrl(userInfo.user?.avatar),
    };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    targetUserInfo.value = {
      userName: props.targetUserName || '未知用户',
      userAvatar: defaultAvatar,
    };
  }
}

// 加载聊天记录
async function loadChatMessages() {
  if (!props.targetUserId || !currentUser.value?.userId) return;

  loading.value = true;
  try {
    // 获取与目标用户的私聊记录
    const result = await newsList({
      pageNum: 1,
      pageSize: 100,
      type: 3, // 私聊信息
    });

    if (result.rows) {
      // 筛选出与目标用户的对话记录
      const filteredMessages = result.rows.filter((msg: NewsVO) => {
        const currentUserId = currentUser.value?.userId?.toString();
        const targetUserId = props.targetUserId.toString();
        const msgSenderId = msg.id.toString();
        const msgReceiverId = msg.otherId.toString();

        return (
          (msgSenderId === currentUserId && msgReceiverId === targetUserId) ||
          (msgSenderId === targetUserId && msgReceiverId === currentUserId)
        );
      });

      // 按时间排序
      chatMessages.value = filteredMessages.sort((a, b) => {
        return new Date(a.createTime || '').getTime() - new Date(b.createTime || '').getTime();
      });
    }
  } catch (error) {
    console.error('加载聊天记录失败:', error);
    message.error('加载聊天记录失败');
  } finally {
    loading.value = false;
    // 滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
  }
}

// 发送消息
async function sendMessage() {
  if (!messageInput.value.trim() || sending.value) return;

  const messageContent = messageInput.value.trim();
  messageInput.value = '';
  sending.value = true;

  try {
    await newsAdd({
      id: currentUser.value?.userId,
      otherId: props.targetUserId,
      news: messageContent,
      type: 3, // 私聊信息
    });

    // 直接添加消息到本地列表，避免重复
    const newMessage = {
      id: Date.now(), // 临时ID
      myId: currentUser.value?.userId,
      otherId: props.targetUserId,
      news: messageContent,
      type: 3,
      createTime: new Date().toISOString(),
      senderName: currentUser.value?.nickName || currentUser.value?.userName || '我',
      senderAvatar: currentUser.value?.avatar || getValidAvatarUrl(),
      isSent: true,
    };

    chatMessages.value.push(newMessage);

    // 滚动到底部
    nextTick(() => {
      scrollToBottom();
    });

    // 不显示成功消息，避免干扰用户体验
  } catch (error) {
    console.error('发送消息失败:', error);
    message.error('发送消息失败');
    // 恢复输入内容
    messageInput.value = messageContent;
  } finally {
    sending.value = false;
  }
}

// 滚动到底部
function scrollToBottom() {
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
  }
}

// 处理回车发送
function handleKeyPress(event: KeyboardEvent) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
}

// 关闭对话框
function handleClose() {
  emit('update:visible', false);
  emit('close');
}

// 判断消息是否为当前用户发送
function isCurrentUserMessage(message: NewsVO): boolean {
  return message.id.toString() === currentUser.value?.userId?.toString();
}

// 监听visible变化
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    getTargetUserInfo();
    loadChatMessages();
  }
});

onMounted(() => {
  if (props.visible) {
    getTargetUserInfo();
    loadChatMessages();
  }
});
</script>

<template>
  <Modal
    :open="visible"
    :title="`与 ${targetUserInfo.userName} 的私聊`"
    width="600px"
    :footer="null"
    @cancel="handleClose"
    class="private-chat-modal p3r-modal"
  >
    <div class="chat-container">
      <!-- 聊天消息区域 -->
      <div
        ref="chatContainer"
        class="chat-messages"
        :class="{ 'loading': loading }"
      >
        <div v-if="loading" class="loading-indicator">
          <div class="text-center py-4">
            <div class="text-gray-500">加载聊天记录中...</div>
          </div>
        </div>

        <div v-else-if="chatMessages.length === 0" class="empty-chat">
          <Empty
            description="还没有聊天记录，开始你们的第一次对话吧！"
            :image="Empty.PRESENTED_IMAGE_SIMPLE"
          />
        </div>

        <div v-else class="messages-list">
          <div
            v-for="message in chatMessages"
            :key="`${message.id}-${message.createTime}`"
            class="message-item"
            :class="{
              'message-sent': isCurrentUserMessage(message),
              'message-received': !isCurrentUserMessage(message)
            }"
          >
            <div class="message-content">
              <div class="message-avatar">
                <Avatar
                  :src="isCurrentUserMessage(message) ? currentUser?.avatar : targetUserInfo.userAvatar"
                  :size="32"
                >
                  <template #icon>
                    <UserOutlined />
                  </template>
                </Avatar>
              </div>
              <div class="message-body">
                <div class="message-text">{{ message.news }}</div>
                <div class="message-time">
                  {{ formatDateTime(message.createTime) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 消息输入区域 -->
      <div class="chat-input">
        <div class="input-container">
          <Input.TextArea
            v-model:value="messageInput"
            placeholder="输入消息..."
            :rows="3"
            :maxlength="500"
            show-count
            @keypress="handleKeyPress"
            :disabled="sending"
          />
          <div class="input-actions">
            <Button
              type="primary"
              :icon="h(SendOutlined)"
              :loading="sending"
              :disabled="!messageInput.trim()"
              @click="sendMessage"
            >
              发送
            </Button>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<style scoped>
/* P3R风格样式 */
.p3r-modal :deep(.ant-modal-content) {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.3),
    0 10px 10px -5px rgba(0, 0, 0, 0.2),
    0 0 30px rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(20px);
}

.p3r-modal :deep(.ant-modal-header) {
  background: transparent;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.p3r-modal :deep(.ant-modal-title) {
  color: #3b82f6;
  font-family: 'Orbitron', sans-serif;
  font-weight: 700;
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

.p3r-modal :deep(.ant-modal-close) {
  color: #64748b;
}

.p3r-modal :deep(.ant-modal-close:hover) {
  color: #3b82f6;
}

.chat-container {
  height: 500px;
  display: flex;
  flex-direction: column;
  font-family: 'Rajdhani', sans-serif;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  margin-bottom: 16px;
  backdrop-filter: blur(10px);
}

.chat-messages.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-chat {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-item {
  display: flex;
  width: 100%;
}

.message-sent {
  justify-content: flex-end;
}

.message-received {
  justify-content: flex-start;
}

.message-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  max-width: 70%;
}

.message-sent .message-content {
  flex-direction: row-reverse;
}

.message-body {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.message-text {
  padding: 8px 12px;
  border-radius: 12px;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.message-sent .message-text {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  color: white;
  border-bottom-right-radius: 4px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.message-received .message-text {
  background: rgba(30, 41, 59, 0.8);
  color: #e2e8f0;
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-bottom-left-radius: 4px;
  backdrop-filter: blur(10px);
}

.message-time {
  font-size: 12px;
  color: #64748b;
  text-align: center;
}

.chat-input {
  border-top: 1px solid rgba(59, 130, 246, 0.2);
  padding-top: 16px;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-actions {
  display: flex;
  justify-content: flex-end;
}

.input-actions :deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  border: 1px solid rgba(59, 130, 246, 0.5);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.input-actions :deep(.ant-btn-primary:hover) {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

.input-container :deep(.ant-input) {
  background: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: #e2e8f0;
  backdrop-filter: blur(10px);
}

.input-container :deep(.ant-input:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

.input-container :deep(.ant-input::placeholder) {
  color: #64748b;
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.5);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.7);
}
</style>
