import type { PageQuery, BaseEntity } from '#/api/common';

export interface CommentVO {
  /**
   * 评论id
   */
  commerntId: string | number;

  /**
   * 用户id
   */
  myId: string | number;

  /**
   * 评论类型，1代表评论，0代表楼主
   */
  commerntType: number;

  /**
   * 是否为回复，1为回复，0为不是回复
   */
  isResponse: number;

  /**
   * 回复的用户id
   */
  responseId: string | number;

  /**
   * 评论内容
   */
  content: string;

  /**
   * 曝光数
   */
  look: number;

  /**
   * 是否为精品，1为精品，0为不是精品
   */
  isFine: number;


  /**
     * 图片1
     */
  icon1: String;

    /**
     * 图片2
     */
  icon2: String;

    /**
     * 图片3
     */
  icon3: String;

    /**
     * 图片4
     */
  icon4: String;

    /**
     * 图片5
     */
  icon5: String;

}

export interface CommentForm extends BaseEntity {
  /**
   * 评论id
   */
  commerntId?: string | number;

  /**
   * 用户id
   */
  myId?: string | number;

  /**
   * 评论类型，1代表评论，0代表楼主
   */
  commerntType?: number;

  /**
   * 是否为回复，1为回复，0为不是回复
   */
  isResponse?: number;

  /**
   * 回复的用户id
   */
  responseId?: string | number;

  /**
   * 评论内容
   */
  content?: string;

  /**
   * 曝光数
   */
  look?: number;

  /**
   * 是否为精品，1为精品，0为不是精品
   */
  isFine?: number;

    /**
     * 图片1
     */
  icon1?: String;

    /**
     * 图片2
     */
  icon2?: String;

    /**
     * 图片3
     */
  icon3?: String;

    /**
     * 图片4
     */
  icon4?: String;

    /**
     * 图片5
     */
  icon5?: String;


}

export interface CommentQuery extends PageQuery {
  /**
   * 评论id
   */
  commerntId?: string | number;

  /**
   * 用户id
   */
  myId?: string | number;

  /**
   * 评论类型，1代表评论，0代表楼主
   */
  commerntType?: number;

  /**
   * 是否为回复，1为回复，0为不是回复
   */
  isResponse?: number;

  /**
   * 回复的用户id
   */
  responseId?: string | number;

  /**
   * 评论内容
   */
  content?: string;

  /**
   * 曝光数
   */
  look?: number;

  /**
   * 是否为精品，1为精品，0为不是精品
   */
  isFine?: number;

   /**
     * 图片1
     */
  icon1?: String;

    /**
     * 图片2
     */
  icon2?: String;

    /**
     * 图片3
     */
  icon3?: String;

    /**
     * 图片4
     */
  icon4?: String;

    /**
     * 图片5
     */
  icon5?: String;


  /**
    * 日期范围参数
    */
  params?: any;
}
