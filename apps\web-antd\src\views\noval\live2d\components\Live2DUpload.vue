<template>
  <div class="live2d-upload-container">
    <!-- 上传区域 -->
    <div class="upload-section">
      <FileUpload
        v-model:value="fileList"
        :accept="acceptTypes"
        :max-number="200"
        :max-size="50"
        :multiple="true"
        :directory="true"
        :help-text="uploadHelpText"
        @change="handleFileChange"
      />
    </div>

    <!-- 使用说明 -->
    <div class="upload-guide">
      <div class="guide-header">
        <FolderOpenOutlined class="guide-icon" />
        <span class="guide-title">Live2D模型上传说明</span>
      </div>

      <div class="guide-content">
        <div class="guide-section">
          <div class="section-title">
            <CheckCircleOutlined class="check-icon" />
            支持的上传方式
          </div>
          <ul class="guide-list">
            <li>直接拖拽整个Live2D模型文件夹（推荐）</li>
            <li>选择多个文件批量上传</li>
            <li>支持嵌套文件夹结构</li>
          </ul>
        </div>

        <div class="guide-section">
          <div class="section-title">
            <FileTextOutlined class="file-icon" />
            支持的文件格式
          </div>
          <div class="file-types">
            <Tag color="blue">模型文件: .moc3</Tag>
            <Tag color="green">配置文件: .model3.json</Tag>
            <Tag color="orange">动作文件: .motion3.json</Tag>
            <Tag color="purple">表情文件: .exp3.json</Tag>
            <Tag color="cyan">物理文件: .physics3.json</Tag>
            <Tag color="geekblue">纹理文件: .png</Tag>
            <Tag color="gold">其他: .json, .txt</Tag>
          </div>
        </div>

        <div class="guide-section">
          <div class="section-title">
            <SettingOutlined class="setting-icon" />
            上传限制
          </div>
          <div class="limits-grid">
            <div class="limit-item">
              <span class="limit-label">单文件大小</span>
              <span class="limit-value">最大 50MB</span>
            </div>
            <div class="limit-item">
              <span class="limit-label">文件数量</span>
              <span class="limit-value">最多 200 个</span>
            </div>
            <div class="limit-item">
              <span class="limit-label">文件夹结构</span>
              <span class="limit-value">保持原有结构</span>
            </div>
          </div>
        </div>

        <div class="guide-section">
          <div class="section-title">
            <BulbOutlined class="tip-icon" />
            使用建议
          </div>
          <ul class="guide-list tips-list">
            <li>建议上传完整的Live2D模型文件夹，确保所有依赖文件完整</li>
            <li>文件名建议使用英文，避免特殊字符</li>
            <li>上传前请确认模型文件可以正常使用</li>
            <li>大文件上传可能需要较长时间，请耐心等待</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 文件预览 -->
    <div v-if="fileList.length > 0" class="file-preview">
      <div class="preview-header">
        <FileOutlined class="preview-icon" />
        <span class="preview-title">已选择文件 ({{ fileList.length }})</span>
        <Button size="small" type="link" @click="clearFiles">清空</Button>
      </div>
      <div class="file-list">
        <div
          v-for="(file, index) in displayFiles"
          :key="index"
          class="file-item"
        >
          <div class="file-info">
            <component :is="getFileIcon(file)" class="file-type-icon" />
            <span class="file-name">{{ file }}</span>
          </div>
        </div>
        <div v-if="fileList.length > 10" class="more-files">
          还有 {{ fileList.length - 10 }} 个文件...
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { Tag, Button } from 'ant-design-vue';
import {
  FolderOpenOutlined,
  CheckCircleOutlined,
  FileTextOutlined,
  SettingOutlined,
  BulbOutlined,
  FileOutlined,
  FileImageOutlined,
  CodeOutlined,
  PlayCircleOutlined,
} from '@ant-design/icons-vue';
import { FileUpload } from '#/components/upload';

interface Props {
  value?: string[];
}

interface Emits {
  (e: 'update:value', value: string[]): void;
  (e: 'change', value: string[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  value: () => [],
});

const emit = defineEmits<Emits>();

const fileList = ref<string[]>(props.value || []);

// 支持的文件类型
const acceptTypes = [
  'json',
  'moc3',
  'png',
  'txt',
  '8192',
  'motion3.json',
  'exp3.json',
  'physics3.json',
  'cdi3.json',
  'model3.json',
  'vtube.json',
];

// 上传帮助文本
const uploadHelpText = '拖拽Live2D模型文件夹到此处，或点击选择文件';

// 显示的文件列表（最多显示10个）
const displayFiles = computed(() => {
  return fileList.value.slice(0, 10);
});

// 根据文件扩展名获取图标
const getFileIcon = (fileName: string) => {
  const ext = fileName.toLowerCase().split('.').pop();

  switch (ext) {
    case 'png':
    case 'jpg':
    case 'jpeg':
      return FileImageOutlined;
    case 'json':
      return CodeOutlined;
    case 'moc3':
      return PlayCircleOutlined;
    default:
      return FileOutlined;
  }
};

// 处理文件变化
const handleFileChange = (files: string[]) => {
  fileList.value = files;
  emit('update:value', files);
  emit('change', files);
};

// 清空文件
const clearFiles = () => {
  fileList.value = [];
  emit('update:value', []);
  emit('change', []);
};

// 监听props变化
watch(() => props.value, (newValue) => {
  fileList.value = newValue || [];
}, { immediate: true });
</script>

<style scoped>
.live2d-upload-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.upload-section {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 24px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.upload-section:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.upload-guide {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 20px;
}

.guide-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.guide-icon {
  color: #1890ff;
  font-size: 18px;
  margin-right: 8px;
}

.guide-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.guide-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.guide-section {
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  border-left: 4px solid #1890ff;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
  color: #262626;
}

.check-icon {
  color: #52c41a;
  margin-right: 8px;
}

.file-icon {
  color: #722ed1;
  margin-right: 8px;
}

.setting-icon {
  color: #fa8c16;
  margin-right: 8px;
}

.tip-icon {
  color: #fadb14;
  margin-right: 8px;
}

.guide-list {
  margin: 0;
  padding-left: 20px;
  color: #595959;
}

.guide-list li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.tips-list li {
  color: #8c8c8c;
  font-size: 13px;
}

.file-types {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.limits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.limit-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.limit-label {
  color: #8c8c8c;
  font-size: 13px;
}

.limit-value {
  color: #262626;
  font-weight: 500;
  font-size: 13px;
}

.file-preview {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.preview-icon {
  color: #1890ff;
  margin-right: 8px;
}

.preview-title {
  font-weight: 500;
  color: #262626;
  flex: 1;
}

.file-list {
  max-height: 200px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #f5f5f5;
}

.file-item:last-child {
  border-bottom: none;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-type-icon {
  color: #1890ff;
  margin-right: 8px;
  font-size: 14px;
}

.file-name {
  color: #595959;
  font-size: 13px;
  word-break: break-all;
}

.more-files {
  text-align: center;
  color: #8c8c8c;
  font-size: 12px;
  padding: 8px 0;
  font-style: italic;
}
</style>
