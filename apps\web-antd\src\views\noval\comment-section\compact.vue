<script setup lang="ts">
import type { CommentVO, CommentForm } from '#/api/noval/comment/model';

import { computed, onMounted, ref } from 'vue';

import { useUserStore } from '@vben/stores';
import { formatDateTime } from '@vben/utils';

import { Avatar, Button, Input, List, message, Space } from 'ant-design-vue';
import { UserOutlined, MessageOutlined, SendOutlined } from '@ant-design/icons-vue';

import { commentAdd, commentList } from '#/api/noval/comment';
import { findUserInfo } from '#/api/system/user';

interface CommentWithUser extends CommentVO {
  userName?: string;
  userAvatar?: string;
  createTime?: string;
}

interface Props {
  // 关联的主题ID
  targetId?: string | number;
  // 评论类型
  commentType?: number;
  // 最大显示评论数
  maxComments?: number;
  // 是否显示发表评论框
  showCommentBox?: boolean;
  // 是否显示"查看更多"链接
  showMoreLink?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  targetId: '',
  commentType: 1,
  maxComments: 5,
  showCommentBox: true,
  showMoreLink: true,
});

const emit = defineEmits<{
  viewMore: [];
  commentAdded: [comment: CommentForm];
}>();

const userStore = useUserStore();
const loading = ref(false);
const submitting = ref(false);
const comments = ref<CommentWithUser[]>([]);
const newComment = ref('');
const totalComments = ref(0);

// 用户信息缓存
const userCache = ref<Map<string | number, { userName: string; userAvatar: string }>>(new Map());

// 当前用户信息
const currentUser = computed(() => userStore.userInfo);

// 是否显示更多按钮
const showMoreButton = computed(() =>
  props.showMoreLink && totalComments.value > props.maxComments
);

// 获取用户信息
async function getUserInfo(userId: string | number) {
  if (userCache.value.has(userId)) {
    return userCache.value.get(userId);
  }

  try {
    const userInfo = await findUserInfo(userId);
    const userData = {
      userName: userInfo.user?.nickName || userInfo.user?.userName || '未知用户',
      userAvatar: userInfo.user?.avatar || '',
    };
    userCache.value.set(userId, userData);
    return userData;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      userName: '未知用户',
      userAvatar: '',
    };
  }
}

// 加载评论列表
async function loadComments() {
  loading.value = true;
  try {
    const response = await commentList({
      pageNum: 1,
      pageSize: props.maxComments,
      commerntType: props.commentType,
      isResponse: 0, // 只获取主评论，不包括回复
    });

    const commentData = response.rows || [];
    totalComments.value = response.total || 0;

    // 获取所有用户信息
    const userIds = [...new Set(commentData.map(item => item.myId))];
    await Promise.all(userIds.map(userId => getUserInfo(userId)));

    // 构建评论数据
    comments.value = commentData.map(comment => ({
      ...comment,
      userName: userCache.value.get(comment.myId)?.userName,
      userAvatar: userCache.value.get(comment.myId)?.userAvatar,
      createTime: comment.createTime || new Date().toISOString(),
    })).sort((a, b) =>
      new Date(b.createTime || 0).getTime() - new Date(a.createTime || 0).getTime()
    );
  } catch (error) {
    console.error('加载评论失败:', error);
    message.error('加载评论失败');
  } finally {
    loading.value = false;
  }
}

// 发表新评论
async function submitComment() {
  if (!newComment.value.trim()) {
    message.warning('请输入评论内容');
    return;
  }

  if (!currentUser.value?.userId) {
    message.error('请先登录');
    return;
  }

  submitting.value = true;
  try {
    const commentData: CommentForm = {
      content: newComment.value.trim(),
      myId: currentUser.value.userId,
      commerntType: props.commentType,
      isResponse: 0,
      responseId: 0,
    };

    await commentAdd(commentData);
    message.success('评论发表成功');
    emit('commentAdded', commentData);
    newComment.value = '';
    await loadComments(); // 重新加载评论
  } catch (error) {
    console.error('发表评论失败:', error);
    message.error('发表评论失败');
  } finally {
    submitting.value = false;
  }
}

// 查看更多评论
function handleViewMore() {
  emit('viewMore');
}

// 组件挂载时加载评论
onMounted(() => {
  loadComments();
});

// 暴露刷新方法
defineExpose({
  refresh: loadComments,
});
</script>

<template>
  <div class="compact-comment-section">
    <!-- 发表评论区域 -->
    <div v-if="showCommentBox" class="comment-input-section mb-4">
      <div class="flex gap-3">
        <Avatar
          :src="currentUser?.avatar"
          :size="32"
        >
          <template #icon>
            <UserOutlined />
          </template>
        </Avatar>
        <div class="flex-1">
          <Input.TextArea
            v-model:value="newComment"
            :rows="2"
            placeholder="写下你的评论..."
            :maxlength="200"
            show-count
            class="mb-2"
          />
          <div class="text-right">
            <Button
              type="primary"
              size="small"
              :loading="submitting"
              @click="submitComment"
            >
              <template #icon>
                <SendOutlined />
              </template>
              发表
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- 评论列表 -->
    <div class="comment-list-section">
      <div v-if="totalComments > 0" class="mb-3 text-sm text-gray-600">
        共 {{ totalComments }} 条评论
      </div>

      <List
        v-if="comments.length > 0"
        :data-source="comments"
        :loading="loading"
        size="small"
      >
        <template #renderItem="{ item: comment }">
          <List.Item class="compact-comment-item">
            <div class="flex gap-2 w-full">
              <Avatar
                :src="comment.userAvatar"
                :size="28"
              >
                <template #icon>
                  <UserOutlined />
                </template>
              </Avatar>
              <div class="flex-1 min-w-0">
                <div class="comment-header mb-1">
                  <span class="font-medium text-sm text-gray-900">{{ comment.userName }}</span>
                  <span class="text-gray-500 text-xs ml-2">
                    {{ formatDateTime(comment.createTime) }}
                  </span>
                </div>
                <div class="comment-text text-sm text-gray-700 break-words">
                  {{ comment.content }}
                </div>
              </div>
            </div>
          </List.Item>
        </template>
      </List>

      <!-- 空状态 -->
      <div v-else-if="!loading" class="text-center py-4 text-gray-500 text-sm">
        <MessageOutlined class="text-lg mb-1" />
        <div>暂无评论</div>
      </div>

      <!-- 查看更多按钮 -->
      <div v-if="showMoreButton" class="text-center mt-3">
        <Button type="link" size="small" @click="handleViewMore">
          查看全部 {{ totalComments }} 条评论
        </Button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.compact-comment-section {
  font-size: 14px;
}

.compact-comment-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.compact-comment-item:last-child {
  border-bottom: none;
}

.comment-header {
  display: flex;
  align-items: center;
}

.comment-text {
  line-height: 1.4;
  word-break: break-word;
}

.comment-input-section {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}
</style>
