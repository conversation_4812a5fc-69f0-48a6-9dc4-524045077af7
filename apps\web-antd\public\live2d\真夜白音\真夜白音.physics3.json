{"Version": 3, "Meta": {"PhysicsSettingCount": 100, "TotalInputCount": 357, "TotalOutputCount": 238, "VertexCount": 434, "Fps": 60, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "角度X离心力+"}, {"Id": "PhysicsSetting2", "Name": "角度X离心力-"}, {"Id": "PhysicsSetting3", "Name": "角度Z离心力+"}, {"Id": "PhysicsSetting4", "Name": "角度Z离心力-"}, {"Id": "PhysicsSetting5", "Name": "左眼睫毛"}, {"Id": "PhysicsSetting6", "Name": "右眼睫毛"}, {"Id": "PhysicsSetting7", "Name": "左眼瞳孔"}, {"Id": "PhysicsSetting8", "Name": "右眼瞳孔"}, {"Id": "PhysicsSetting9", "Name": "左眼开闭物理"}, {"Id": "PhysicsSetting10", "Name": "右眼开闭物理"}, {"Id": "PhysicsSetting11", "Name": "左眼高光1"}, {"Id": "PhysicsSetting12", "Name": "右眼高光1"}, {"Id": "PhysicsSetting13", "Name": "左眼高光2"}, {"Id": "PhysicsSetting14", "Name": "右眼高光2"}, {"Id": "PhysicsSetting15", "Name": "左眼泪2"}, {"Id": "PhysicsSetting16", "Name": "右眼泪2"}, {"Id": "PhysicsSetting17", "Name": "左眼泪2_2"}, {"Id": "PhysicsSetting18", "Name": "右眼泪2_2"}, {"Id": "PhysicsSetting19", "Name": "猫耳L"}, {"Id": "PhysicsSetting20", "Name": "猫耳R"}, {"Id": "PhysicsSetting21", "Name": "猫耳L1"}, {"Id": "PhysicsSetting22", "Name": "猫耳R1"}, {"Id": "PhysicsSetting23", "Name": "猫耳L2"}, {"Id": "PhysicsSetting24", "Name": "猫耳R2"}, {"Id": "PhysicsSetting25", "Name": "发饰蝴蝶结1R"}, {"Id": "PhysicsSetting26", "Name": "麦克风蝴蝶结1R"}, {"Id": "PhysicsSetting27", "Name": "发饰蝴蝶结1L"}, {"Id": "PhysicsSetting28", "Name": "麦克风蝴蝶结1L"}, {"Id": "PhysicsSetting29", "Name": "发饰花物理"}, {"Id": "PhysicsSetting30", "Name": "汗物理X"}, {"Id": "PhysicsSetting31", "Name": "汗物理Y"}, {"Id": "PhysicsSetting32", "Name": "帽子带物理"}, {"Id": "PhysicsSetting33", "Name": "前发1R"}, {"Id": "PhysicsSetting34", "Name": "前发1L"}, {"Id": "PhysicsSetting35", "Name": "侧发1R"}, {"Id": "PhysicsSetting36", "Name": "侧发1L"}, {"Id": "PhysicsSetting37", "Name": "侧发2L"}, {"Id": "PhysicsSetting38", "Name": "侧发2R"}, {"Id": "PhysicsSetting39", "Name": "袜子1_1XL"}, {"Id": "PhysicsSetting40", "Name": "袜子1_1YL"}, {"Id": "PhysicsSetting41", "Name": "肩膀上下"}, {"Id": "PhysicsSetting42", "Name": "左上臂旋转"}, {"Id": "PhysicsSetting43", "Name": "右上臂旋转"}, {"Id": "PhysicsSetting44", "Name": "右上臂旋转_2"}, {"Id": "PhysicsSetting45", "Name": "左下臂旋转"}, {"Id": "PhysicsSetting46", "Name": "右下臂旋转"}, {"Id": "PhysicsSetting47", "Name": "右下臂旋转_2"}, {"Id": "PhysicsSetting48", "Name": "左手带物理R1"}, {"Id": "PhysicsSetting49", "Name": "右手带物理R1"}, {"Id": "PhysicsSetting50", "Name": "左手带物理L1"}, {"Id": "PhysicsSetting51", "Name": "右手带物理L1"}, {"Id": "PhysicsSetting52", "Name": "左手带物理R2"}, {"Id": "PhysicsSetting53", "Name": "右手带物理R2"}, {"Id": "PhysicsSetting54", "Name": "左手带物理L2"}, {"Id": "PhysicsSetting55", "Name": "右手带物理L2"}, {"Id": "PhysicsSetting56", "Name": "左手带物理3"}, {"Id": "PhysicsSetting57", "Name": "右手带物理3"}, {"Id": "PhysicsSetting58", "Name": "左袖X"}, {"Id": "PhysicsSetting59", "Name": "右袖X"}, {"Id": "PhysicsSetting60", "Name": "左袖Y"}, {"Id": "PhysicsSetting61", "Name": "右袖Y"}, {"Id": "PhysicsSetting62", "Name": "裙子带1_1R"}, {"Id": "PhysicsSetting63", "Name": "裙子带2_1R"}, {"Id": "PhysicsSetting64", "Name": "裙子带2_1L"}, {"Id": "PhysicsSetting65", "Name": "裙子蝴蝶结R"}, {"Id": "PhysicsSetting66", "Name": "裙子蝴蝶结L"}, {"Id": "PhysicsSetting67", "Name": "裙子带1_1L"}, {"Id": "PhysicsSetting68", "Name": "腿环L"}, {"Id": "PhysicsSetting69", "Name": "腿环R"}, {"Id": "PhysicsSetting70", "Name": "后发1L"}, {"Id": "PhysicsSetting71", "Name": "后发1Y"}, {"Id": "PhysicsSetting72", "Name": "后发1X"}, {"Id": "PhysicsSetting73", "Name": "辫子1X"}, {"Id": "PhysicsSetting74", "Name": "蝴蝶结1R"}, {"Id": "PhysicsSetting75", "Name": "蝴蝶结1L"}, {"Id": "PhysicsSetting76", "Name": "领子蝴蝶结1"}, {"Id": "PhysicsSetting77", "Name": "领子蝴蝶结1L"}, {"Id": "PhysicsSetting78", "Name": "领子蝴蝶结2L"}, {"Id": "PhysicsSetting79", "Name": "领子蝴蝶结2R"}, {"Id": "PhysicsSetting80", "Name": "胸X1"}, {"Id": "PhysicsSetting81", "Name": "胸X2"}, {"Id": "PhysicsSetting82", "Name": "胸Y2"}, {"Id": "PhysicsSetting83", "Name": "胸Y1"}, {"Id": "PhysicsSetting84", "Name": "裙子2_1X"}, {"Id": "PhysicsSetting85", "Name": "裙子2_1Y"}, {"Id": "PhysicsSetting86", "Name": "裙子1_1X"}, {"Id": "PhysicsSetting87", "Name": "裙子1_1Y"}, {"Id": "PhysicsSetting88", "Name": "铃铛1"}, {"Id": "PhysicsSetting89", "Name": "铃铛2"}, {"Id": "PhysicsSetting90", "Name": "脖子饰品"}, {"Id": "PhysicsSetting91", "Name": "脖子饰品X"}, {"Id": "PhysicsSetting92", "Name": "角度Y 身体Y"}, {"Id": "PhysicsSetting93", "Name": "头Y整体物理"}, {"Id": "PhysicsSetting94", "Name": "头Z整体物理"}, {"Id": "PhysicsSetting95", "Name": "头Z身体Z绑定"}, {"Id": "PhysicsSetting96", "Name": "头X身体X"}, {"Id": "PhysicsSetting97", "Name": "身体X位置X"}, {"Id": "PhysicsSetting98", "Name": "角度X腿X"}, {"Id": "PhysicsSetting99", "Name": "身体Y位置Y"}, {"Id": "PhysicsSetting100", "Name": "位置Y腿Y绑定"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBrowLForm3"}, "VertexIndex": 1, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.5, "Delay": 1, "Acceleration": 2, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBrowLY3"}, "VertexIndex": 1, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.5, "Delay": 1, "Acceleration": 2, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBrowLAngle3"}, "VertexIndex": 1, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.5, "Delay": 1, "Acceleration": 2, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBrowRForm3"}, "VertexIndex": 1, "Scale": 45, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.5, "Delay": 1, "Acceleration": 2, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 40, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyelashL"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamEyelashL2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.8, "Delay": 0.8, "Acceleration": 0.78, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 0.78, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 40, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyelashL3"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamEyelashL4"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.8, "Delay": 0.8, "Acceleration": 0.78, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 0.78, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting7", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 40, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamEyeBallY"}, "Weight": 20, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamPuPilL"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.8, "Delay": 0.8, "Acceleration": 0.78, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 0.78, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting8", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 40, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamEyeBallY"}, "Weight": 20, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamPuPilL2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.8, "Delay": 0.8, "Acceleration": 0.78, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 0.78, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting9", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 50, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeLOC"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.8, "Delay": 0.8, "Acceleration": 0.7, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.8, "Delay": 0.8, "Acceleration": 0.8, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting10", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 50, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeLOC2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.8, "Delay": 0.8, "Acceleration": 0.7, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.8, "Delay": 0.8, "Acceleration": 0.8, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting11", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 40, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamEyeBallY"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeBallX"}, "Weight": 10, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeHighLightL1"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "ParamEyeHighLightLX"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.95, "Acceleration": 0.78, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 0.78, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting12", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 40, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamEyeBallY"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeBallX"}, "Weight": 10, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeHighLightL3"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "ParamEyeHighLightLX2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.95, "Acceleration": 0.78, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 0.78, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting13", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 40, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamEyeBallY"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeBallX"}, "Weight": 10, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeHighLightL2"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting14", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 40, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamEyeBallY"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeBallX"}, "Weight": 10, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeHighLightL4"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting15", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 30, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBrowLForm4"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBrowLY4"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting16", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 30, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBrowLForm5"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBrowLY5"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting17", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 0, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBrowLAngle4"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting18", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 0, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBrowLAngle5"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}, {"Position": {"X": 0, "Y": 16}, "Mobility": 0.93, "Delay": 0.9, "Acceleration": 0.8, "Radius": 8}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting19", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param244"}, "VertexIndex": 2, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param243"}, "VertexIndex": 3, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param242"}, "VertexIndex": 4, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 9}, {"Position": {"X": 0, "Y": 17}, "Mobility": 0.9, "Delay": 0.95, "Acceleration": 0.95, "Radius": 8}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.88, "Delay": 1, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.89, "Delay": 1.05, "Acceleration": 1.05, "Radius": 6}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting20", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 20, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param245"}, "VertexIndex": 2, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param246"}, "VertexIndex": 3, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param247"}, "VertexIndex": 4, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9}, "Mobility": 0.92, "Delay": 0.9, "Acceleration": 0.9, "Radius": 9}, {"Position": {"X": 0, "Y": 17}, "Mobility": 0.9, "Delay": 0.95, "Acceleration": 0.95, "Radius": 8}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.88, "Delay": 1, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.89, "Delay": 1.05, "Acceleration": 1.05, "Radius": 6}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting21", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param239"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.9, "Radius": 9}, {"Position": {"X": 0, "Y": 17}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.95, "Radius": 8}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.88, "Delay": 0.95, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 31}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting22", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 20, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param250"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 9}, "Mobility": 0.92, "Delay": 0.85, "Acceleration": 0.9, "Radius": 9}, {"Position": {"X": 0, "Y": 17}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.95, "Radius": 8}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.88, "Delay": 0.95, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 31}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting23", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param241"}, "VertexIndex": 2, "Scale": 0.3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param240"}, "VertexIndex": 3, "Scale": 0.3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.93, "Delay": 0.93, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 26}, "Mobility": 0.88, "Delay": 0.95, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 33}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting24", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 20, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param248"}, "VertexIndex": 2, "Scale": 0.3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param249"}, "VertexIndex": 3, "Scale": 0.3, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 0.95, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.93, "Delay": 0.93, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 26}, "Mobility": 0.88, "Delay": 0.95, "Acceleration": 1, "Radius": 7}, {"Position": {"X": 0, "Y": 33}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting25", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param156"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param155"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting26", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param228"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param229"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting27", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param157"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param158"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting28", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param230"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param231"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting29", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param159"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param160"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting30", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLForm3"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLY3"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBrowLY6"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBrowLAngle6"}, "VertexIndex": 1, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting31", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 0, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLForm3"}, "Weight": 10, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLY3"}, "Weight": 10, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLAngle3"}, "Weight": 25, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBrowRForm3"}, "Weight": 25, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "Param218"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBrowLForm6"}, "VertexIndex": 2, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting32", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBrowLForm2"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBrowLY2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamBrowLAngle2"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "ParamBrowRForm2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "ParamBrowRY2"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.93, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.89, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting33", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param2"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param3"}, "VertexIndex": 2, "Scale": 0.916, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param4"}, "VertexIndex": 3, "Scale": 0.746, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting34", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param187"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param188"}, "VertexIndex": 2, "Scale": 0.916, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param189"}, "VertexIndex": 3, "Scale": 0.746, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting35", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param5"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param6"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param7"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param8"}, "VertexIndex": 4, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1.05, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 1, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.89, "Delay": 0.95, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.87, "Delay": 0.9, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting36", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param9"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param10"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param11"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param12"}, "VertexIndex": 4, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1.05, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 1, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.89, "Delay": 0.95, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.87, "Delay": 0.9, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting37", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param13"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param14"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param15"}, "VertexIndex": 3, "Scale": 0.823, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param16"}, "VertexIndex": 4, "Scale": 0.6, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.94, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.92, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.88, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting38", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param17"}, "VertexIndex": 1, "Scale": 1.427, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param18"}, "VertexIndex": 2, "Scale": 1.236, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param19"}, "VertexIndex": 3, "Scale": 1.026, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param20"}, "VertexIndex": 4, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.94, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.92, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.9, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.88, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting39", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param81"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param80"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting40", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param83"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param82"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting41", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param98"}, "VertexIndex": 1, "Scale": 1, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param162"}, "VertexIndex": 1, "Scale": 1, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param220"}, "VertexIndex": 1, "Scale": 1, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting42", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLForm3"}, "Weight": 15, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLY3"}, "Weight": 15, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLAngle3"}, "Weight": 15, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBrowRForm3"}, "Weight": 15, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param97"}, "VertexIndex": 1, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1.5, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting43", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLForm3"}, "Weight": 15, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLY3"}, "Weight": 15, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLAngle3"}, "Weight": 15, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBrowRForm3"}, "Weight": 15, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param163"}, "VertexIndex": 1, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1.5, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting44", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLForm3"}, "Weight": 15, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLY3"}, "Weight": 15, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBrowLAngle3"}, "Weight": 15, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBrowRForm3"}, "Weight": 15, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param221"}, "VertexIndex": 1, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1.5, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting45", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param97"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param96"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param93"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param88"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param92"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param99"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 0.97, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.87, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting46", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param163"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param164"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param167"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param168"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param169"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param161"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 0.97, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.87, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting47", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param163"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param222"}, "VertexIndex": 1, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param225"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param226"}, "VertexIndex": 2, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param227"}, "VertexIndex": 2, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 0.97, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.87, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting48", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param97"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param100"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param101"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param102"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 0.97, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.87, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting49", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param97"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param170"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param171"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param172"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 0.97, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.87, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting50", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param97"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param108"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param109"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param110"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 0.97, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.87, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting51", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param97"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param178"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param171"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param180"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 0.97, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.87, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting52", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param97"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param103"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param104"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param105"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param106"}, "VertexIndex": 4, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 0.97, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.87, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting53", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param97"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param173"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param174"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param175"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param176"}, "VertexIndex": 4, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 0.97, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.87, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting54", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param97"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param111"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param112"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param113"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param114"}, "VertexIndex": 4, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 0.97, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.87, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting55", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param97"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param181"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param182"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param175"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param184"}, "VertexIndex": 4, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 0.97, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.87, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting56", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param97"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param115"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param116"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.93, "Delay": 0.97, "Acceleration": 0.95, "Radius": 7}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 9}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting57", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param97"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param185"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param186"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 8}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.93, "Delay": 0.97, "Acceleration": 0.95, "Radius": 7}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 10}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 9}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting58", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param96"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param94"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting59", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param164"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param165"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param223"}, "VertexIndex": 2, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting60", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param96"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param95"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting61", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param164"}, "Weight": 30, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param166"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param224"}, "VertexIndex": 2, "Scale": 0.7, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting62", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param97"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param122"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param123"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param124"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 0.97, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.89, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.87, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting63", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param97"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param91"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param117"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param118"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param119"}, "VertexIndex": 4, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 0.97, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.89, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.87, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting64", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param97"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param120"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param121"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param126"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param127"}, "VertexIndex": 4, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 0.97, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.89, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.87, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting65", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param97"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param129"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param130"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 0.97, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.89, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.87, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting66", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param97"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param132"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param133"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 0.97, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.89, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.87, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting67", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param97"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param125"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param89"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param90"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 0.97, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.89, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.87, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting68", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param97"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param144"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param145"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 0.97, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.89, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.87, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting69", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param97"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param142"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param143"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 0.97, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.89, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.87, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting70", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param21"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param22"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param23"}, "VertexIndex": 3, "Scale": 0.916, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param24"}, "VertexIndex": 4, "Scale": 0.746, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting71", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param26"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param27"}, "VertexIndex": 3, "Scale": 0.916, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param28"}, "VertexIndex": 4, "Scale": 0.746, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting72", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param25"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param30"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param31"}, "VertexIndex": 3, "Scale": 0.916, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param32"}, "VertexIndex": 4, "Scale": 0.746, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting73", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param33"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param34"}, "VertexIndex": 2, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param35"}, "VertexIndex": 3, "Scale": 0.916, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param36"}, "VertexIndex": 4, "Scale": 0.746, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting74", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param41"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param42"}, "VertexIndex": 2, "Scale": 0.826, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param43"}, "VertexIndex": 3, "Scale": 0.792, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param44"}, "VertexIndex": 4, "Scale": 0.613, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting75", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param190"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param191"}, "VertexIndex": 2, "Scale": 0.826, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param192"}, "VertexIndex": 3, "Scale": 0.792, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param193"}, "VertexIndex": 4, "Scale": 0.613, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.91, "Delay": 0.94, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.9, "Delay": 0.91, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting76", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param46"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param47"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param48"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.89, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.87, "Delay": 0.93, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting77", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param50"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param51"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param52"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.89, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.87, "Delay": 0.93, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting78", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param53"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param54"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param55"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param56"}, "VertexIndex": 4, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.93, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.91, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.89, "Delay": 0.93, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.87, "Delay": 0.9, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting79", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param57"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param58"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param59"}, "VertexIndex": 4, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param60"}, "VertexIndex": 5, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.93, "Delay": 1.2, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 29}, "Mobility": 0.91, "Delay": 1.15, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 37}, "Mobility": 0.89, "Delay": 1.1, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 44}, "Mobility": 0.88, "Delay": 1.05, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting80", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param61"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.89, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.87, "Delay": 0.93, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting81", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 80, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param63"}, "VertexIndex": 1, "Scale": 1.535, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param64"}, "VertexIndex": 2, "Scale": 1.086, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 1.2, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.89, "Delay": 1.15, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.87, "Delay": 1.1, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.85, "Delay": 1.05, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting82", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 80, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param65"}, "VertexIndex": 1, "Scale": 1.377, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param66"}, "VertexIndex": 2, "Scale": 1.15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param67"}, "VertexIndex": 1, "Scale": 1.377, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 1.2, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.89, "Delay": 1.15, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.87, "Delay": 1.1, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.85, "Delay": 1.05, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting83", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param62"}, "VertexIndex": 1, "Scale": 1.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.89, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.87, "Delay": 0.93, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting84", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 15, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param72"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param73"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param74"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param75"}, "VertexIndex": 4, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.89, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.87, "Delay": 0.93, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting85", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 5, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param77"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param78"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param79"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.89, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.87, "Delay": 0.93, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting86", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param75"}, "Weight": 50, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param69"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param68"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.89, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.87, "Delay": 0.93, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting87", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param79"}, "Weight": 50, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param76"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.89, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.87, "Delay": 0.93, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting88", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param79"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param150"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param151"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param146"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param147"}, "VertexIndex": 4, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.91, "Delay": 1, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 19}, "Mobility": 0.89, "Delay": 0.97, "Acceleration": 0.95, "Radius": 9}, {"Position": {"X": 0, "Y": 27}, "Mobility": 0.87, "Delay": 0.93, "Acceleration": 0.9, "Radius": 8}, {"Position": {"X": 0, "Y": 34}, "Mobility": 0.85, "Delay": 0.9, "Acceleration": 0.85, "Radius": 7}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting89", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param79"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param152"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param153"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param148"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param149"}, "VertexIndex": 4, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 11}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 1, "Radius": 11}, {"Position": {"X": 0, "Y": 21}, "Mobility": 0.89, "Delay": 0.93, "Acceleration": 0.95, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.87, "Delay": 0.91, "Acceleration": 0.9, "Radius": 9}, {"Position": {"X": 0, "Y": 38}, "Mobility": 0.85, "Delay": 0.89, "Acceleration": 0.85, "Radius": 8}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting90", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param79"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param154"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.95, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.89, "Delay": 0.93, "Acceleration": 0.95, "Radius": 10}, {"Position": {"X": 0, "Y": 24}, "Mobility": 0.87, "Delay": 0.91, "Acceleration": 0.9, "Radius": 9}, {"Position": {"X": 0, "Y": 32}, "Mobility": 0.85, "Delay": 0.89, "Acceleration": 0.85, "Radius": 8}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting91", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 0, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBreath"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param79"}, "Weight": 0, "Type": "X", "Reflect": false}], "Output": [], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 11}, "Mobility": 0.91, "Delay": 0.95, "Acceleration": 0.95, "Radius": 11}, {"Position": {"X": 0, "Y": 21}, "Mobility": 0.89, "Delay": 0.93, "Acceleration": 0.95, "Radius": 10}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.87, "Delay": 0.91, "Acceleration": 0.9, "Radius": 9}, {"Position": {"X": 0, "Y": 38}, "Mobility": 0.85, "Delay": 0.89, "Acceleration": 0.85, "Radius": 8}], "Normalization": {"Position": {"Minimum": -15, "Default": 0, "Maximum": 15}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting92", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting93", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 100, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamAngleY2"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 1, "Acceleration": 0.8, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting94", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamAngleZ2"}, "VertexIndex": 1, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 0.8, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting95", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param98"}, "VertexIndex": 1, "Scale": 5, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "Param162"}, "VertexIndex": 1, "Scale": 5, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param86"}, "VertexIndex": 1, "Scale": 5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": true}, {"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleZ2"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param84"}, "VertexIndex": 1, "Scale": 10, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param220"}, "VertexIndex": 1, "Scale": 5, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting96", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting97", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param86"}, "VertexIndex": 1, "Scale": 8, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting98", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param84"}, "VertexIndex": 1, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting99", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param87"}, "VertexIndex": 1, "Scale": 6, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting100", "Input": [{"Source": {"Target": "Parameter", "Id": "Param87"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param85"}, "VertexIndex": 1, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.01, "Delay": 10, "Acceleration": 10, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}]}