export default eventHandler(async (event) => {
  return useResponseSuccess([
    {
      dictCode: 1,
      dictLabel: '管理员信息',
      dictValue: '0',
      dictType: 'new_type',
      dictSort: 1,
      status: '0',
      remark: '管理员发送的系统信息',
      createTime: '2024-01-01 00:00:00',
    },
    {
      dictCode: 2,
      dictLabel: '论坛信息',
      dictValue: '1',
      dictType: 'new_type',
      dictSort: 2,
      status: '0',
      remark: '论坛相关的通知信息',
      createTime: '2024-01-01 00:00:00',
    },
    {
      dictCode: 3,
      dictLabel: '私聊信息',
      dictValue: '2',
      dictType: 'new_type',
      dictSort: 3,
      status: '0',
      remark: '用户之间的私聊消息',
      createTime: '2024-01-01 00:00:00',
    },
  ]);
});
