// Live2D 相关类型定义

export interface Live2DConfig {
  modelPath: string
  width?: number
  height?: number
  scale?: number
  position?: { x: number; y: number }
  autoPlay?: boolean
  showToolbar?: boolean
}

export interface Live2DMessage {
  text: string
  duration?: number
  type?: 'info' | 'success' | 'warning' | 'error'
}

export interface Live2DModel {
  id: string
  name: string
  path: string
  preview?: string
  textures?: string[]
}

export interface Live2DMotion {
  name: string
  file: string
  fadeIn?: number
  fadeOut?: number
}

export interface Live2DExpression {
  name: string
  file: string
}

// PIXI Live2D Display 扩展
declare global {
  interface Window {
    PIXI: any
  }
}

export interface Live2DDisplayModel {
  scale: { set: (value: number) => void }
  position: { set: (x: number, y: number) => void }
  interactive: boolean
  buttonMode: boolean
  internalModel: {
    motionManager?: any
    textures?: any[]
  }
  motion: (name: string) => void
  hitTest: (area: string, x: number, y: number) => boolean
  on: (event: string, handler: Function) => void
  destroy: () => void
}
