<script setup lang="ts">
import { onUnmounted } from 'vue';

import { Page } from '@vben/common-ui';

import { emitter } from './mitt';
import NovalDetailsPanel from './novalDetails/index.vue';
import NovalDirectoryPanel from './novalDirectory/index.vue';

onUnmounted(() => emitter.off('rowClick'));
</script>

<template>
  <Page
    :auto-content-height="true"
    content-class="flex flex-col lg:flex-row gap-4"
  >
    <NovalDetailsPanel class="flex-1 overflow-hidden" />
    <NovalDirectoryPanel class="flex-1 overflow-hidden" />
  </Page>
</template>
