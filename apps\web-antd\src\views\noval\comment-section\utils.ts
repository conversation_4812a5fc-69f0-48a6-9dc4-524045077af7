import { buildShortUUID } from '@vben/utils';

import { commentInfo } from '#/api/noval/comment';

/**
 * 生成唯一的评论ID
 * 确保与数据库中的ID不重复
 */
export async function generateUniqueCommentId(prefix = 'comment'): Promise<string> {
  let attempts = 0;
  const maxAttempts = 10;

  while (attempts < maxAttempts) {
    const id = buildShortUUID(prefix);
    
    try {
      // 尝试查询这个ID是否已存在
      await commentInfo(id);
      // 如果查询成功，说明ID已存在，需要重新生成
      attempts++;
      continue;
    } catch (error) {
      // 如果查询失败（404等），说明ID不存在，可以使用
      return id;
    }
  }

  // 如果尝试多次都失败，使用时间戳作为后缀确保唯一性
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 生成楼层号
 * 根据现有评论数量计算下一个楼层号
 */
export function generateFloorNumber(existingComments: any[]): number {
  if (!existingComments || existingComments.length === 0) {
    return 1;
  }

  // 找到最大的楼层号
  const maxFloor = Math.max(...existingComments.map(comment => comment.floor || 0));
  return maxFloor + 1;
}

/**
 * 格式化评论数据
 * 为评论添加必要的字段和格式化
 */
export function formatCommentData(comment: any, options: {
  isOwner?: boolean;
  floor?: number;
  likeCount?: number;
  dislikeCount?: number;
} = {}) {
  return {
    ...comment,
    floor: options.floor || 1,
    likeCount: options.likeCount || Math.floor(Math.random() * 50),
    dislikeCount: options.dislikeCount || Math.floor(Math.random() * 5),
    createTime: comment.createTime || new Date().toISOString(),
  };
}

/**
 * 验证评论内容
 */
export function validateCommentContent(content: string): {
  valid: boolean;
  message?: string;
} {
  if (!content || !content.trim()) {
    return {
      valid: false,
      message: '评论内容不能为空',
    };
  }

  if (content.length > 1000) {
    return {
      valid: false,
      message: '评论内容不能超过1000个字符',
    };
  }

  // 简单的敏感词检测（实际项目中应该使用更完善的过滤系统）
  const sensitiveWords = ['垃圾', '傻逼', '操你妈'];
  const hasSensitiveWord = sensitiveWords.some(word => content.includes(word));
  
  if (hasSensitiveWord) {
    return {
      valid: false,
      message: '评论内容包含敏感词，请修改后重试',
    };
  }

  return {
    valid: true,
  };
}

/**
 * 构建评论树形结构
 * 将扁平的评论数据构建成树形结构
 */
export function buildCommentTree(comments: any[]): any[] {
  if (!comments || comments.length === 0) {
    return [];
  }

  // 分离不同类型的评论
  const ownerPosts = comments.filter(c => c.commerntType === 0);
  const mainReplies = comments.filter(c => c.commerntType === 1 && c.isResponse === 0);
  const subReplies = comments.filter(c => c.commerntType === 1 && c.isResponse === 1);

  // 构建回复映射
  const replyMap = new Map<string | number, any[]>();
  subReplies.forEach(reply => {
    if (!replyMap.has(reply.responseId)) {
      replyMap.set(reply.responseId, []);
    }
    replyMap.get(reply.responseId)?.push(reply);
  });

  // 为主回复添加子回复
  mainReplies.forEach(reply => {
    reply.children = replyMap.get(reply.myId) || [];
    // 按时间排序子回复
    reply.children.sort((a: any, b: any) => 
      new Date(a.createTime || 0).getTime() - new Date(b.createTime || 0).getTime()
    );
  });

  // 合并所有评论并按楼层排序
  const allComments = [...ownerPosts, ...mainReplies];
  return allComments.sort((a, b) => (a.floor || 0) - (b.floor || 0));
}

/**
 * 获取用户等级标识
 * 根据用户的发帖数量等信息计算等级
 */
export function getUserLevel(postCount: number = 0): {
  level: number;
  title: string;
  color: string;
} {
  if (postCount >= 1000) {
    return { level: 6, title: '大神', color: '#f50' };
  } else if (postCount >= 500) {
    return { level: 5, title: '高手', color: '#fa8c16' };
  } else if (postCount >= 100) {
    return { level: 4, title: '老手', color: '#faad14' };
  } else if (postCount >= 50) {
    return { level: 3, title: '熟手', color: '#52c41a' };
  } else if (postCount >= 10) {
    return { level: 2, title: '新手', color: '#1890ff' };
  } else {
    return { level: 1, title: '萌新', color: '#722ed1' };
  }
}

/**
 * 计算相对时间
 * 将时间转换为相对时间显示（如：3分钟前、1小时前等）
 */
export function getRelativeTime(dateTime: string | Date): string {
  const now = new Date();
  const time = new Date(dateTime);
  const diff = now.getTime() - time.getTime();

  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;
  const week = 7 * day;
  const month = 30 * day;
  const year = 365 * day;

  if (diff < minute) {
    return '刚刚';
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`;
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`;
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`;
  } else if (diff < month) {
    return `${Math.floor(diff / week)}周前`;
  } else if (diff < year) {
    return `${Math.floor(diff / month)}个月前`;
  } else {
    return `${Math.floor(diff / year)}年前`;
  }
}
