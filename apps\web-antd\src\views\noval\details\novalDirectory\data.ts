import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { DirectoryVO } from '#/api/noval/details/noval-directory-model';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'chapter',
    label: '搜索章节',
  },
];

export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '第X章',
    field: 'chapter',
  },
  {
    title: '章节名称',
    field: 'chaptername',
  },
  {
    title: '链接',
    field: 'link',
  },
  {
    title: '创建时间',
    field: 'createTime',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const drawerSchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    fieldName: 'id',
  },
  {
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    fieldName: 'id',
    label: '所属小说id',
  },
  {
    component: 'Input',
    fieldName: 'chaptername',
    label: '章节名称',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'chapter',
    label: '第X章',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'link',
    label: '链接',
    rules: 'required',
  },
];
