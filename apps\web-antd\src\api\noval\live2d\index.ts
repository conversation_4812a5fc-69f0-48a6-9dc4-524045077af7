import type { Live2dForm, Live2dQuery, Live2dVO } from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询Live2d列表
* @param params
* @returns Live2d列表
*/
export function live2dList(params?: Live2dQuery) {
  return requestClient.get<PageResult<Live2dVO>>('/noval/live2d/list', { params });
}

/**
 * 根据名字/类型查询live2d列表(非分页)
 * 当name和type都为null时查询所有数据
 * @param params
 * @returns Live2dVO数组
 */
export function live2dList2(params?: Live2dQuery) {
  return requestClient.get<Live2dVO[]>('/noval/live2d/list2', {
    params,
  });
}

/**
 * 导出Live2d列表
 * @param params
 * @returns Live2d列表
 */
export function live2dExport(params?: Live2dQuery) {
  return commonExport('/noval/live2d/export', params ?? {});
}

/**
 * 查询Live2d详情
 * @param id id
 * @returns Live2d详情
 */
export function live2dInfo(id: ID) {
  return requestClient.get<Live2dVO>(`/noval/live2d/${id}`);
}

/**
 * 新增Live2d
 * @param data
 * @returns void
 */
export function live2dAdd(data: Live2dForm) {
  return requestClient.postWithMsg<void>('/noval/live2d', data);
}

/**
 * 更新Live2d
 * @param data
 * @returns void
 */
export function live2dUpdate(data: Live2dForm) {
  return requestClient.putWithMsg<void>('/noval/live2d', data);
}

/**
 * 根据名字查询Live2d记录
 * 使用统一的list2接口
 * @param name Live2d名字
 * @returns Live2d记录列表
 */
export function live2dListByName(name: string) {
  return requestClient.get<Live2dVO[]>('/noval/live2d/list2', {
    params: {
      name,
      type: null,
    },
  });
}

/**
 * 删除Live2d
 * @param id id
 * @returns void
 */
export function live2dRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/noval/live2d/${id}`);
}

/**
 * 根据名字删除所有同名Live2d记录
 * @param names Live2d名字数组
 * @returns void
 */
export async function live2dRemoveByNames(names: string[]) {
  try {
    console.log('🗑️ 开始删除Live2D记录:', names);

    // 先尝试使用后端批量删除API
    try {
      const result = await requestClient.deleteWithMsg<void>(
        '/noval/live2d/removeByName',
        {
          data: names,
        },
      );
      console.log('✅ 批量删除成功');
      return result;
    } catch (batchError) {
      console.warn('⚠️ 批量删除API失败，尝试逐个删除:', batchError);

      // 如果批量删除失败，使用逐个删除作为备用方案
      let successCount = 0;
      let failCount = 0;

      for (const name of names) {
        try {
          // 查询同名记录
          const records = await live2dList2({ name, type: undefined });
          console.log(`📋 找到 "${name}" 的记录数:`, records.length);

          // 逐个删除记录
          for (const record of records) {
            if (record.id) {
              await live2dRemove(record.id);
              successCount++;
            }
          }
          console.log(`✅ 成功删除 "${name}"`);
        } catch (error) {
          console.error(`❌ 删除 "${name}" 失败:`, error);
          failCount++;
        }
      }

      if (failCount > 0) {
        throw new Error(`部分删除失败：成功${successCount}个，失败${failCount}个`);
      }

      console.log(`✅ 逐个删除完成，共删除${successCount}个记录`);
      return { success: true, count: successCount };
    }
  } catch (error) {
    console.error('❌ 删除失败:', error);
    throw error;
  }
}

/**
 * 根据单个名字删除所有同名Live2d记录
 * @param name Live2d名字
 * @returns void
 */
export function live2dRemoveByName(name: string) {
  return live2dRemoveByNames([name]);
}
