import type { Live2dForm, Live2dQuery, Live2dVO } from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询Live2d列表
* @param params
* @returns Live2d列表
*/
export function live2dList(params?: Live2dQuery) {
  return requestClient.get<PageResult<Live2dVO>>('/noval/live2d/list', { params });
}

/**
 * 根据名字/类型查询live2d列表(非分页)
 * 当name和type都为null时查询所有数据
 * @param params
 * @returns Live2dVO数组
 */
export function live2dList2(params?: Live2dQuery) {
  return requestClient.get<Live2dVO[]>('/noval/live2d/list2', {
    params,
  });
}

/**
 * 导出Live2d列表
 * @param params
 * @returns Live2d列表
 */
export function live2dExport(params?: Live2dQuery) {
  return commonExport('/noval/live2d/export', params ?? {});
}

/**
 * 查询Live2d详情
 * @param id id
 * @returns Live2d详情
 */
export function live2dInfo(id: ID) {
  return requestClient.get<Live2dVO>(`/noval/live2d/${id}`);
}

/**
 * 新增Live2d
 * @param data
 * @returns void
 */
export function live2dAdd(data: Live2dForm) {
  return requestClient.postWithMsg<void>('/noval/live2d', data);
}

/**
 * 更新Live2d
 * @param data
 * @returns void
 */
export function live2dUpdate(data: Live2dForm) {
  return requestClient.putWithMsg<void>('/noval/live2d', data);
}

/**
 * 根据名字查询Live2d记录
 * 使用统一的list2接口
 * @param name Live2d名字
 * @returns Live2d记录列表
 */
export function live2dListByName(name: string) {
  return requestClient.get<Live2dVO[]>('/noval/live2d/list2', {
    params: {
      name,
      type: null,
    },
  });
}

/**
 * 删除Live2d
 * @param id id
 * @returns void
 */
export function live2dRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/noval/live2d/${id}`);
}

/**
 * 根据名字删除所有同名Live2d记录
 * @param names Live2d名字数组
 * @returns void
 */
export async function live2dRemoveByNames(names: string[]) {
  try {
    console.log('🗑️ [DEBUG] 开始删除Live2D记录:', names);
    console.log('🗑️ [DEBUG] 删除的名称数量:', names.length);

    // 1. 先查询所有同名记录，收集所有ID
    const allIds: number[] = [];
    const nameToIds: Record<string, number[]> = {};

    for (const name of names) {
      try {
        console.log(`🔍 [DEBUG] 正在查询名称: "${name}"`);
        const records = await live2dList2({ name, type: undefined });
        console.log(`📋 [DEBUG] 找到 "${name}" 的记录数:`, records.length);
        console.log(`📋 [DEBUG] "${name}" 的记录详情:`, records);

        nameToIds[name] = [];
        for (const record of records) {
          if (record.id) {
            const id = Number(record.id);
            allIds.push(id);
            nameToIds[name].push(id);
            console.log(`📝 [DEBUG] 添加ID: ${id} (来自 "${name}")`);
          }
        }
      } catch (error) {
        console.error(`❌ [DEBUG] 查询 "${name}" 失败:`, error);
      }
    }

    console.log(`📊 [DEBUG] 名称到ID的映射:`, nameToIds);
    console.log(`📊 [DEBUG] 总共找到 ${allIds.length} 条记录，所有ID:`, allIds);

    if (allIds.length === 0) {
      console.log('⚠️ [DEBUG] 没有找到要删除的记录');
      return { success: true, count: 0 };
    }

    // 2. 使用后端批量删除API，传入所有ID
    const deleteUrl = `/noval/live2d/${allIds.join(',')}`;
    console.log(`🌐 [DEBUG] 删除请求URL:`, deleteUrl);

    // 获取当前的Authorization头
    const { useAccessStore } = await import('@vben/stores');
    const accessStore = useAccessStore();
    const token = accessStore.accessToken;
    console.log(`🔑 [DEBUG] AccessStore Token:`, token ? `Bearer ${token}` : '未找到token');
    console.log(`🔑 [DEBUG] AccessStore状态:`, {
      hasToken: !!token,
      tokenLength: token?.length || 0,
      isAccessChecked: accessStore.isAccessChecked,
    });

    try {
      console.log(`🚀 [DEBUG] 开始发送删除请求...`);
      const result = await requestClient.deleteWithMsg<void>(deleteUrl);
      console.log(`✅ [DEBUG] 批量删除成功，删除了 ${allIds.length} 条记录`);
      console.log(`✅ [DEBUG] 删除响应:`, result);
      return result;
    } catch (error: any) {
      console.error('❌ [DEBUG] 批量删除失败:', error);
      console.error('❌ [DEBUG] 错误详情:', {
        message: error?.message || '未知错误',
        status: error?.status || '未知状态',
        statusText: error?.statusText || '未知状态文本',
        response: error?.response || '无响应数据',
        url: deleteUrl,
        ids: allIds,
      });
      throw error;
    }
  } catch (error) {
    console.error('❌ 删除失败:', error);
    throw error;
  }
}

/**
 * 根据单个名字删除所有同名Live2d记录
 * @param name Live2d名字
 * @returns void
 */
export function live2dRemoveByName(name: string) {
  return live2dRemoveByNames([name]);
}
