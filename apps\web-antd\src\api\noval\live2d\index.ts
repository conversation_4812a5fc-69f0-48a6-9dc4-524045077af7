import type { Live2dForm, Live2dQuery, Live2dVO } from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询Live2d列表
* @param params
* @returns Live2d列表
*/
export function live2dList(params?: Live2dQuery) {
  return requestClient.get<PageResult<Live2dVO>>('/noval/live2d/list', { params });
}

/**
 * 根据名字/类型查询live2d列表(非分页)
 * 当name和type都为null时查询所有数据
 * @param params
 * @returns Live2dVO数组
 */
export async function live2dList2(params?: Live2dQuery) {
  try {
    console.log('🔍 [LIST2-DEBUG] 开始查询Live2D记录:', params);
    console.log('🔍 [LIST2-DEBUG] 查询URL: /noval/live2d/list2');
    console.log('🔍 [LIST2-DEBUG] 查询参数:', params);

    const result = await requestClient.get<Live2dVO[]>('/noval/live2d/list2', {
      params,
      errorMessageMode: 'none', // 禁用自动错误提示，我们手动处理
    });

    console.log('✅ [LIST2-DEBUG] 查询成功，结果:', result);
    console.log('✅ [LIST2-DEBUG] 记录数量:', result?.length || 0);
    return result;
  } catch (error: any) {
    console.error('❌ [LIST2-DEBUG] 查询失败:', error);
    console.error('❌ [LIST2-DEBUG] 错误详情:', {
      message: error?.message || '未知错误',
      status: error?.status || '未知状态',
      statusText: error?.statusText || '未知状态文本',
      response: error?.response || '无响应数据',
      responseData: error?.response?.data || '无响应数据',
      responseStatus: error?.response?.status || '无响应状态',
      responseHeaders: error?.response?.headers || '无响应头',
      config: error?.config || '无请求配置',
      url: '/noval/live2d/list2',
      params,
    });

    // 如果有响应数据，尝试解析具体的错误信息
    if (error?.response?.data) {
      console.error('❌ [LIST2-DEBUG] 后端响应数据:', error.response.data);
    }

    throw error;
  }
}

/**
 * 导出Live2d列表
 * @param params
 * @returns Live2d列表
 */
export function live2dExport(params?: Live2dQuery) {
  return commonExport('/noval/live2d/export', params ?? {});
}

/**
 * 查询Live2d详情
 * @param id id
 * @returns Live2d详情
 */
export function live2dInfo(id: ID) {
  return requestClient.get<Live2dVO>(`/noval/live2d/${id}`);
}

/**
 * 新增Live2d
 * @param data
 * @returns void
 */
export function live2dAdd(data: Live2dForm) {
  return requestClient.postWithMsg<void>('/noval/live2d', data);
}

/**
 * 更新Live2d
 * @param data
 * @returns void
 */
export function live2dUpdate(data: Live2dForm) {
  return requestClient.putWithMsg<void>('/noval/live2d', data);
}

/**
 * 根据名字查询Live2d记录
 * 使用统一的list2接口
 * @param name Live2d名字
 * @returns Live2d记录列表
 */
export function live2dListByName(name: string) {
  return requestClient.get<Live2dVO[]>('/noval/live2d/list2', {
    params: {
      name,
      type: null,
    },
  });
}

/**
 * 删除Live2d
 * @param id id
 * @returns void
 */
export function live2dRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/noval/live2d/${id}`);
}

/**
 * 根据名字删除所有同名Live2d记录
 * @param names Live2d名字数组
 * @returns void
 */
export async function live2dRemoveByNames(names: string[]) {
  try {
    console.log('🗑️ [DEBUG] 开始删除Live2D记录:', names);
    console.log('🗑️ [DEBUG] 删除的名称数量:', names.length);

    // 1. 先查询所有同名记录，收集所有ID
    const allIds: number[] = [];
    const nameToIds: Record<string, number[]> = {};

    for (const name of names) {
      try {
        console.log(`🔍 [DEBUG] 正在查询名称: "${name}"`);
        // 使用标准的分页查询API，设置大的pageSize获取所有数据
        const result = await live2dList({
          pageNum: 1,
          pageSize: 10_000,
          name,
        });
        const records = result.rows || [];
        console.log(`📋 [DEBUG] 找到 "${name}" 的记录数:`, records.length);
        console.log(`📋 [DEBUG] "${name}" 的记录详情:`, records);

        nameToIds[name] = [];
        for (const record of records) {
          if (record.id) {
            const id = Number(record.id);
            allIds.push(id);
            nameToIds[name].push(id);
            console.log(`📝 [DEBUG] 添加ID: ${id} (来自 "${name}")`);
          }
        }
      } catch (error) {
        console.error(`❌ [DEBUG] 查询 "${name}" 失败:`, error);
      }
    }

    console.log(`📊 [DEBUG] 名称到ID的映射:`, nameToIds);
    console.log(`📊 [DEBUG] 总共找到 ${allIds.length} 条记录，所有ID:`, allIds);

    if (allIds.length === 0) {
      console.log('⚠️ [DEBUG] 没有找到要删除的记录');
      return { success: true, count: 0 };
    }

    // 2. 使用后端批量删除API，传入所有ID
    const deleteUrl = `/noval/live2d/${allIds.join(',')}`;
    console.log(`🌐 [DEBUG] 删除请求URL:`, deleteUrl);

    // 获取当前的Authorization头
    const { useAccessStore } = await import('@vben/stores');
    const accessStore = useAccessStore();
    const token = accessStore.accessToken;
    console.log(`🔑 [DEBUG] AccessStore Token:`, token ? `Bearer ${token}` : '未找到token');
    console.log(`🔑 [DEBUG] AccessStore状态:`, {
      hasToken: !!token,
      tokenLength: token?.length || 0,
      isAccessChecked: accessStore.isAccessChecked,
    });

    try {
      console.log(`🚀 [DEBUG] 开始发送删除请求...`);
      console.log(`🚀 [DEBUG] 请求详情:`, {
        method: 'DELETE',
        url: deleteUrl,
        ids: allIds,
        idsCount: allIds.length,
      });

      // 使用原始的requestClient来获取更详细的响应信息
      const result = await requestClient.delete(deleteUrl, {
        errorMessageMode: 'none', // 禁用自动错误提示，我们手动处理
      });

      console.log(`✅ [DEBUG] 批量删除成功，删除了 ${allIds.length} 条记录`);
      console.log(`✅ [DEBUG] 删除响应:`, result);
      return result;
    } catch (error: any) {
      console.error('❌ [DEBUG] 批量删除失败:', error);
      console.error('❌ [DEBUG] 错误详情:', {
        message: error?.message || '未知错误',
        status: error?.status || '未知状态',
        statusText: error?.statusText || '未知状态文本',
        response: error?.response || '无响应数据',
        responseData: error?.response?.data || '无响应数据',
        responseStatus: error?.response?.status || '无响应状态',
        responseHeaders: error?.response?.headers || '无响应头',
        config: error?.config || '无请求配置',
        url: deleteUrl,
        ids: allIds,
      });

      // 如果有响应数据，尝试解析具体的错误信息
      if (error?.response?.data) {
        console.error('❌ [DEBUG] 后端响应数据:', error.response.data);
      }

      throw error;
    }
  } catch (error) {
    console.error('❌ 删除失败:', error);
    throw error;
  }
}

/**
 * 根据单个名字删除所有同名Live2d记录
 * @param name Live2d名字
 * @returns void
 */
export function live2dRemoveByName(name: string) {
  return live2dRemoveByNames([name]);
}
