<script setup lang="ts">
import type { Recordable } from '@vben/types';

import { ref } from 'vue';

import { Page, useVbenModal, type VbenFormProps } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';
import { getVxePopupContainer } from '@vben/utils';

import { Modal, Popconfirm, Space, message } from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  useVbenVxeGrid,
  vxeCheckboxChecked,
  type VxeGridProps
} from '#/adapter/vxe-table';

import {
  commentExport,
  commentList,
  commentRemove,
} from '#/api/noval/comment';
import type { CommentForm } from '#/api/noval/comment/model';
import { commonDownloadExcel } from '#/utils/file/download';
import {
  deleteRecordWithFiles,
  deleteRecordsWithFiles,
  extractCommentUrls
} from '#/utils/file-delete-helper';

import commentModal from './comment-modal.vue';
import { columns, querySchema } from './data';

// 获取用户store
const userStore = useUserStore();

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  // 处理区间选择器RangePicker时间格式 将一个字段映射为两个字段 搜索/导出会用到
  // 不需要直接删除
  // fieldMappingTime: [
  //  [
  //    'createTime',
  //    ['params[beginTime]', 'params[endTime]'],
  //    ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
  //  ],
  // ],
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    // trigger: 'row',
  },
  // 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
  // columns: columns(),
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        // 获取当前登录用户的ID
        const currentUserId = userStore.userInfo?.userId;

        return await commentList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          myId: currentUserId, // 自动添加当前用户ID
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'commerntId',
  },
  // 表格全局唯一表示 保存列配置需要用到
  id: 'noval-comment-index'
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [CommentModal, modalApi] = useVbenModal({
  connectedComponent: commentModal,
});

async function handleDelete(row: Required<CommentForm>) {
  try {
    await deleteRecordWithFiles(
      row,
      commentRemove,
      extractCommentUrls
    );
    message.success('删除成功，已同时删除关联图片');
    await tableApi.query();
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败');
  }
}

function handleMultiDelete() {
  const rows = tableApi.grid.getCheckboxRecords();
  const ids = rows.map((row: Required<CommentForm>) => row.commerntId);
  Modal.confirm({
    title: '批量删除确认',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条评论吗？\n\n⚠️ 注意：此操作将同时删除评论记录和关联的图片文件。`,
    okText: '确认删除',
    cancelText: '取消',
    onOk: async () => {
      try {
        await deleteRecordsWithFiles(
          rows,
          commentRemove,
          extractCommentUrls
        );
        message.success(`成功删除${rows.length}条评论及关联图片`);
        await tableApi.query();
      } catch (error) {
        console.error('批量删除失败:', error);
        message.error('批量删除失败');
      }
    },
  });
}

function handleDownloadExcel() {
  // 获取当前登录用户的ID
  const currentUserId = userStore.userInfo?.userId;

  // 在导出参数中添加当前用户ID
  const exportParams = {
    ...tableApi.formApi.form.values,
    myId: currentUserId,
  };

  commonDownloadExcel(commentExport, '评论数据', exportParams, {
    fieldMappingTime: formOptions.fieldMappingTime,
  });
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="评论列表">
      <template #toolbar-tools>
        <Space>
          <a-button
            v-access:code="['noval:comment:export']"
            @click="handleDownloadExcel"
          >
            {{ $t('pages.common.export') }}
          </a-button>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['noval:comment:remove']"
            @click="handleMultiDelete">
            {{ $t('pages.common.delete') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <ghost-button
              danger
              v-access:code="['noval:comment:remove']"
              @click.stop=""
            >
              {{ $t('pages.common.delete') }}
            </ghost-button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <CommentModal @reload="tableApi.query()" />
  </Page>
</template>
