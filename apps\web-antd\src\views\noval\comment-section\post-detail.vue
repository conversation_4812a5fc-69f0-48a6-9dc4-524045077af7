<script setup lang="ts">
import type { CommentVO, CommentForm } from '#/api/noval/comment/model';

import { computed, onMounted, ref } from 'vue';

import { useUserStore } from '@vben/stores';
import { formatDateTime } from '@vben/utils';

import {
  Avatar,
  Button,
  Card,
  Input,
  List,
  message,
  Space,
  Spin,
  Badge,
  Divider,
  Breadcrumb
} from 'ant-design-vue';
import {
  UserOutlined,
  MessageOutlined,
  SendOutlined,
  LikeOutlined,
  DislikeOutlined,
  ArrowLeftOutlined,
  HomeOutlined
} from '@ant-design/icons-vue';

import { commentAdd, commentList, commentInfo } from '#/api/noval/comment';
import { findUserInfo } from '#/api/system/user';

interface CommentWithUser extends CommentVO {
  userName?: string;
  userAvatar?: string;
  createTime?: string;
  children?: CommentWithUser[];
  likeCount?: number;
  dislikeCount?: number;
  floor?: number;
}

interface Props {
  // 帖子ID
  postId: string | number;
  // 贴吧名称
  forumName?: string;
  // 是否只读模式
  readonly?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  forumName: '小说讨论吧',
  readonly: false,
});

const emit = defineEmits<{
  back: [];
}>();

const userStore = useUserStore();
const loading = ref(false);
const submitting = ref(false);
const postDetail = ref<CommentWithUser | null>(null);
const replies = ref<CommentWithUser[]>([]);
const newReply = ref('');
const replyingTo = ref<number | string | null>(null);
const replyContent = ref('');
const totalReplies = ref(0);

// 用户信息缓存
const userCache = ref<Map<string | number, { userName: string; userAvatar: string }>>(new Map());

// 当前用户信息
const currentUser = computed(() => userStore.userInfo);

// 获取用户信息
async function getUserInfo(userId: string | number) {
  if (userCache.value.has(userId)) {
    return userCache.value.get(userId);
  }

  try {
    const userInfo = await findUserInfo(userId);
    const userData = {
      userName: userInfo.user?.nickName || userInfo.user?.userName || '未知用户',
      userAvatar: userInfo.user?.avatar || '',
    };
    userCache.value.set(userId, userData);
    return userData;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      userName: '未知用户',
      userAvatar: '',
    };
  }
}

// 加载帖子详情和回复
async function loadPostDetail() {
  loading.value = true;
  try {
    // 获取帖子详情（commerntType=0的楼主帖子）
    const post = await commentInfo(props.postId);
    const userInfo = await getUserInfo(post.myId);

    postDetail.value = {
      ...post,
      userName: userInfo?.userName,
      userAvatar: userInfo?.userAvatar,
      likeCount: 0, // 可以从数据库获取
      dislikeCount: 0, // 可以从数据库获取
      floor: 1,
    };

    // 获取该帖子的评论列表（commerntType=1, responseId=帖子ID）
    const response = await commentList({
      pageNum: 1,
      pageSize: 1000,
      commerntType: 1, // 获取评论
      // 这里应该根据 responseId 筛选，但由于API限制，暂时获取所有评论
    });

    const allComments = response.rows || [];

    // 筛选出属于当前帖子的评论
    const postComments = allComments.filter(comment =>
      comment.responseId === props.postId
    );

    // 获取所有用户信息
    const userIds = [...new Set(postComments.map(item => item.myId))];
    await Promise.all(userIds.map(userId => getUserInfo(userId)));

    // 构建评论数据
    const commentsWithUser: CommentWithUser[] = postComments.map((comment, index) => ({
      ...comment,
      userName: userCache.value.get(comment.myId)?.userName,
      userAvatar: userCache.value.get(comment.myId)?.userAvatar,
      createTime: comment.createTime || new Date().toISOString(),
      likeCount: 0, // 可以从数据库获取
      dislikeCount: 0, // 可以从数据库获取
      floor: index + 2, // 从2楼开始
      children: [],
    }));

    // 分离主评论和回复
    const mainComments = commentsWithUser.filter(c => c.isResponse === 0);
    const subReplies = commentsWithUser.filter(c => c.isResponse === 1);

    // 构建回复树（回复的回复）
    const replyMap = new Map<string | number, CommentWithUser[]>();
    subReplies.forEach(reply => {
      if (!replyMap.has(reply.responseId)) {
        replyMap.set(reply.responseId, []);
      }
      replyMap.get(reply.responseId)?.push(reply);
    });

    // 为主评论添加子回复
    mainComments.forEach(comment => {
      // 查找回复该评论的回复（responseId = comment.commerntId）
      comment.children = allComments.filter(reply =>
        reply.isResponse === 1 && reply.responseId === comment.commerntId
      ).map(reply => ({
        ...reply,
        userName: userCache.value.get(reply.myId)?.userName,
        userAvatar: userCache.value.get(reply.myId)?.userAvatar,
        createTime: reply.createTime || new Date().toISOString(),
        likeCount: 0,
        dislikeCount: 0,
      }));

      comment.children.sort((a, b) =>
        new Date(a.createTime || 0).getTime() - new Date(b.createTime || 0).getTime()
      );
    });

    replies.value = mainComments.sort((a, b) => (a.floor || 0) - (b.floor || 0));
    totalReplies.value = postComments.length;
  } catch (error) {
    console.error('加载帖子详情失败:', error);
    message.error('加载帖子详情失败');
  } finally {
    loading.value = false;
  }
}

// 发表回复
async function submitReply() {
  if (!newReply.value.trim()) {
    message.warning('请输入回复内容');
    return;
  }

  if (!currentUser.value?.userId) {
    message.error('请先登录');
    return;
  }

  submitting.value = true;
  try {
    const replyData: CommentForm = {
      // 不传递 commerntId，让后端自动生成
      content: newReply.value.trim(),
      myId: currentUser.value.userId,
      commerntType: 1, // 1代表这是帖子的评论
      isResponse: 0, // 0代表不是回复
      responseId: postDetail.value?.commerntId || 0, // 回复的是帖子ID
    };

    await commentAdd(replyData);
    message.success('回复发表成功');
    newReply.value = '';
    await loadPostDetail();
  } catch (error) {
    console.error('发表回复失败:', error);
    message.error('发表回复失败');
  } finally {
    submitting.value = false;
  }
}

// 回复某个回复
async function submitSubReply(parentReply: CommentWithUser) {
  if (!replyContent.value.trim()) {
    message.warning('请输入回复内容');
    return;
  }

  if (!currentUser.value?.userId) {
    message.error('请先登录');
    return;
  }

  submitting.value = true;
  try {
    const replyData: CommentForm = {
      // 不传递 commerntId，让后端自动生成
      content: replyContent.value.trim(),
      myId: currentUser.value.userId,
      commerntType: 1, // 1代表这是评论
      isResponse: 1, // 1代表是回复的回复
      responseId: parentReply.commerntId, // 回复的是评论ID
    };

    await commentAdd(replyData);
    message.success('回复发表成功');
    replyContent.value = '';
    replyingTo.value = null;
    await loadPostDetail();
  } catch (error) {
    console.error('发表回复失败:', error);
    message.error('发表回复失败');
  } finally {
    submitting.value = false;
  }
}

// 开始回复
function startReply(reply: CommentWithUser) {
  replyingTo.value = reply.commerntId;
  replyContent.value = '';
}

// 取消回复
function cancelReply() {
  replyingTo.value = null;
  replyContent.value = '';
}

// 点赞
function handleLike(comment: CommentWithUser) {
  comment.likeCount = (comment.likeCount || 0) + 1;
  message.success('点赞成功');
}

// 点踩
function handleDislike(comment: CommentWithUser) {
  comment.dislikeCount = (comment.dislikeCount || 0) + 1;
  message.success('点踩成功');
}

// 获取帖子标题
function getPostTitle(content: string) {
  const lines = content.split('\n');
  return lines[0] || '无标题';
}

// 获取帖子内容
function getPostContent(content: string) {
  const lines = content.split('\n');
  return lines.slice(1).join('\n').trim();
}

// 返回列表
function goBack() {
  emit('back');
}

// 组件挂载时加载数据
onMounted(() => {
  loadPostDetail();
});
</script>

<template>
  <div class="post-detail">
    <!-- 面包屑导航 -->
    <Card class="breadcrumb-card mb-4">
      <Breadcrumb>
        <Breadcrumb.Item>
          <HomeOutlined />
          <span @click="goBack" class="breadcrumb-link">{{ props.forumName }}</span>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          帖子详情
        </Breadcrumb.Item>
      </Breadcrumb>
      <Button type="text" @click="goBack" class="back-btn">
        <template #icon>
          <ArrowLeftOutlined />
        </template>
        返回列表
      </Button>
    </Card>

    <Spin :spinning="loading">
      <!-- 帖子内容 -->
      <Card v-if="postDetail" class="post-content-card mb-4">
        <div class="post-header">
          <div class="flex items-center gap-3 mb-4">
            <Avatar :src="postDetail.userAvatar" :size="48">
              <template #icon>
                <UserOutlined />
              </template>
            </Avatar>
            <div>
              <div class="flex items-center gap-2">
                <span class="author-name">{{ postDetail.userName }}</span>
                <Badge text="楼主" color="#f50" />
              </div>
              <div class="post-time">{{ formatDateTime(postDetail.createTime) }}</div>
            </div>
          </div>
        </div>

        <div class="post-content">
          <h1 class="post-title">{{ getPostTitle(postDetail.content || '') }}</h1>
          <div class="post-body">{{ getPostContent(postDetail.content || '') }}</div>
        </div>

        <Divider />

        <div class="post-actions">
          <Space>
            <Button @click="handleLike(postDetail)">
              <template #icon>
                <LikeOutlined />
              </template>
              {{ postDetail.likeCount }}
            </Button>
            <Button @click="handleDislike(postDetail)">
              <template #icon>
                <DislikeOutlined />
              </template>
              {{ postDetail.dislikeCount }}
            </Button>
            <span class="floor-number">1楼</span>
          </Space>
        </div>
      </Card>

      <!-- 回复输入框 -->
      <Card v-if="!readonly" class="reply-input-card mb-4">
        <template #title>
          <MessageOutlined />
          回复帖子
        </template>
        <div class="flex gap-3">
          <Avatar :src="currentUser?.avatar" :size="40">
            <template #icon>
              <UserOutlined />
            </template>
          </Avatar>
          <div class="flex-1">
            <Input.TextArea
              v-model:value="newReply"
              :rows="4"
              placeholder="写下你的回复..."
              :maxlength="1000"
              show-count
              class="mb-3"
            />
            <div class="text-right">
              <Button
                type="primary"
                :loading="submitting"
                @click="submitReply"
              >
                <template #icon>
                  <SendOutlined />
                </template>
                发表回复
              </Button>
            </div>
          </div>
        </div>
      </Card>

      <!-- 回复列表 -->
      <Card class="replies-card">
        <template #title>
          回复列表 ({{ totalReplies }})
        </template>

        <div v-if="replies.length > 0" class="replies-list">
          <div
            v-for="reply in replies"
            :key="reply.commerntId"
            class="reply-item"
          >
            <!-- 主回复 -->
            <div class="main-reply">
              <div class="flex gap-3">
                <Avatar :src="reply.userAvatar" :size="40">
                  <template #icon>
                    <UserOutlined />
                  </template>
                </Avatar>
                <div class="flex-1">
                  <div class="reply-header">
                    <span class="author-name">{{ reply.userName }}</span>
                    <span class="reply-time">{{ formatDateTime(reply.createTime) }}</span>
                    <span class="floor-number">{{ reply.floor }}楼</span>
                  </div>
                  <div class="reply-content">{{ reply.content }}</div>
                  <div class="reply-actions">
                    <Space>
                      <Button size="small" @click="handleLike(reply)">
                        <LikeOutlined />
                        {{ reply.likeCount }}
                      </Button>
                      <Button size="small" @click="handleDislike(reply)">
                        <DislikeOutlined />
                        {{ reply.dislikeCount }}
                      </Button>
                      <Button
                        v-if="!readonly"
                        size="small"
                        @click="startReply(reply)"
                      >
                        <MessageOutlined />
                        回复
                      </Button>
                    </Space>
                  </div>

                  <!-- 回复输入框 -->
                  <div
                    v-if="replyingTo === reply.commerntId"
                    class="sub-reply-input mt-3"
                  >
                    <Input.TextArea
                      v-model:value="replyContent"
                      :rows="3"
                      :placeholder="`回复 ${reply.userName}...`"
                      :maxlength="500"
                      show-count
                      class="mb-2"
                    />
                    <div class="text-right">
                      <Space>
                        <Button size="small" @click="cancelReply">取消</Button>
                        <Button
                          type="primary"
                          size="small"
                          :loading="submitting"
                          @click="submitSubReply(reply)"
                        >
                          发表回复
                        </Button>
                      </Space>
                    </div>
                  </div>

                  <!-- 子回复列表 -->
                  <div v-if="reply.children && reply.children.length > 0" class="sub-replies mt-3">
                    <div
                      v-for="subReply in reply.children"
                      :key="subReply.commerntId"
                      class="sub-reply"
                    >
                      <div class="flex gap-2">
                        <Avatar :src="subReply.userAvatar" :size="32">
                          <template #icon>
                            <UserOutlined />
                          </template>
                        </Avatar>
                        <div class="flex-1">
                          <div class="sub-reply-header">
                            <span class="author-name">{{ subReply.userName }}</span>
                            <span class="reply-time">{{ formatDateTime(subReply.createTime) }}</span>
                          </div>
                          <div class="sub-reply-content">{{ subReply.content }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <Divider v-if="reply !== replies[replies.length - 1]" />
          </div>
        </div>

        <div v-else class="empty-replies">
          <div class="text-center py-8 text-gray-500">
            <MessageOutlined class="text-4xl mb-2" />
            <div>暂无回复，快来抢沙发吧！</div>
          </div>
        </div>
      </Card>
    </Spin>
  </div>
</template>

<style scoped>
/* P3R风格样式 */
.post-detail {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #0a1220 100%);
  min-height: 100vh;
  font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
  position: relative;
  overflow-x: hidden;
}

.post-detail::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 150, 255, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.03) 0%, transparent 50%),
    linear-gradient(180deg, rgba(0, 100, 200, 0.05) 0%, transparent 100%);
  pointer-events: none;
  z-index: 0;
}

.breadcrumb-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(20px);
}

.breadcrumb-card :deep(.ant-card-body) {
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
}

.breadcrumb-card :deep(.ant-breadcrumb) {
  color: #ffffff;
}

.breadcrumb-card :deep(.ant-breadcrumb-link) {
  color: #60a5fa;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.breadcrumb-card :deep(.ant-breadcrumb-link:hover) {
  color: #93c5fd;
  text-shadow: 0 0 8px rgba(96, 165, 250, 0.4);
}

.breadcrumb-card :deep(.ant-breadcrumb-separator) {
  color: #94a3b8;
}

.back-btn {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.back-btn:hover {
  color: #ffffff;
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

.post-content-card {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border: 2px solid #444444;
  border-radius: 0;
  transform: skewX(-1deg);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.post-content-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(220, 20, 60, 0.1), transparent);
  animation: shine 4s infinite;
}

.post-content-card :deep(.ant-card-body) {
  transform: skewX(1deg);
  background: transparent;
  color: #ffffff;
  position: relative;
  z-index: 1;
}

.author-name {
  font-weight: 700;
  color: #dc143c;
  font-size: 16px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.post-time, .reply-time {
  font-size: 12px;
  color: #888888;
  margin-left: 8px;
}

.post-title {
  font-size: 24px;
  font-weight: 900;
  color: #ffffff;
  margin: 20px 0;
  text-shadow:
    0 0 10px rgba(255, 0, 0, 0.5),
    2px 2px 4px rgba(0, 0, 0, 0.8);
  letter-spacing: 1px;
  line-height: 1.3;
}

.post-body {
  font-size: 16px;
  line-height: 1.8;
  color: #cccccc;
  white-space: pre-wrap;
  word-break: break-word;
  background: rgba(0, 0, 0, 0.2);
  padding: 20px;
  border-left: 4px solid #dc143c;
  margin: 16px 0;
}

.floor-number {
  background: linear-gradient(45deg, #dc143c, #8b0000);
  color: white;
  padding: 4px 12px;
  border-radius: 0;
  font-size: 12px;
  font-weight: 700;
  margin-left: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid #ff0000;
  box-shadow: 0 2px 8px rgba(220, 20, 60, 0.3);
}

.reply-input-card {
  background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
  border: 2px solid #dc143c;
  border-radius: 0;
  transform: skewX(-1deg);
  box-shadow:
    0 8px 25px rgba(220, 20, 60, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  position: relative;
  z-index: 1;
}

.reply-input-card :deep(.ant-card-head) {
  background: linear-gradient(135deg, #dc143c, #8b0000);
  border-bottom: 2px solid #ff0000;
  transform: skewX(1deg);
  margin: -1px -1px 0 -1px;
}

.reply-input-card :deep(.ant-card-head-title) {
  color: white;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.reply-input-card :deep(.ant-card-body) {
  transform: skewX(1deg);
  background: transparent;
  color: #ffffff;
}

.replies-card {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border: 2px solid #444444;
  border-radius: 0;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  position: relative;
  z-index: 1;
}

.replies-card :deep(.ant-card-head) {
  background: linear-gradient(135deg, #333333, #1a1a1a);
  border-bottom: 2px solid #555555;
}

.replies-card :deep(.ant-card-head-title) {
  color: white;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.replies-card :deep(.ant-card-body) {
  background: transparent;
  color: #ffffff;
}

.reply-item {
  padding: 20px 0;
  border-bottom: 1px solid #333333;
  position: relative;
  transition: all 0.3s ease;
}

.reply-item:hover {
  background: rgba(220, 20, 60, 0.05);
  border-left: 4px solid #dc143c;
  padding-left: 16px;
}

.reply-header {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.reply-content {
  font-size: 14px;
  line-height: 1.6;
  color: #cccccc;
  margin-bottom: 12px;
  white-space: pre-wrap;
  word-break: break-word;
}

.reply-actions {
  margin-bottom: 12px;
}

.reply-actions :deep(.ant-btn) {
  background: linear-gradient(135deg, #333333, #1a1a1a);
  border: 1px solid #666666;
  color: #ffffff;
  border-radius: 0;
  font-weight: 500;
  transition: all 0.3s ease;
}

.reply-actions :deep(.ant-btn:hover) {
  background: linear-gradient(135deg, #dc143c, #8b0000);
  border-color: #ff0000;
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 20, 60, 0.3);
}

.sub-reply-input {
  background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
  padding: 16px;
  border-radius: 0;
  border: 2px solid #dc143c;
  border-left: 4px solid #ff0000;
}

.sub-replies {
  background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
  padding: 16px;
  border-radius: 0;
  border-left: 4px solid #dc143c;
  border: 1px solid #444444;
}

.sub-reply {
  padding: 12px 0;
  border-bottom: 1px solid #333333;
  transition: all 0.3s ease;
}

.sub-reply:hover {
  background: rgba(220, 20, 60, 0.05);
  padding-left: 8px;
}

.sub-reply:not(:last-child) {
  margin-bottom: 8px;
  padding-bottom: 12px;
}

.sub-reply-header {
  margin-bottom: 6px;
  display: flex;
  align-items: center;
}

.sub-reply-content {
  font-size: 13px;
  line-height: 1.5;
  color: #aaaaaa;
  white-space: pre-wrap;
  word-break: break-word;
}

.empty-replies {
  background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
  border: 2px solid #444444;
  border-radius: 0;
  color: #888888;
  text-align: center;
  padding: 40px;
}

.empty-replies :deep(.anticon) {
  color: #dc143c;
}

/* 按钮样式 */
:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #dc143c, #8b0000);
  border: 2px solid #ff0000;
  border-radius: 0;
  color: white;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(220, 20, 60, 0.4);
  transition: all 0.3s ease;
}

:deep(.ant-btn-primary:hover) {
  background: linear-gradient(135deg, #ff0000, #dc143c);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(220, 20, 60, 0.6);
}

/* 输入框样式 */
:deep(.ant-input),
:deep(.ant-input-affix-wrapper) {
  background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
  border: 2px solid #444444;
  border-radius: 0;
  color: #ffffff;
  transition: all 0.3s ease;
}

:deep(.ant-input:focus),
:deep(.ant-input-affix-wrapper:focus),
:deep(.ant-input-affix-wrapper-focused) {
  border-color: #dc143c;
  box-shadow: 0 0 10px rgba(220, 20, 60, 0.3);
}

:deep(.ant-input::placeholder) {
  color: #666666;
}

/* 头像样式 */
:deep(.ant-avatar) {
  border: 2px solid #dc143c;
  box-shadow: 0 0 10px rgba(220, 20, 60, 0.3);
}

/* 徽章样式 */
:deep(.ant-badge) {
  .ant-badge-count {
    background: linear-gradient(135deg, #dc143c, #8b0000);
    border: 1px solid #ff0000;
    color: white;
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(220, 20, 60, 0.4);
  }
}

/* 分割线样式 */
:deep(.ant-divider) {
  border-color: #333333;
  background: linear-gradient(90deg, transparent, #dc143c, transparent);
  height: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .post-detail {
    padding: 12px;
  }

  .post-title {
    font-size: 20px;
  }

  .breadcrumb-card :deep(.ant-card-body) {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .reply-item {
    padding: 16px 0;
  }

  .post-body {
    font-size: 14px;
    padding: 16px;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #dc143c, #8b0000);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #ff0000, #dc143c);
}

/* 动画 */
@keyframes shine {
  0% { left: -100%; }
  100% { left: 100%; }
}
</style>
