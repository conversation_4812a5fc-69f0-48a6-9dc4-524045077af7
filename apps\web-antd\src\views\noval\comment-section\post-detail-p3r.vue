<script setup lang="ts">
import type { CommentVO, CommentForm } from '#/api/noval/comment/model';

import { computed, nextTick, onMounted, onUnmounted, ref } from 'vue';

import { useUserStore } from '@vben/stores';
import { formatDateTime } from '@vben/utils';

import {
  Avatar,
  Button,
  Card,
  Input,
  List,
  message,
  Space,
  Spin,
  Badge,
  Divider,
  Breadcrumb,
  Modal
} from 'ant-design-vue';
import {
  UserOutlined,
  MessageOutlined,
  SendOutlined,
  LikeOutlined,
  DislikeOutlined,
  ArrowLeftOutlined,
  HomeOutlined,
  HeartOutlined,
  HeartFilled,
  EyeOutlined
} from '@ant-design/icons-vue';

import { commentAdd, commentList, commentInfo, commentIncreaseLook } from '#/api/noval/comment';
import { findUserInfo } from '#/api/system/user';
import { todayList, todayUpdate, todayAdd } from '#/api/noval/today';
import { goodbadInfo, goodbadAdd, goodbadRemove, goodbadBatchStats } from '#/api/noval/goodbad';
import type { GoodbadForm } from '#/api/noval/goodbad/model';
import { attentionList, attentionAdd, attentionRemove } from '#/api/noval/attention';
import type { AttentionForm } from '#/api/noval/attention/model';
import { useNotifyStore } from '#/store/notify';

interface CommentWithUser extends CommentVO {
  userName?: string;
  userAvatar?: string;
  createTime?: string;
  children?: CommentWithUser[];
  floor?: number;
  likeCount?: number; // 点赞数（从GoodbadVO统计）
  dislikeCount?: number; // 点踩数（从GoodbadVO统计）
  isFollowed?: boolean; // 是否已关注该用户
}

interface Props {
  // 帖子ID
  postId: string | number;
  // 贴吧名称
  forumName?: string;
  // 是否只读模式
  readonly?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  forumName: '小说讨论吧',
  readonly: false,
});

const emit = defineEmits<{
  back: [];
}>();

const userStore = useUserStore();
const notifyStore = useNotifyStore();
const loading = ref(false);
const submitting = ref(false);
const postDetail = ref<CommentWithUser | null>(null);
const replies = ref<CommentWithUser[]>([]);
const newReply = ref('');
const replyingTo = ref<number | string | null>(null);
const replyContent = ref('');
const totalReplies = ref(0);
const scrollY = ref(0);
const replyInputHeight = ref(200); // 默认高度
const isFloating = ref(false); // 是否浮动状态
const replyInputRef = ref(null); // 输入框引用
const originalHeight = ref(0); // 原始高度

// 用户信息缓存
const userCache = ref<Map<string | number, { userName: string; userAvatar: string }>>(new Map());

// 用户点赞/踩状态缓存 - key: commentId, value: { isLiked: boolean, isDisliked: boolean }
const userVoteCache = ref<Map<string | number, { isLiked: boolean; isDisliked: boolean }>>(new Map());

// 用户关注状态缓存 - key: userId, value: boolean
const userFollowCache = ref<Map<string | number, boolean>>(new Map());

// 用户帖子弹窗相关
const showUserPostsModal = ref(false);
const selectedUserId = ref<string | number>('');
const selectedUserName = ref('');
const userPosts = ref<CommentWithUser[]>([]);

// 当前用户信息
const currentUser = computed(() => userStore.userInfo);

// 默认头像 - 使用多个备选方案
const defaultAvatars = [
  'https://unpkg.com/@vbenjs/static-source@0.1.7/source/avatar-v1.webp',
  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiMzYjgyZjYiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMTRDOC42ODYyOSAxNCA2IDE2LjY4NjMgNiAyMEg2VjIwSDZIMThIMThWMjBDMTggMTYuNjg2MyAxNS4zMTM3IDE0IDEyIDE0WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cjwvc3ZnPgo=',
];

// 获取有效的头像URL
function getValidAvatarUrl(userAvatar?: string): string {
  // 如果用户头像存在且有效，返回用户头像
  if (userAvatar && userAvatar.trim() && userAvatar !== 'null' && userAvatar !== 'undefined') {
    return userAvatar.trim();
  }
  // 否则返回第一个默认头像
  return defaultAvatars[0];
}

// 获取用户信息
async function getUserInfo(userId: string | number) {
  if (userCache.value.has(userId)) {
    return userCache.value.get(userId);
  }

  try {
    const userInfo = await findUserInfo(userId);
    const userData = {
      userName: userInfo.user?.nickName || userInfo.user?.userName || '未知用户',
      userAvatar: getValidAvatarUrl(userInfo.user?.avatar),
    };
    userCache.value.set(userId, userData);
    return userData;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      userName: '未知用户',
      userAvatar: getValidAvatarUrl(),
    };
  }
}

// 获取用户对评论的点赞/踩状态
async function getUserVoteStatus(commentId: string | number) {
  if (!currentUser.value?.userId) {
    return { isLiked: false, isDisliked: false };
  }

  if (userVoteCache.value.has(commentId)) {
    return userVoteCache.value.get(commentId)!;
  }

  try {
    // 查询点赞记录
    const likeRecord = await goodbadInfo(commentId, currentUser.value.userId);
    const status = {
      isLiked: likeRecord && likeRecord.goodBad === 1,
      isDisliked: likeRecord && likeRecord.goodBad === 0,
    };
    userVoteCache.value.set(commentId, status);
    return status;
  } catch (error) {
    // 如果没有记录，返回默认状态
    const status = { isLiked: false, isDisliked: false };
    userVoteCache.value.set(commentId, status);
    return status;
  }
}

// 获取用户关注状态
async function getUserFollowStatus(userId: string | number) {
  if (!currentUser.value?.userId || userId === currentUser.value.userId) {
    return false; // 不能关注自己
  }

  if (userFollowCache.value.has(userId)) {
    return userFollowCache.value.get(userId)!;
  }

  try {
    // 查询关注记录
    const response = await attentionList({
      id: currentUser.value.userId,
      ortherId: userId,
      pageNum: 1,
      pageSize: 1
    });
    const isFollowed = response.rows && response.rows.length > 0;
    userFollowCache.value.set(userId, isFollowed);
    return isFollowed;
  } catch (error) {
    // 如果没有记录，返回默认状态
    userFollowCache.value.set(userId, false);
    return false;
  }
}

// 加载帖子详情和回复
async function loadPostDetail() {
  loading.value = true;
  try {
    // 获取帖子详情（commerntType=0的楼主帖子）
    const post = await commentInfo(props.postId);

    // 增加帖子浏览量（防重复机制）
    try {
      const viewedKey = `post_viewed_${post.commerntId}`;
      const hasViewed = sessionStorage.getItem(viewedKey);

      if (!hasViewed) {
        const updatedPostData: CommentForm = {
          commerntId: post.commerntId,
          myId: post.myId,
          commerntType: post.commerntType,
          isResponse: post.isResponse,
          responseId: post.responseId,
          content: post.content,
          look: (post.look || 0) + 1, // look属性+1
          icon1: post.icon1,
          icon2: post.icon2,
          icon3: post.icon3,
          icon4: post.icon4,
          icon5: post.icon5,
        };

        // 更新到数据库
        await commentIncreaseLook(updatedPostData);

        // 更新本地的post数据
        post.look = updatedPostData.look || 0;

        // 标记为已浏览（本次会话期间）
        sessionStorage.setItem(viewedKey, 'true');
      }
    } catch (error) {
      console.error('增加浏览量失败:', error);
      // 不影响主要功能，继续执行
    }
    const userInfo = await getUserInfo(post.myId);

    // 获取帖子的点赞统计
    const postStats = await goodbadBatchStats([post.commerntId]);
    const stats = postStats.get(post.commerntId) || { likeCount: 0, dislikeCount: 0 };

    // 获取关注状态
    const isFollowed = await getUserFollowStatus(post.myId);

    postDetail.value = {
      ...post,
      userName: userInfo?.userName || '未知用户',
      userAvatar: userInfo?.userAvatar || getValidAvatarUrl(),
      likeCount: stats.likeCount, // 从GoodbadVO统计的点赞数
      dislikeCount: stats.dislikeCount, // 从GoodbadVO统计的点踩数
      floor: 1,
      isFollowed, // 关注状态
    };

    // 获取该帖子的评论列表（commerntType=1, responseId=帖子ID）
    const response = await commentList({
      pageNum: 1,
      pageSize: 1000,
      commerntType: 1, // 获取评论
      responseId: props.postId, // 直接通过API筛选属于当前帖子的评论
    });

    const allComments = response.rows || [];

    // 获取属于当前帖子的评论
    const postComments = allComments;

    // 获取所有用户信息
    const userIds = [...new Set(postComments.map(item => item.myId))];
    await Promise.all(userIds.map(userId => getUserInfo(userId)));

    // 批量获取所有评论的点赞统计数据
    const commentIds = postComments.map(comment => comment.commerntId);
    const commentsStatsMap = await goodbadBatchStats(commentIds);

    // 构建评论数据
    const commentsWithUser: CommentWithUser[] = await Promise.all(
      postComments.map(async (comment, index) => {
        const commentStats = commentsStatsMap.get(comment.commerntId) || { likeCount: 0, dislikeCount: 0 };
        const isFollowed = await getUserFollowStatus(comment.myId);
        return {
          ...comment,
          userName: userCache.value.get(comment.myId)?.userName || '未知用户',
          userAvatar: userCache.value.get(comment.myId)?.userAvatar || getValidAvatarUrl(),
          createTime: comment.createTime || new Date().toISOString(),
          likeCount: commentStats.likeCount, // 从GoodbadVO统计的点赞数
          dislikeCount: commentStats.dislikeCount, // 从GoodbadVO统计的点踩数
          floor: index + 2, // 从2楼开始
          children: [],
          isFollowed, // 关注状态
        };
      })
    );

    // 分离主评论和回复
    const mainComments = commentsWithUser.filter(c => c.isResponse === 0);
    const subReplies = commentsWithUser.filter(c => c.isResponse === 1);

    // 构建回复树（回复的回复）
    mainComments.forEach(comment => {
      // 查找回复该评论的回复（responseId = comment.commerntId）
      const subReplies = allComments.filter(reply =>
        reply.isResponse === 1 && reply.responseId === comment.commerntId
      );

      comment.children = subReplies.map(reply => {
        const replyStats = commentsStatsMap.get(reply.commerntId) || { likeCount: 0, dislikeCount: 0 };
        return {
          ...reply,
          userName: userCache.value.get(reply.myId)?.userName || '未知用户',
          userAvatar: userCache.value.get(reply.myId)?.userAvatar || getValidAvatarUrl(),
          createTime: reply.createTime || new Date().toISOString(),
          likeCount: replyStats.likeCount, // 从GoodbadVO统计的点赞数
          dislikeCount: replyStats.dislikeCount, // 从GoodbadVO统计的点踩数
        };
      });

      comment.children.sort((a, b) =>
        new Date(a.createTime || 0).getTime() - new Date(b.createTime || 0).getTime()
      );
    });

    replies.value = mainComments.sort((a, b) => (a.floor || 0) - (b.floor || 0));
    totalReplies.value = postComments.length;

    // 预加载所有评论的点赞/踩状态
    if (currentUser.value?.userId) {
      const allComments = [postDetail.value, ...commentsWithUser];
      await Promise.all(
        allComments.map(async (comment) => {
          try {
            await getUserVoteStatus(comment.commerntId);
          } catch (error) {
            // 忽略单个评论的状态加载错误
            console.warn(`加载评论 ${comment.commerntId} 的点赞状态失败:`, error);
          }
        }),
      );
    }
  } catch (error) {
    console.error('加载帖子详情失败:', error);
    message.error('加载帖子详情失败');
  } finally {
    loading.value = false;
  }
}

// 发表回复
async function submitReply() {
  if (!newReply.value.trim()) {
    message.warning('请输入回复内容');
    return;
  }

  if (!currentUser.value?.userId) {
    message.error('请先登录');
    return;
  }

  submitting.value = true;
  try {
    const replyData: CommentForm = {
      content: newReply.value.trim(),
      myId: currentUser.value.userId,
      commerntType: 1, // 1代表这是帖子的评论
      isResponse: 0, // 0代表不是回复
      responseId: postDetail.value?.commerntId || 0, // 回复的是帖子ID
    };

    await commentAdd(replyData);
    message.success('回复发表成功');
    newReply.value = '';
    await loadPostDetail();
    // 更新全局评论数量
    await notifyStore.fetchCommentCount();
  } catch (error) {
    console.error('发表回复失败:', error);
    message.error('发表回复失败');
  } finally {
    submitting.value = false;
  }
}

// 回复某个回复
async function submitSubReply(parentReply: CommentWithUser) {
  if (!replyContent.value.trim()) {
    message.warning('请输入回复内容');
    return;
  }

  if (!currentUser.value?.userId) {
    message.error('请先登录');
    return;
  }

  submitting.value = true;
  try {
    const replyData: CommentForm = {
      content: replyContent.value.trim(),
      myId: currentUser.value.userId,
      commerntType: 1, // 1代表这是评论
      isResponse: 1, // 1代表是回复的回复
      responseId: parentReply.commerntId, // 回复的是评论ID
    };

    await commentAdd(replyData);
    message.success('回复发表成功');
    replyContent.value = '';
    replyingTo.value = null;
    await loadPostDetail();
    // 更新全局评论数量
    await notifyStore.fetchCommentCount();
  } catch (error) {
    console.error('发表回复失败:', error);
    message.error('发表回复失败');
  } finally {
    submitting.value = false;
  }
}

// 开始回复
function startReply(reply: CommentWithUser) {
  replyingTo.value = reply.commerntId;
  replyContent.value = '';
}

// 取消回复
function cancelReply() {
  replyingTo.value = null;
  replyContent.value = '';
}

// 点赞
async function handleLike(comment: CommentWithUser) {
  if (!currentUser.value?.userId) {
    message.error('请先登录');
    return;
  }

  try {
    const commentId = comment.commerntId;
    const userId = currentUser.value.userId;

    // 获取当前状态
    const currentStatus = await getUserVoteStatus(commentId);

    if (currentStatus.isLiked) {
      // 已经点赞，取消点赞
      await goodbadRemove(commentId, userId);
      userVoteCache.value.set(commentId, { isLiked: false, isDisliked: false });
      comment.likeCount = Math.max(0, (comment.likeCount || 0) - 1);
    } else if (currentStatus.isDisliked) {
      // 已经点踩，先取消踩再点赞
      await goodbadRemove(commentId, userId);
      const likeData: GoodbadForm = {
        commerntId: commentId,
        whoId: userId,
        goodBad: 1, // 1代表赞
      };
      await goodbadAdd(likeData);
      userVoteCache.value.set(commentId, { isLiked: true, isDisliked: false });
      comment.likeCount = (comment.likeCount || 0) + 1;
      comment.dislikeCount = Math.max(0, (comment.dislikeCount || 0) - 1);
    } else {
      // 没有记录，新增点赞
      const likeData: GoodbadForm = {
        commerntId: commentId,
        whoId: userId,
        goodBad: 1, // 1代表赞
      };
      await goodbadAdd(likeData);
      userVoteCache.value.set(commentId, { isLiked: true, isDisliked: false });
      comment.likeCount = (comment.likeCount || 0) + 1;
    }
  } catch (error) {
    console.error('点赞操作失败:', error);
    message.error('操作失败，请重试');
  }
}

// 点踩
async function handleDislike(comment: CommentWithUser) {
  if (!currentUser.value?.userId) {
    message.error('请先登录');
    return;
  }

  try {
    const commentId = comment.commerntId;
    const userId = currentUser.value.userId;

    // 获取当前状态
    const currentStatus = await getUserVoteStatus(commentId);

    if (currentStatus.isDisliked) {
      // 已经点踩，取消踩
      await goodbadRemove(commentId, userId);
      userVoteCache.value.set(commentId, { isLiked: false, isDisliked: false });
      comment.dislikeCount = Math.max(0, (comment.dislikeCount || 0) - 1);
    } else if (currentStatus.isLiked) {
      // 已经点赞，先取消赞再点踩
      await goodbadRemove(commentId, userId);
      const dislikeData: GoodbadForm = {
        commerntId: commentId,
        whoId: userId,
        goodBad: 0, // 0代表踩
      };
      await goodbadAdd(dislikeData);
      userVoteCache.value.set(commentId, { isLiked: false, isDisliked: true });
      comment.dislikeCount = (comment.dislikeCount || 0) + 1;
      comment.likeCount = Math.max(0, (comment.likeCount || 0) - 1);
    } else {
      // 没有记录，新增踩
      const dislikeData: GoodbadForm = {
        commerntId: commentId,
        whoId: userId,
        goodBad: 0, // 0代表踩
      };
      await goodbadAdd(dislikeData);
      userVoteCache.value.set(commentId, { isLiked: false, isDisliked: true });
      comment.dislikeCount = (comment.dislikeCount || 0) + 1;
    }
  } catch (error) {
    console.error('点踩操作失败:', error);
    message.error('操作失败，请重试');
  }
}

// 获取帖子标题
function getPostTitle(content: string) {
  const lines = content.split('\n');
  return lines[0] || '无标题';
}

// 获取帖子内容
function getPostContent(content: string) {
  const lines = content.split('\n');
  return lines.slice(1).join('\n').trim();
}

// 返回列表
function goBack() {
  emit('back');
}

// 关注/取消关注用户
async function handleFollow(comment: CommentWithUser, event: Event) {
  event.stopPropagation(); // 阻止事件冒泡

  if (!currentUser.value?.userId) {
    message.error('请先登录');
    return;
  }

  if (comment.myId === currentUser.value.userId) {
    message.warning('不能关注自己');
    return;
  }

  try {
    const userId = comment.myId;
    const currentUserId = currentUser.value.userId;
    const isCurrentlyFollowed = await getUserFollowStatus(userId);

    if (isCurrentlyFollowed) {
      // 取消关注
      const response = await attentionList({
        id: currentUserId,
        ortherId: userId,
        pageNum: 1,
        pageSize: 1
      });

      if (response.rows && response.rows.length > 0) {
        const attentionRecord = response.rows[0];
        await attentionRemove(attentionRecord.id);
        userFollowCache.value.set(userId, false);
        comment.isFollowed = false;
        message.success('已取消关注');
      }
    } else {
      // 添加关注
      const followData: AttentionForm = {
        id: currentUserId,
        ortherId: userId,
      };
      await attentionAdd(followData);
      userFollowCache.value.set(userId, true);
      comment.isFollowed = true;
      message.success('关注成功');
    }
  } catch (error) {
    console.error('关注操作失败:', error);
    message.error('操作失败，请重试');
  }
}

// 查看用户帖子
async function viewUserPosts(userId: string | number, userName: string, event: Event) {
  event.stopPropagation(); // 阻止事件冒泡

  try {
    selectedUserId.value = userId;
    selectedUserName.value = userName;

    // 获取该用户的所有帖子
    const response = await commentList({
      pageNum: 1,
      pageSize: 100,
      commerntType: 0, // 只获取帖子
      myId: userId, // 根据用户ID筛选
    });

    const userPostData = response.rows || [];

    // 为每个帖子计算回复数
    const postsWithReplyCount = await Promise.all(
      userPostData.map(async (post) => {
        let replyCount = 0;

        try {
          // 获取该帖子的所有评论
          const repliesResponse = await commentList({
            pageNum: 1,
            pageSize: 1000,
            commerntType: 1, // 获取评论
            responseId: post.commerntId, // 直接通过API筛选属于当前帖子的回复
          });

          const postReplies = repliesResponse.rows || [];
          replyCount = postReplies.length;
        } catch (error) {
          console.error('获取帖子回复数失败:', error);
          replyCount = 0;
        }

        return {
          ...post,
          userName,
          userAvatar: userCache.value.get(userId)?.userAvatar || getValidAvatarUrl(),
          createTime: post.createTime || new Date().toISOString(),
          replyCount, // 添加计算出的回复数
        };
      })
    );

    userPosts.value = postsWithReplyCount;

    showUserPostsModal.value = true;
  } catch (error) {
    console.error('获取用户帖子失败:', error);
    message.error('获取用户帖子失败');
  }
}

// 获取帖子预览内容
function getPostPreview(content: string) {
  const lines = content.split('\n');
  const contentLines = lines.slice(1).join('\n').trim();
  return contentLines.length > 100 ? contentLines.substring(0, 100) + '...' : contentLines;
}

// 防抖定时器
let scrollTimer = null;

// 滚动监听函数
function handleScroll() {
  // 清除之前的定时器
  if (scrollTimer) {
    clearTimeout(scrollTimer);
  }

  // 防抖处理
  scrollTimer = setTimeout(() => {
    const currentScrollY = window.scrollY || document.documentElement.scrollTop;
    scrollY.value = currentScrollY;

    const scrollThreshold = 300; // 滚动阈值
    const shouldFloat = currentScrollY > scrollThreshold;

    // 只有在状态真正需要改变时才更新
    if (isFloating.value !== shouldFloat) {
      isFloating.value = shouldFloat;
    }
  }, 50); // 50ms防抖
}

// 获取原始输入框高度
function getOriginalHeight() {
  nextTick(() => {
    if (replyInputRef.value && replyInputRef.value.$el) {
      originalHeight.value = replyInputRef.value.$el.offsetHeight;
    }
  });
}

// 组件挂载时加载数据
onMounted(() => {
  loadPostDetail();

  // 获取原始高度
  getOriginalHeight();

  // 添加滚动监听
  window.addEventListener('scroll', handleScroll, { passive: true });
});

// 组件卸载时移除监听
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);

  // 清除定时器
  if (scrollTimer) {
    clearTimeout(scrollTimer);
  }
});
</script>

<template>
  <div class="p3r-post-detail">
    <!-- 面包屑导航 -->
    <Card class="breadcrumb-card mb-4">
      <Breadcrumb>
        <Breadcrumb.Item>
          <HomeOutlined />
          <span @click="goBack" class="breadcrumb-link">{{ props.forumName }}</span>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          帖子详情
        </Breadcrumb.Item>
      </Breadcrumb>
      <Button type="text" @click="goBack" class="back-btn">
        <template #icon>
          <ArrowLeftOutlined />
        </template>
        返回列表
      </Button>
    </Card>

    <Spin :spinning="loading">
      <!-- 帖子内容 -->
      <Card v-if="postDetail" class="post-content-card mb-4">
        <div class="post-header">
          <div class="flex items-center gap-3 mb-4">
            <Avatar
              :src="getValidAvatarUrl(postDetail.userAvatar)"
              :size="48"
              class="clickable-avatar"
              @click="viewUserPosts(postDetail.myId, postDetail.userName || '未知用户', $event)"
            >
              <template #icon>
                <UserOutlined />
              </template>
            </Avatar>
            <div class="flex-1">
              <div class="flex items-center gap-2">
                <span class="author-name">{{ postDetail.userName }}</span>
                <Badge text="楼主" color="#3b82f6" />
                <!-- 关注按钮 -->
                <Button
                  v-if="currentUser?.userId && postDetail.myId !== currentUser.userId"
                  :type="postDetail.isFollowed ? 'default' : 'primary'"
                  size="small"
                  class="follow-btn-inline"
                  @click="handleFollow(postDetail, $event)"
                >
                  <template #icon>
                    <HeartFilled v-if="postDetail.isFollowed" />
                    <HeartOutlined v-else />
                  </template>
                  {{ postDetail.isFollowed ? '已关注' : '关注' }}
                </Button>
              </div>
              <div class="post-time">{{ formatDateTime(postDetail.createTime) }}</div>
            </div>
          </div>
        </div>

        <div class="post-content">
          <h1 class="post-title">{{ getPostTitle(postDetail.content || '') }}</h1>
          <div class="post-body">{{ getPostContent(postDetail.content || '') }}</div>
        </div>

        <Divider />

        <div class="post-actions">
          <Space>
            <Button
              @click="handleLike(postDetail)"
              :type="getUserVoteStatus(postDetail.commerntId).then(status => status.isLiked) ? 'primary' : 'default'"
              :class="{ 'liked': userVoteCache.get(postDetail.commerntId)?.isLiked }"
            >
              <template #icon>
                <LikeOutlined />
              </template>
              {{ postDetail.likeCount || 0 }}
            </Button>
            <Button
              @click="handleDislike(postDetail)"
              :type="getUserVoteStatus(postDetail.commerntId).then(status => status.isDisliked) ? 'primary' : 'default'"
              :class="{ 'disliked': userVoteCache.get(postDetail.commerntId)?.isDisliked }"
            >
              <template #icon>
                <DislikeOutlined />
              </template>
              {{ postDetail.dislikeCount || 0 }}
            </Button>
            <span class="floor-number">1楼</span>
          </Space>
        </div>
      </Card>

      <!-- 回复列表 -->
      <Card class="replies-card">
        <template #title>
          回复列表 ({{ totalReplies }})
        </template>

        <div v-if="replies.length > 0" class="replies-list">
          <div
            v-for="reply in replies"
            :key="reply.commerntId"
            class="reply-item"
          >
            <!-- 主回复 -->
            <div class="main-reply">
              <div class="flex gap-3">
                <Avatar
                  :src="getValidAvatarUrl(reply.userAvatar)"
                  :size="40"
                  class="clickable-avatar"
                  @click="viewUserPosts(reply.myId, reply.userName || '未知用户', $event)"
                >
                  <template #icon>
                    <UserOutlined />
                  </template>
                </Avatar>
                <div class="flex-1">
                  <div class="reply-header">
                    <div class="flex items-center gap-2">
                      <span class="author-name">{{ reply.userName }}</span>
                      <!-- 关注按钮 -->
                      <Button
                        v-if="currentUser?.userId && reply.myId !== currentUser.userId"
                        :type="reply.isFollowed ? 'default' : 'primary'"
                        size="small"
                        class="follow-btn-inline"
                        @click="handleFollow(reply, $event)"
                      >
                        <template #icon>
                          <HeartFilled v-if="reply.isFollowed" />
                          <HeartOutlined v-else />
                        </template>
                        {{ reply.isFollowed ? '已关注' : '关注' }}
                      </Button>
                      <span class="reply-time">{{ formatDateTime(reply.createTime) }}</span>
                      <span class="floor-number">{{ reply.floor }}楼</span>
                    </div>
                  </div>
                  <div class="reply-content">{{ reply.content }}</div>
                  <div class="reply-actions">
                    <Space>
                      <Button
                        size="small"
                        @click="handleLike(reply)"
                        :class="{ 'liked': userVoteCache.get(reply.commerntId)?.isLiked }"
                      >
                        <LikeOutlined />
                        {{ reply.likeCount || 0 }}
                      </Button>
                      <Button
                        size="small"
                        @click="handleDislike(reply)"
                        :class="{ 'disliked': userVoteCache.get(reply.commerntId)?.isDisliked }"
                      >
                        <DislikeOutlined />
                        {{ reply.dislikeCount || 0 }}
                      </Button>
                      <Button
                        v-if="!readonly"
                        size="small"
                        @click="startReply(reply)"
                      >
                        <MessageOutlined />
                        回复
                      </Button>
                    </Space>
                  </div>

                  <!-- 回复输入框 -->
                  <div
                    v-if="replyingTo === reply.commerntId"
                    class="sub-reply-input mt-3"
                  >
                    <Input.TextArea
                      v-model:value="replyContent"
                      :rows="3"
                      :placeholder="`回复 ${reply.userName}...`"
                      :maxlength="500"
                      show-count
                      class="mb-2"
                    />
                    <div class="text-right">
                      <Space>
                        <Button size="small" @click="cancelReply">取消</Button>
                        <Button
                          type="primary"
                          size="small"
                          :loading="submitting"
                          @click="submitSubReply(reply)"
                        >
                          发表回复
                        </Button>
                      </Space>
                    </div>
                  </div>

                  <!-- 子回复列表 -->
                  <div v-if="reply.children && reply.children.length > 0" class="sub-replies mt-3">
                    <div
                      v-for="subReply in reply.children"
                      :key="subReply.commerntId"
                      class="sub-reply"
                    >
                      <div class="flex gap-2">
                        <Avatar
                          :src="getValidAvatarUrl(subReply.userAvatar)"
                          :size="32"
                        >
                          <template #icon>
                            <UserOutlined />
                          </template>
                        </Avatar>
                        <div class="flex-1">
                          <div class="sub-reply-header">
                            <span class="author-name">{{ subReply.userName }}</span>
                            <span class="reply-time">{{ formatDateTime(subReply.createTime) }}</span>
                          </div>
                          <div class="sub-reply-content">{{ subReply.content }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <Divider v-if="reply !== replies[replies.length - 1]" />
          </div>
        </div>

        <div v-else class="empty-replies">
          <div class="text-center py-8 text-gray-500">
            <MessageOutlined class="text-4xl mb-2" />
            <div>暂无回复，快来抢沙发吧！</div>
          </div>
        </div>
      </Card>

      <!-- 回复输入框占位符 (浮动时保持布局) -->
      <div
        v-if="!readonly && isFloating"
        class="reply-input-placeholder mb-4"
        :style="{ height: originalHeight + 'px' }"
      ></div>

      <!-- 回复输入框 -->
      <Card
        v-if="!readonly"
        ref="replyInputRef"
        :class="[
          'reply-input-card',
          { 'mb-4': !isFloating },
          { 'floating': isFloating }
        ]"
      >
        <template #title>
          <MessageOutlined />
          回复帖子
        </template>
        <div class="reply-input-content">
          <Avatar
            :src="getValidAvatarUrl(currentUser?.avatar)"
            :size="40"
            @error="() => { /* 已经是默认头像，无需处理 */ }"
          >
            <template #icon>
              <UserOutlined />
            </template>
          </Avatar>
          <div class="reply-input-main">
            <Input.TextArea
              v-model:value="newReply"
              :rows="isFloating ? 2 : 4"
              placeholder="写下你的回复..."
              :maxlength="1000"
              show-count
              class="reply-textarea"
            />
            <div class="reply-button-container">
              <Button
                type="primary"
                :loading="submitting"
                @click="submitReply"
              >
                <template #icon>
                  <SendOutlined />
                </template>
                发表回复
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </Spin>

    <!-- 用户帖子弹窗 -->
    <Modal
      v-model:open="showUserPostsModal"
      :title="`${selectedUserName} 的帖子`"
      width="800px"
      :footer="null"
      @cancel="showUserPostsModal = false"
    >
      <div class="user-posts-modal">
        <div v-if="userPosts.length > 0" class="user-posts-list">
          <List
            :data-source="userPosts"
            item-layout="vertical"
            size="small"
          >
            <template #renderItem="{ item: userPost }">
              <List.Item class="user-post-item" @click="goBack(); showUserPostsModal = false">
                <div class="user-post-content">
                  <h4 class="user-post-title">{{ getPostTitle(userPost.content || '') }}</h4>
                  <div class="user-post-preview">{{ getPostPreview(userPost.content || '') }}</div>
                  <div class="user-post-meta">
                    <span class="user-post-time">{{ formatDateTime(userPost.createTime) }}</span>
                    <span class="user-post-stats">
                      <EyeOutlined /> {{ userPost.look || 0 }}
                      <MessageOutlined style="margin-left: 12px;" /> {{ userPost.replyCount || 0 }}
                    </span>
                  </div>
                </div>
              </List.Item>
            </template>
          </List>
        </div>
        <div v-else class="user-posts-empty">
          <div class="text-center py-8">
            <MessageOutlined class="text-4xl text-gray-400 mb-2" />
            <div class="text-gray-500">该用户还没有发表帖子</div>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<style scoped>
/* 确保页面可以正常滚动 */
html, body {
  overflow-y: auto !important;
  height: auto !important;
  scroll-behavior: smooth;
}

/* P5R风格样式 - 蓝色主题 */
.p3r-post-detail {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, #0a0a0a 0%, #0a0a1a 25%, #050a2a 50%, #0a0a1a 75%, #0a0a0a 100%);
  min-height: 100vh;
  font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
  position: relative;
  overflow-x: hidden;
  /* 确保页面可以正常滚动 */
  overflow-y: auto;
}

.p3r-post-detail::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
    linear-gradient(45deg, transparent 48%, rgba(59, 130, 246, 0.03) 49%, rgba(59, 130, 246, 0.03) 51%, transparent 52%),
    linear-gradient(-45deg, transparent 48%, rgba(0, 0, 0, 0.1) 49%, rgba(0, 0, 0, 0.1) 51%, transparent 52%);
  background-size: 100% 100%, 100% 100%, 60px 60px, 60px 60px;
  pointer-events: none;
  z-index: 0;
  animation: p5rBgShift 20s infinite linear;
}

@keyframes p5rBgShift {
  0% { background-position: 0% 0%, 0% 0%, 0px 0px, 0px 0px; }
  100% { background-position: 0% 0%, 0% 0%, 60px 60px, -60px -60px; }
}

.breadcrumb-card {
  background: rgba(255, 255, 255, 0.02);
  border: 2px solid #3b82f6;
  border-radius: 0;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(20px);
  clip-path: polygon(0 0, calc(100% - 10px) 0, 100% 10px, 100% 100%, 10px 100%, 0 calc(100% - 10px));
}

.breadcrumb-card :deep(.ant-card-body) {
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
}

.breadcrumb-card :deep(.ant-breadcrumb) {
  color: #ffffff;
}

.breadcrumb-link {
  color: #60a5fa;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.breadcrumb-link:hover {
  color: #93c5fd;
  text-shadow: 0 0 8px rgba(96, 165, 250, 0.4);
}

.breadcrumb-card :deep(.ant-breadcrumb-separator) {
  color: #94a3b8;
}

.back-btn {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.back-btn:hover {
  color: #ffffff;
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

.post-content-card {
  background: rgba(255, 255, 255, 0.02);
  border: 2px solid #3b82f6;
  border-radius: 0;
  box-shadow:
    0 0 30px rgba(59, 130, 246, 0.4),
    0 8px 32px rgba(0, 0, 0, 0.6),
    inset 0 1px 0 rgba(59, 130, 246, 0.2);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(20px);
  overflow: hidden;
  clip-path: polygon(0 0, calc(100% - 15px) 0, 100% 15px, 100% 100%, 15px 100%, 0 calc(100% - 15px));
}

.post-content-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.05), transparent);
  animation: gentleShine 6s infinite;
}

.post-content-card :deep(.ant-card-body) {
  background: transparent;
  color: #ffffff;
  position: relative;
  z-index: 1;
}

.author-name {
  font-weight: 600;
  color: #60a5fa;
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.post-time, .reply-time {
  font-size: 12px;
  color: #94a3b8;
  margin-left: 8px;
}

.post-title {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  margin: 20px 0;
  text-shadow:
    0 2px 8px rgba(59, 130, 246, 0.2),
    0 1px 2px rgba(0, 0, 0, 0.5);
  letter-spacing: 0.5px;
  line-height: 1.3;
}

.post-body {
  font-size: 16px;
  line-height: 1.8;
  color: #cbd5e1;
  white-space: pre-wrap;
  word-break: break-word;
  background: rgba(0, 0, 0, 0.1);
  padding: 20px;
  border-left: 4px solid #3b82f6;
  border-radius: 0 8px 8px 0;
  margin: 16px 0;
}

.floor-number {
  background: linear-gradient(45deg, #3b82f6, #1e40af);
  color: white;
  padding: 4px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  margin-left: 8px;
  letter-spacing: 0.3px;
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.reply-input-card {
  background: rgba(255, 255, 255, 0.02);
  border: 2px solid #3b82f6;
  border-radius: 0;
  box-shadow:
    0 0 25px rgba(59, 130, 246, 0.4),
    inset 0 1px 0 rgba(59, 130, 246, 0.2);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(20px);
  clip-path: polygon(0 0, calc(100% - 12px) 0, 100% 12px, 100% 100%, 12px 100%, 0 calc(100% - 12px));
  transition: all 0.3s ease;
}

/* 浮动状态样式 */
.reply-input-card.floating {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% - 40px);
  max-width: 800px;
  z-index: 1000;
  box-shadow:
    0 0 40px rgba(59, 130, 246, 0.6),
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  background: rgba(255, 255, 255, 0.05);
  pointer-events: auto;
}

/* 确保浮动时不影响页面滚动 */
.reply-input-card.floating {
  margin-bottom: 0 !important;
}

/* 占位符样式 - 保持原始输入框的高度 */
.reply-input-placeholder {
  visibility: hidden;
  /* 高度通过内联样式动态设置 */
}

.reply-input-card :deep(.ant-card-head) {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.6), rgba(30, 64, 175, 0.4));
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px 16px 0 0;
  backdrop-filter: blur(10px);
}

.reply-input-card :deep(.ant-card-head-title) {
  color: white;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.reply-input-card :deep(.ant-card-body) {
  background: transparent;
  color: #ffffff;
  padding: 16px;
}

.reply-input-content {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.reply-input-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.reply-textarea {
  margin-bottom: 12px !important;
}

.reply-textarea :deep(.ant-input) {
  resize: none;
}

.reply-button-container {
  text-align: right;
}

/* 浮动状态下的紧凑布局 */
.reply-input-card.floating .reply-input-content {
  gap: 8px;
}

.reply-input-card.floating :deep(.ant-card-body) {
  padding: 12px;
}

.reply-input-card.floating .reply-textarea {
  margin-bottom: 8px !important;
}

.replies-card {
  background: rgba(255, 255, 255, 0.02);
  border: 2px solid #3b82f6;
  border-radius: 0;
  box-shadow:
    0 0 30px rgba(59, 130, 246, 0.4),
    0 8px 32px rgba(0, 0, 0, 0.6),
    inset 0 1px 0 rgba(59, 130, 246, 0.2);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(20px);
  clip-path: polygon(0 0, calc(100% - 15px) 0, 100% 15px, 100% 100%, 15px 100%, 0 calc(100% - 15px));
}

.replies-card :deep(.ant-card-head) {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.4), rgba(30, 64, 175, 0.3));
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 16px 16px 0 0;
  backdrop-filter: blur(10px);
}

.replies-card :deep(.ant-card-head-title) {
  color: white;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.replies-card :deep(.ant-card-body) {
  background: transparent;
  color: #ffffff;
}

.reply-item {
  padding: 20px 0;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.reply-item:hover {
  background: rgba(59, 130, 246, 0.03);
  border-left: 3px solid #3b82f6;
  padding-left: 16px;
  border-radius: 0 8px 8px 0;
}

.reply-header {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.reply-content {
  font-size: 14px;
  line-height: 1.6;
  color: #cbd5e1;
  margin-bottom: 12px;
  white-space: pre-wrap;
  word-break: break-word;
}

.reply-actions {
  margin-bottom: 12px;
}

.reply-actions :deep(.ant-btn) {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(5px);
}

.reply-actions :deep(.ant-btn:hover) {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  border-color: rgba(59, 130, 246, 0.3);
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.sub-reply-input {
  background: rgba(59, 130, 246, 0.05);
  padding: 16px;
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  backdrop-filter: blur(10px);
}

.sub-replies {
  background: rgba(255, 255, 255, 0.02);
  padding: 16px;
  border-radius: 12px;
  border-left: 3px solid #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(5px);
}

.sub-reply {
  padding: 12px 0;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sub-reply:hover {
  background: rgba(59, 130, 246, 0.03);
  padding-left: 8px;
  border-radius: 8px;
}

.sub-reply:not(:last-child) {
  margin-bottom: 8px;
  padding-bottom: 12px;
}

.sub-reply-header {
  margin-bottom: 6px;
  display: flex;
  align-items: center;
}

.sub-reply-content {
  font-size: 13px;
  line-height: 1.5;
  color: #94a3b8;
  white-space: pre-wrap;
  word-break: break-word;
}

.empty-replies {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 12px;
  color: #94a3b8;
  text-align: center;
  padding: 40px;
  backdrop-filter: blur(10px);
}

.empty-replies :deep(.anticon) {
  color: #60a5fa;
}

/* 按钮样式 */
:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  color: white;
  font-weight: 600;
  letter-spacing: 0.3px;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.ant-btn-primary:hover) {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
}

/* 点赞/踩按钮状态样式 */
:deep(.ant-btn.liked) {
  background: linear-gradient(135deg, #10b981, #059669) !important;
  border-color: rgba(16, 185, 129, 0.3) !important;
  color: white !important;
  box-shadow: 0 0 15px rgba(16, 185, 129, 0.4) !important;
}

:deep(.ant-btn.liked:hover) {
  background: linear-gradient(135deg, #34d399, #10b981) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(16, 185, 129, 0.5) !important;
}

:deep(.ant-btn.disliked) {
  background: linear-gradient(135deg, #ef4444, #dc2626) !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
  color: white !important;
  box-shadow: 0 0 15px rgba(239, 68, 68, 0.4) !important;
}

:deep(.ant-btn.disliked:hover) {
  background: linear-gradient(135deg, #f87171, #ef4444) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(239, 68, 68, 0.5) !important;
}

/* 输入框样式 */
:deep(.ant-input),
:deep(.ant-input-affix-wrapper) {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  color: #ffffff;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

:deep(.ant-input:focus),
:deep(.ant-input-affix-wrapper:focus),
:deep(.ant-input-affix-wrapper-focused) {
  border-color: #3b82f6;
  box-shadow:
    0 0 0 3px rgba(59, 130, 246, 0.1),
    0 0 12px rgba(59, 130, 246, 0.2);
}

:deep(.ant-input::placeholder) {
  color: #94a3b8;
}

/* 头像样式 */
:deep(.ant-avatar) {
  border: 2px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.2);
  position: relative;
  z-index: 10;
  background: rgba(255, 255, 255, 0.1);
}

:deep(.ant-avatar img) {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

:deep(.ant-avatar .ant-avatar-icon) {
  color: #60a5fa;
}

/* 徽章样式 */
:deep(.ant-badge) {
  .ant-badge-count {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    border: 1px solid rgba(59, 130, 246, 0.3);
    color: white;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  }
}

/* 分割线样式 */
:deep(.ant-divider) {
  border-color: rgba(59, 130, 246, 0.1);
  background: linear-gradient(90deg, transparent, #3b82f6, transparent);
  height: 1px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .p3r-post-detail {
    padding: 12px;
  }

  .post-title {
    font-size: 20px;
  }

  .breadcrumb-card :deep(.ant-card-body) {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .reply-item {
    padding: 16px 0;
  }

  .post-body {
    font-size: 14px;
    padding: 16px;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 20, 25, 0.5);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
}

/* 动画 */
@keyframes gentleShine {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 隐藏全局返回顶部按钮，避免与页面元素冲突 */
:global(.z-popup.fixed.bottom-10) {
  display: none !important;
}

/* 用户头像样式 */
.clickable-avatar {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid rgba(59, 130, 246, 0.3);
}

.clickable-avatar:hover {
  transform: scale(1.1);
  border-color: #3b82f6;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
}

/* 关注按钮样式 */
.follow-section {
  display: flex;
  align-items: center;
}

.follow-btn {
  border-radius: 0;
  font-size: 12px;
  height: 28px;
  padding: 0 12px;
  font-weight: 600;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  clip-path: polygon(0 0, calc(100% - 6px) 0, 100% 6px, 100% 100%, 6px 100%, 0 calc(100% - 6px));
}

.follow-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.follow-btn-small {
  border-radius: 0;
  font-size: 10px;
  height: 24px;
  padding: 0 8px;
  font-weight: 600;
  letter-spacing: 0.3px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  clip-path: polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px));
}

.follow-btn-small:hover {
  transform: translateY(-1px) scale(1.05);
  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);
}

/* 内联关注按钮样式 */
.follow-btn-inline {
  border-radius: 0;
  font-size: 10px;
  height: 20px;
  padding: 0 8px;
  font-weight: 600;
  letter-spacing: 0.3px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  clip-path: polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px));
  margin: 0 4px;
  vertical-align: middle;
}

.follow-btn-inline:hover {
  transform: translateY(-1px) scale(1.05);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* 用户帖子弹窗样式 */
.user-posts-modal {
  max-height: 500px;
  overflow-y: auto;
}

.user-posts-list :deep(.ant-list-item) {
  border-bottom: 1px solid #e5e7eb;
  padding: 12px 0;
}

.user-post-item {
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.user-post-item:hover {
  background: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.user-post-content {
  width: 100%;
}

.user-post-title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.user-post-preview {
  font-size: 14px;
  color: #cbd5e1;
  line-height: 1.5;
  margin-bottom: 8px;
}

.user-post-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #94a3b8;
}

.user-post-time {
  color: #94a3b8;
}

.user-post-stats {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #60a5fa;
}

.user-posts-empty {
  text-align: center;
  padding: 40px 20px;
  color: #94a3b8;
}
</style>
