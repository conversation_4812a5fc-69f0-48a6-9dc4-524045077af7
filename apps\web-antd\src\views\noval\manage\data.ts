import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'id',
    label: '书本id',
  },
  {
    component: 'Input',
    fieldName: 'name',
    label: '书名',
  },
  {
    component: 'Input',
    fieldName: 'author',
    label: '作者',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.SYS_NOVAL_STATE 便于维护
      options: getDictOptions('sys_noval_state'),
    },
    fieldName: 'flag',
    label: '状态',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '书本id',
    field: 'id',
  },
  {
    title: '书名',
    field: 'name',
  },
  {
    title: '作者',
    field: 'author',
  },
  {
    title: '状态',
    field: 'flag',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.SYS_NOVAL_STATE 便于维护
        return renderDict(row.flag, 'sys_noval_state');
      },
    },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '书本id',
    fieldName: 'id',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '书名',
    fieldName: 'name',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '作者',
    fieldName: 'author',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '状态',
    fieldName: 'flag',
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.SYS_NOVAL_STATE 便于维护
      options: getDictOptions('sys_noval_state'),
    },
    rules: 'selectRequired',
  },
];
