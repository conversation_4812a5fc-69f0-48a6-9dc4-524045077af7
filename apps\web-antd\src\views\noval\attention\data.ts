import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';


export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'userName',
    label: '用户名',
    componentProps: {
      placeholder: '请输入要查找的用户名',
    },
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '头像',
    field: 'userAvatar',
    width: 80,
    slots: { default: 'userAvatar' },
  },
  {
    title: '用户名',
    field: 'userName',
    width: 200,
    slots: { default: 'userName' },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 120,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '我的id',
    fieldName: 'id',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '我关注的id',
    fieldName: 'otherId',
    component: 'Input',
    rules: 'required',
  },
];
