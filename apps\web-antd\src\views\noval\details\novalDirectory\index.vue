<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { PageQuery } from '#/api/common';
import type { DirectoryVO } from '#/api/noval/details/noval-directory-model';

import { ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';

import { Modal, Popconfirm, Space } from 'ant-design-vue';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import {
  novalDirectoryExport,
  novalDirectoryList,
  novalDirectoryRemove,
} from '#/api/noval/details/noval-directory';
import { commonDownloadExcel } from '#/utils/file/download';

import { emitter } from '../mitt';
import { columns, querySchema } from './data';
import novalDirectoryDrawer from './dict-data-drawer.vue';

const novalDirectory = ref('');

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    // trigger: 'row',
  },
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        const params: PageQuery = {
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        };
        if (novalDirectory.value) {
          params.id = novalDirectory.value;
        }

        return await novalDirectoryList(params);
      },
    },
  },
  rowConfig: {
    keyField: 'id',
  },
  id: 'noval-directory-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [NovalDirectoryDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: novalDirectoryDrawer,
});

function handleAdd() {
  drawerApi.setData({
  id: novalDirectory.value,
  isUpdate: false,
  });
  drawerApi.open();
}

async function handleEdit(record: DirectoryVO) {
  drawerApi.setData({
    isUpdate: true,
    id: record.id,
  });
  drawerApi.open();
}

async function handleDelete(row: DirectoryVO) {
  await novalDirectoryRemove([row.id], [row.chapter]);
  await tableApi.query();
}

function handleMultiDelete() {
  const rows = tableApi.grid.getCheckboxRecords();
  const ids = rows.map((row: DirectoryVO) => row.id);
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await novalDirectoryRemove(ids, ids);
      await tableApi.query();
    },
  });
}

function handleDownloadExcel() {
  commonDownloadExcel(novalDirectoryExport, '小说章节', tableApi.formApi.form.values);
}

emitter.on('rowClick', async (value) => {
  novalDirectory.value = value;
  await tableApi.query();
});
</script>

<template>
  <div>
    <BasicTable id="dict-data" table-title="小说章节列表">
      <template #toolbar-tools>
        <Space>
          <a-button
            v-access:code="['noval:directory:export']"
            @click="handleDownloadExcel"
          >
            {{ $t('pages.common.export') }}
          </a-button>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['noval:directory:remove']"
            @click="handleMultiDelete"
          >
            {{ $t('pages.common.delete') }}
          </a-button>
          <a-button
            :disabled="novalDirectory === ''"
            type="primary"
            v-access:code="['noval:directory:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <ghost-button
            v-access:code="['noval:directory:edit']"
            @click="handleEdit(row)"
          >
            {{ $t('pages.common.edit') }}
          </ghost-button>
          <Popconfirm
            :get-popup-container="
              (node) => getVxePopupContainer(node, 'dict-data')
            "
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <ghost-button
              danger
              v-access:code="['noval:directory:remove']"
              @click.stop=""
            >
              {{ $t('pages.common.delete') }}
            </ghost-button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <NovalDirectoryDrawer @reload="tableApi.query()" />
  </div>
</template>
