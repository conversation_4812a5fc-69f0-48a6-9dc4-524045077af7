<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { DetailsVO } from '#/api/noval/details/noval-details-model';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Space, Modal, Popconfirm, message } from 'ant-design-vue';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import { getVxePopupContainer } from '@vben/utils';
import {
  novalDetailsExport,
  novalDetailsList,
  novalDetailsRemove,
  novalDetailsTypeCache,
} from '#/api/noval/details/noval-details';
import { commonDownloadExcel } from '#/utils/file/download';
import {
  deleteRecordWithFiles,
  deleteRecordsWithFiles,
  extractDetailsUrls
} from '#/utils/file-delete-helper';

import { emitter } from '../mitt';
import { columns, querySchema } from './data';
import dictTypeModal from './dict-type-modal.vue';

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 70,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    // trigger: 'row',
  },
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await novalDetailsList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'id',
  },
  id: 'noval-details-index',
};

const lastid = ref('');

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    cellClick: (e) => {
      const { row } = e;
      if (lastid.value === row.id) {
        return;
      }
      emitter.emit('rowClick', row.id);
      lastid.value = row.id;
    },
  },
});
const [DictTypeModal, modalApi] = useVbenModal({
  connectedComponent: dictTypeModal,
});

function handleAdd() {
  modalApi.setData({});
  modalApi.open();
}

async function handleEdit(record: DetailsVO) {
  modalApi.setData({ id: record.id });
  modalApi.open();
}

async function handleDelete(row: DetailsVO) {
  try {
    await deleteRecordWithFiles(
      row,
      novalDetailsRemove,
      extractDetailsUrls
    );
    message.success('删除成功，已同时删除关联图片');
    await tableApi.query();
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败');
  }
}

function handleMultiDelete() {
  const rows = tableApi.grid.getCheckboxRecords();
  const ids = rows.map((row: DetailsVO) => row.id);
  Modal.confirm({
    title: '批量删除确认',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？\n\n⚠️ 注意：此操作将同时删除记录和关联的图片文件。`,
    okText: '确认删除',
    cancelText: '取消',
    onOk: async () => {
      try {
        await deleteRecordsWithFiles(
          rows,
          novalDetailsRemove,
          extractDetailsUrls
        );
        message.success(`成功删除${rows.length}条记录及关联图片`);
        await tableApi.query();
      } catch (error) {
        console.error('批量删除失败:', error);
        message.error('批量删除失败');
      }
    },
  });
}

async function handleRefreshCache() {
  await novalDetailsTypeCache();
  await tableApi.query();
}

function handleDownloadExcel() {
  commonDownloadExcel(
    novalDetailsExport,
    '小说详细',
    tableApi.formApi.form.values,
  );
}
</script>

<template>
  <div>
    <BasicTable id="id" table-title="小说详细">
      <template #toolbar-tools>
        <Space>
          <a-button
            v-access:code="['noval:details:edit']"
            @click="handleRefreshCache"
          >
            刷新缓存
          </a-button>
          <a-button
            v-access:code="['noval:details:export']"
            @click="handleDownloadExcel"
          >
            {{ $t('pages.common.export') }}
          </a-button>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['noval:details:remove']"
            @click="handleMultiDelete"
          >
            {{ $t('pages.common.delete') }}
          </a-button>
          <a-button
            type="primary"
            v-access:code="['noval:details:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <ghost-button
            v-access:code="['noval:details:edit']"
            @click.stop="handleEdit(row)"
          >
            {{ $t('pages.common.edit') }}
          </ghost-button>
          <Popconfirm
            :get-popup-container="
              (node) => getVxePopupContainer(node, 'id')
            "
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <ghost-button
              danger
              v-access:code="['noval:details:remove']"
              @click.stop=""
            >
              {{ $t('pages.common.delete') }}
            </ghost-button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <DictTypeModal @reload="tableApi.query()" />
  </div>
</template>
