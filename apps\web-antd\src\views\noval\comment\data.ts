import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'content',
    label: '评论内容',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '评论id',
    field: 'commerntId',
  },
  {
    title: '用户id',
    field: 'myId',
  },
  {
    title: '评论类型',
    field: 'commerntType',
    slots: {
      default: ({ row }) => {
        // 0代表楼主，1代表评论
        return renderDict(row.commerntType, 'commernt_type');
      },
    },
  },
  {
    title: '是否回复',
    field: 'isResponse',
    slots: {
      default: ({ row }) => {
        // 0为不是回复，1为回复
        return renderDict(row.isResponse, 'is_response');
      },
    },
  },
  {
    title: '是否精品',
    field: 'isFine',
    slots: {
      default: ({ row }) => {
        // 0为不是精品，1为精品
        return renderDict(row.isFine, 'noval_is_fine');
      },
    },
  },
  {
    title: '回复目标ID',
    field: 'responseId',
  },
  {
    title: '评论内容',
    field: 'content',
  },
  {
    title: '曝光数',
    field: 'look',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '评论id',
    fieldName: 'commerntId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '用户id',
    fieldName: 'myId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '评论类型',
    fieldName: 'commerntType',
    component: 'Select',
    componentProps: {
      // 0代表楼主，1代表评论
      options: getDictOptions('commernt_type'),
    },
    rules: 'selectRequired',
  },
  {
    label: '是否回复',
    fieldName: 'isResponse',
    component: 'Select',
    componentProps: {
      // 0为不是回复，1为回复
      options: getDictOptions('is_response'),
    },
    rules: 'selectRequired',
  },
  {
    label: '回复目标ID',
    fieldName: 'responseId',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '评论内容',
    fieldName: 'content',
    component: 'RichTextarea',
    componentProps: {
      // options: {
      //  readyonly: false, // 是否只读
      // }
      // width: '100%', // 宽度
      // showImageUpload: true, // 是否显示图片上传
      // height: 400 // 高度 默认400
    },
    rules: 'required',
  },
];
