<template>
  <div class="live2d-test-page">
    <div class="test-header">
      <h1>Live2D 测试页面</h1>
      <p>这是一个用于测试新 Live2D 组件的页面</p>
    </div>

    <div class="test-controls">
      <a-space>
        <a-button @click="toggleLive2D" type="primary">
          {{ showLive2D ? '隐藏' : '显示' }} Live2D
        </a-button>
        <a-button @click="switchModel">切换模型</a-button>
        <a-button @click="showCustomMessage">显示自定义消息</a-button>
        <a-button @click="showTimeMessage">显示时间消息</a-button>
        <a-button @click="takeScreenshot">截图</a-button>
      </a-space>
    </div>

    <div class="test-info">
      <a-card title="组件信息">
        <p><strong>当前状态:</strong> {{ showLive2D ? '显示中' : '已隐藏' }}</p>
        <p><strong>模型路径:</strong> {{ currentModelPath }}</p>
        <p><strong>PIXI.js 版本:</strong> {{ pixiVersion }}</p>
        <p><strong>设备类型:</strong> {{ deviceType }}</p>
      </a-card>
    </div>

    <!-- Live2D 组件 -->
    <Live2D v-if="showLive2D" ref="live2dRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Card as ACard, Button as AButton, Space as ASpace } from 'ant-design-vue'
import Live2D from '#/components/Live2D/index.vue'
import { LIVE2D_CONFIG, getTimeBasedMessage, getDeviceConfig } from '#/components/Live2D/config'

// 响应式数据
const showLive2D = ref(true)
const live2dRef = ref()
const currentModelPath = ref(LIVE2D_CONFIG.models[0].path)
const pixiVersion = ref('')

// 计算属性
const deviceType = computed(() => {
  const width = window.innerWidth
  if (width <= 768) return '移动设备'
  if (width <= 1024) return '平板设备'
  return '桌面设备'
})

// 方法
const toggleLive2D = () => {
  showLive2D.value = !showLive2D.value
}

const switchModel = () => {
  if (live2dRef.value) {
    // 切换到下一个模型
    const currentIndex = LIVE2D_CONFIG.models.findIndex(m => m.path === currentModelPath.value)
    const nextIndex = (currentIndex + 1) % LIVE2D_CONFIG.models.length
    currentModelPath.value = LIVE2D_CONFIG.models[nextIndex].path
    
    // 这里可以调用 Live2D 组件的方法来切换模型
    console.log('切换到模型:', currentModelPath.value)
  }
}

const showCustomMessage = () => {
  if (live2dRef.value) {
    live2dRef.value.showMessage('这是一条自定义测试消息！')
  }
}

const showTimeMessage = () => {
  if (live2dRef.value) {
    const timeMessage = getTimeBasedMessage()
    live2dRef.value.showMessage(timeMessage)
  }
}

const takeScreenshot = () => {
  if (live2dRef.value) {
    // 调用 Live2D 组件的截图方法
    console.log('截图功能测试')
  }
}

// 获取 PIXI 版本信息
const getPixiVersion = () => {
  try {
    if (window.PIXI) {
      pixiVersion.value = window.PIXI.VERSION || '未知'
    } else {
      pixiVersion.value = '未加载'
    }
  } catch (error) {
    pixiVersion.value = '获取失败'
  }
}

// 组件挂载
onMounted(() => {
  getPixiVersion()
  
  // 延迟获取版本信息，因为 PIXI 可能还没有加载
  setTimeout(() => {
    getPixiVersion()
  }, 1000)
})
</script>

<style scoped>
.live2d-test-page {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 32px;
}

.test-header h1 {
  color: #1890ff;
  margin-bottom: 8px;
}

.test-header p {
  color: #666;
  font-size: 16px;
}

.test-controls {
  margin-bottom: 32px;
  text-align: center;
}

.test-info {
  margin-bottom: 32px;
}

.test-info .ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-info p {
  margin-bottom: 8px;
  font-size: 14px;
}

.test-info strong {
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .live2d-test-page {
    padding: 16px;
  }
  
  .test-controls .ant-space {
    flex-wrap: wrap;
  }
  
  .test-controls .ant-btn {
    margin-bottom: 8px;
  }
}
</style>
