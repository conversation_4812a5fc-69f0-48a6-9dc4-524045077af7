<template>
  <!-- Live2D容器 -->
  <div
    v-if="!isHidden"
    class="live2d-container"
    :class="{ 'dragging': isDragging }"
    :style="{ right: position.right + 'px', bottom: position.bottom + 'px' }"
    @mousedown="handleMouseDown"
    @click="handleCanvasClick"
    @mouseenter="showToolbarOnHover"
    @mouseleave="hideToolbarOnLeave"
  >
    <canvas ref="liveCanvas"></canvas>

    <!-- 工具栏 - 只在悬停时显示 -->
    <div v-if="showToolbar" class="live2d-toolbar">
      <button @click="hideLive2D" title="隐藏看板娘">❌</button>
    </div>
  </div>

  <!-- 消息气泡 - 独立于Live2D容器 -->
  <div v-if="showMessage && !isHidden" class="live2d-message-bubble" :class="messageType">
    {{ currentMessage }}
  </div>

  <!-- 隐藏时的显示按钮 -->
  <div v-if="isHidden" class="live2d-show-button" @click="showLive2D">
    <span>🎭</span>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as PIXI from 'pixi.js'
import { Live2DModel } from 'pixi-live2d-display/cubism4'
import { live2dListByName, live2dList2 } from '#/api/noval/live2d'

// 为了 pixi-live2d-display 内部调用
window.PIXI = PIXI

// 响应式数据
const liveCanvas = ref<HTMLCanvasElement>()
const showMessage = ref(false)
const currentMessage = ref('')
const messageType = ref('normal')
const autoMode = ref(true)
const isHidden = ref(false)
const showToolbar = ref(false)

// 拖拽相关状态
const isDragging = ref(false)
const dragStarted = ref(false)
const dragOffset = ref({ x: 0, y: 0 })
const position = ref({ right: 20, bottom: 20 }) // 初始位置

let app: PIXI.Application | null = null
let model: any = null
let autoTimer: number | null = null
let toolbarTimer: number | null = null

// 丰富的面部表情参数（基于cdi3.json）
const faceExpressions = [
  { name: '开心', params: { 'ParamMouthForm': 1, 'ParamBrowLY': 0.5, 'ParamBrowRY': 0.5 } },
  { name: '生气', params: { 'ParamMouthForm': -0.8, 'ParamBrowLY': -0.8, 'ParamBrowRY': -0.8 } },
  { name: '惊讶', params: { 'ParamMouthOpenY': 0.8, 'ParamBrowLY': 0.8, 'ParamBrowRY': 0.8 } },
  { name: '眨眼', params: { 'ParamEyeLOpen': 0, 'ParamEyeROpen': 0 } },
  { name: '微笑', params: { 'ParamMouthForm': 0.6, 'ParamBrowLY': 0.3, 'ParamBrowRY': 0.3 } },
  { name: '害羞', params: { 'ParamMouthForm': 0.3, 'ParamBrowLY': 0.2, 'ParamBrowRY': 0.2, 'ParamEyeLOpen': 0.7, 'ParamEyeROpen': 0.7 } },
  { name: '困惑', params: { 'ParamMouthForm': -0.3, 'ParamBrowLY': -0.3, 'ParamBrowRY': 0.3 } },
  { name: '得意', params: { 'ParamMouthForm': 0.8, 'ParamBrowLY': 0.6, 'ParamBrowRY': 0.6, 'ParamEyeLOpen': 0.8, 'ParamEyeROpen': 0.8 } },
  { name: '无奈', params: { 'ParamMouthForm': -0.2, 'ParamBrowLY': -0.4, 'ParamBrowRY': -0.4 } },
  { name: '调皮', params: { 'ParamMouthForm': 0.5, 'ParamEyeLOpen': 0.3, 'ParamEyeROpen': 1.0 } },
  { name: '思考', params: { 'ParamMouthForm': 0.1, 'ParamBrowLY': 0.2, 'ParamBrowRY': -0.2 } },
  { name: '撒娇', params: { 'ParamMouthForm': 0.4, 'ParamBrowLY': 0.4, 'ParamBrowRY': 0.4, 'ParamEyeLOpen': 0.6, 'ParamEyeROpen': 0.6 } }
]

// 服装配饰系统（基于exp3文件）
const outfits = [
  { name: 'JK包', file: 'jk包' },
  { name: '戴帽子', file: '戴帽子' },
  { name: '手柄', file: '手柄' },
  { name: '脱外套', file: '脱外套' },
  { name: '裙子', file: '裙子' },
  { name: '马尾L隐藏', file: '马尾L隐藏' },
  { name: '马尾R隐藏', file: '马尾R隐藏' }
]

// 特殊表情（眼睛相关）
const specialExpressions = [
  { name: '圈圈眼', file: '圈圈眼' },
  { name: '爱心眼', file: '爱心眼' },
  { name: '黑化', file: '黑化' }
]

// 身体动作系统
const bodyActions = [
  {
    name: '点头',
    params: { 'ParamAngleY': 0, 'ParamAngleX': 5, 'ParamAngleZ': 0 },
    duration: 800,
    description: '轻轻点头表示同意'
  },
  {
    name: '摇头',
    params: { 'ParamAngleY': 10, 'ParamAngleX': 0, 'ParamAngleZ': 0 },
    duration: 1000,
    description: '左右摇头表示否定'
  },
  {
    name: '歪头',
    params: { 'ParamAngleY': 0, 'ParamAngleX': 0, 'ParamAngleZ': 15 },
    duration: 1500,
    description: '可爱地歪着头思考'
  },
  {
    name: '左看',
    params: { 'ParamAngleY': -20, 'ParamAngleX': 0, 'ParamAngleZ': 0 },
    duration: 1200,
    description: '向左边看去'
  },
  {
    name: '右看',
    params: { 'ParamAngleY': 20, 'ParamAngleX': 0, 'ParamAngleZ': 0 },
    duration: 1200,
    description: '向右边看去'
  },
  {
    name: '抬头',
    params: { 'ParamAngleY': 0, 'ParamAngleX': -10, 'ParamAngleZ': 0 },
    duration: 1000,
    description: '抬头看向上方'
  },
  {
    name: '低头',
    params: { 'ParamAngleY': 0, 'ParamAngleX': 10, 'ParamAngleZ': 0 },
    duration: 1000,
    description: '害羞地低下头'
  }
]

// 复合动作（表情+身体动作）
const complexActions = [
  {
    name: '害羞点头',
    faceExpression: { name: '害羞', params: { 'ParamMouthForm': 0.3, 'ParamBrowLY': 0.2, 'ParamBrowRY': 0.2, 'ParamEyeLOpen': 0.7, 'ParamEyeROpen': 0.7 } },
    bodyAction: { name: '点头', params: { 'ParamAngleY': 0, 'ParamAngleX': 5, 'ParamAngleZ': 0 } },
    message: '嗯嗯~人家知道了啦~'
  },
  {
    name: '调皮歪头',
    faceExpression: { name: '调皮', params: { 'ParamMouthForm': 0.5, 'ParamEyeLOpen': 0.3, 'ParamEyeROpen': 1.0 } },
    bodyAction: { name: '歪头', params: { 'ParamAngleY': 0, 'ParamAngleX': 0, 'ParamAngleZ': 15 } },
    message: '嘿嘿~这样看起来怎么样？'
  },
  {
    name: '思考抬头',
    faceExpression: { name: '思考', params: { 'ParamMouthForm': 0.1, 'ParamBrowLY': 0.2, 'ParamBrowRY': -0.2 } },
    bodyAction: { name: '抬头', params: { 'ParamAngleY': 0, 'ParamAngleX': -10, 'ParamAngleZ': 0 } },
    message: '让我想想看...'
  },
  {
    name: '得意左看',
    faceExpression: { name: '得意', params: { 'ParamMouthForm': 0.8, 'ParamBrowLY': 0.6, 'ParamBrowRY': 0.6 } },
    bodyAction: { name: '左看', params: { 'ParamAngleY': -20, 'ParamAngleX': 0, 'ParamAngleZ': 0 } },
    message: '哼哼~我很厉害吧！'
  }
]

// Live2D 配置 - 优先使用本地模型，支持云端模型
const live2dConfig = {
  modelAPI: '/live2d/真夜白音/',  // 本地模型路径
  cloudModelAPI: 'https://ruoyi-1363865599.cos-website.ap-guangzhou.myqcloud.com/live2d/真夜白音/',  // 云端模型路径
  modelId: 1,
  modelTexturesId: 0,
  waifuSize: [300, 400],
  showHitokoto: true,
  showWelcomeMessage: true,
  hitAreas: {
    head: { x: [-0.35, 0.35], y: [0.19, 0.79] },
    body: { x: [-0.3, 0.3], y: [-0.9, 0.3] }
  }
}

// 台词库
const messages = {
  welcome: [
    '你好呀！我是阿留，很高兴见到你~',
    '欢迎来到这里！今天也要加油哦！',
    '嗨！要一起度过愉快的时光吗？'
  ],
  idle: [
    '今天天气不错呢~',
    '要不要一起学习呢？',
    '记得多喝水哦！',
    '工作累了就休息一下吧~',
    '你今天也很棒呢！',
    '要保持好心情哦~'
  ],
  touch: [
    '呀！不要乱摸啦~',
    '好痒好痒！',
    '讨厌~人家会害羞的',
    '你想干什么呢？',
    '再这样我要生气了哦！'
  ],
  praise: [
    '谢谢夸奖！',
    '人家会害羞的~',
    '嘿嘿，你也很棒呢！',
    '真的吗？好开心！'
  ],
  expression: [
    '这个表情怎么样？',
    '我还有很多表情呢~',
    '喜欢这个表情吗？',
    '换个心情换个表情！',
    '每个表情都有不同的感觉哦~',
    '看我的新表情！',
    '表情管理大师就是我~',
    '这样的我可爱吗？',
    '表情包又增加了呢！'
  ],
  action: [
    '让我做个动作给你看~',
    '我会很多有趣的动作哦！',
    '这个动作帅气吗？',
    '看我的特技表演！',
    '动作演示开始！',
    '我的动作库很丰富呢~',
    '要不要再来一个动作？',
    '这就是我的看家本领！'
  ],
  outfit: [
    '这套衣服好看吗？',
    '换个造型试试~',
    '我有很多漂亮的衣服呢！',
    '今天穿这个怎么样？',
    '时尚就是要不断变化！'
  ],
  hide: [
    '要休息一下吗？',
    '我先隐藏一下~',
    '想我的时候就点击召唤我吧！',
    '拜拜~记得想我哦！'
  ],
  time: {
    morning: ['早上好！新的一天开始了~', '早安！今天也要元气满满！'],
    noon: ['中午了，记得吃午饭哦~', '午休时间到了！'],
    afternoon: ['下午好！工作辛苦了~', '下午茶时间到了！'],
    evening: ['晚上好！今天辛苦了~', '该休息了哦~', '晚安！做个好梦~']
  }
}

// 当前模型信息
const currentModel = ref({
  name: '',
  url: '',
  files: []
})

// 从数据库获取默认模型
const getDefaultModelFromDB = async () => {
  try {
    console.log('🔍 获取数据库中的默认模型: 真夜白音')

    // 使用统一的list2 API查询指定名字的模型
    const records = await live2dList2({
      name: '真夜白音',
      type: null
    })

    console.log('📊 查询到的记录数:', records.length)

    if (records && records.length > 0) {
      // 查找主模型文件（.model3.json）
      const mainModelRecord = records.find(record =>
        record.originalName && record.originalName.endsWith('.model3.json')
      )

      if (mainModelRecord) {
        console.log('✅ 找到数据库中的默认模型:', mainModelRecord.icon)
        return {
          modelUrl: mainModelRecord.icon,
          modelName: '真夜白音',
          allFiles: records
        }
      } else {
        console.warn('⚠️ 未找到主模型文件(.model3.json)')
        console.log('📋 可用文件:', records.map(r => r.originalName))
      }
    } else {
      console.warn('⚠️ 数据库中未找到"真夜白音"模型')
    }
  } catch (error) {
    console.error('❌ 获取数据库模型失败:', error)
  }

  return null
}

// 初始化Live2D
const initLive2D = async (modelUrl?: string) => {
  if (!liveCanvas.value) return

  try {
    console.log('🎭 Initializing Live2D...')

    // 创建PIXI应用
    if (!app) {
      app = new PIXI.Application({
        view: liveCanvas.value,
        autoStart: true,
        backgroundAlpha: 0,
        width: 280,
        height: 300
      })
    }

    // 清除之前的模型
    if (model) {
      app.stage.removeChild(model)
      model.destroy()
      model = null
    }

    // 使用传入的模型URL或默认本地模型
    const targetModelUrl = modelUrl || `${live2dConfig.modelAPI}真夜白音.model3.json`
    console.log('📁 Loading model from:', targetModelUrl)

    try {
      model = await Live2DModel.from(targetModelUrl)

      if (model) {
        console.log('✅ Model loaded successfully')

        app.stage.addChild(model)
        model.scale.set(0.08)
        model.x = app.screen.width / 2
        model.y = app.screen.height * 1.1
        model.anchor.set(0.5, 0.5)
        model.interactive = true
        model.buttonMode = true

        setupInteraction()
        checkAvailableMotions()

        setTimeout(() => {
          displayMessage(getRandomMessage('welcome'), 'welcome')
        }, 1000)

        startAutoMode()
        console.log('🎉 Live2D initialization complete!')
        return // 成功加载，直接返回
      }
    } catch (modelError) {
      console.warn('⚠️ Failed to load model from:', targetModelUrl, modelError)
    }

    // 如果是云端模型加载失败，尝试本地模型
    if (modelUrl && !modelUrl.startsWith('/live2d/')) {
      console.log('🔄 Fallback to local model...')
      try {
        const localModelUrl = `${live2dConfig.modelAPI}真夜白音.model3.json`
        console.log('📁 Loading local model from:', localModelUrl)

        model = await Live2DModel.from(localModelUrl)

        if (model) {
          console.log('✅ Local model loaded successfully')
          app.stage.addChild(model)
          model.scale.set(0.08)
          model.x = app.screen.width / 2
          model.y = app.screen.height * 1.1
          model.anchor.set(0.5, 0.5)
          model.interactive = true
          model.buttonMode = true

          setupInteraction()
          checkAvailableMotions()

          setTimeout(() => {
            displayMessage('已切换到本地模型', 'normal')
          }, 1000)

          startAutoMode()
          console.log('🎉 Local Live2D initialization complete!')
          return
        }
      } catch (localError) {
        console.error('❌ Local model also failed:', localError)
      }
    }

    // 如果所有模型都加载失败
    console.error('❌ All models failed to load')
    displayMessage('Live2D模型加载失败', 'normal', 5000)
  } catch (error) {
    console.error('❌ Live2D initialization failed:', error)
  }
}

// 监听应用模型事件
const handleApplyModel = (event: CustomEvent) => {
  const { modelName, modelUrl, allFiles } = event.detail
  console.log('🎯 Applying Live2D model:', modelName, modelUrl)

  currentModel.value = {
    name: modelName,
    url: modelUrl,
    files: allFiles
  }

  // 重新初始化模型
  initLive2D(modelUrl)
  displayMessage(`已切换到 "${modelName}" 模型`, 'normal', 3000)
}



// 初始化
onMounted(async () => {
  // 监听应用模型事件
  window.addEventListener('applyLive2DModel', handleApplyModel as EventListener)

  // 先尝试从数据库获取默认模型
  const dbModelData = await getDefaultModelFromDB()

  if (dbModelData) {
    console.log('🎯 使用数据库中的模型:', dbModelData.modelUrl)
    currentModel.value = {
      name: dbModelData.modelName,
      url: dbModelData.modelUrl,
      files: dbModelData.allFiles
    }
    await initLive2D(dbModelData.modelUrl)
  } else {
    console.log('🔄 回退到本地默认模型')
    await initLive2D()
  }
})

// 检查可用的动作和表情
const checkAvailableMotions = () => {
  if (!model) return

  try {
    // 检查模型的内部结构
    console.log('Model structure:', model)

    // 尝试获取动作定义
    if (model.internalModel) {
      console.log('Internal model:', model.internalModel)

      // 检查动作管理器
      if (model.internalModel.motionManager) {
        console.log('Motion manager:', model.internalModel.motionManager)
        console.log('Motion definitions:', model.internalModel.motionManager.definitions)
      }

      // 检查表情管理器
      if (model.internalModel.motionManager?.expressionManager) {
        console.log('Expression manager:', model.internalModel.motionManager.expressionManager)
        console.log('Expression definitions:', model.internalModel.motionManager.expressionManager.definitions)
      }

      // 检查表情管理器的另一种方式
      if (model.internalModel.expressionManager) {
        console.log('Expression manager (direct):', model.internalModel.expressionManager)
        console.log('Expression definitions (direct):', model.internalModel.expressionManager.definitions)
      }
    }

    // 尝试播放默认表情
    setTimeout(() => {
      console.log('🎭 Starting initial expression...')
      playRandomExpression()
    }, 500)

  } catch (error) {
    console.warn('Error checking motions:', error)
  }
}

// 获取随机消息
const getRandomMessage = (category: string, subcategory?: string): string => {
  const msgs = messages as any
  let messageArray: string[] = []

  if (subcategory && msgs[category] && msgs[category][subcategory]) {
    messageArray = msgs[category][subcategory]
  } else if (msgs[category]) {
    messageArray = Array.isArray(msgs[category]) ? msgs[category] : []
  }

  if (messageArray.length === 0) return '...'
  return messageArray[Math.floor(Math.random() * messageArray.length)]
}

// 显示消息
const displayMessage = (message: string, type: string = 'normal', duration: number = 3000) => {
  console.log('💬 displayMessage called:', { message, type, duration })

  currentMessage.value = message
  messageType.value = type
  showMessage.value = true

  console.log('💬 Message state:', {
    currentMessage: currentMessage.value,
    messageType: messageType.value,
    showMessage: showMessage.value
  })

  setTimeout(() => {
    console.log('💬 Hiding message after', duration, 'ms')
    showMessage.value = false
  }, duration)
}

// 设置交互
const setupInteraction = () => {
  if (!model || !liveCanvas.value) return

  // 交互现在由容器的点击事件处理，不需要在canvas上设置
  // 移除鼠标悬停触发，只保留点击触发
}

// 播放面部表情（通过参数控制）
const playFaceExpression = (expressionData: any) => {
  if (!model || !model.internalModel) {
    console.warn('❌ Model or internal model not available for face expression')
    return
  }

  try {
    const coreModel = model.internalModel.coreModel
    if (coreModel) {
      console.log('🎭 Playing face expression:', expressionData.name)

      // 重置所有表情参数
      resetFaceParameters()

      let successCount = 0
      // 设置新的表情参数
      for (const [paramId, value] of Object.entries(expressionData.params)) {
        const paramIndex = coreModel.getParameterIndex(paramId)
        if (paramIndex >= 0) {
          coreModel.setParameterValueByIndex(paramIndex, value as number)
          console.log(`✅ Set parameter ${paramId} to ${value}`)
          successCount++
        } else {
          console.warn(`❌ Parameter ${paramId} not found`)
        }
      }

      console.log(`✅ Face expression applied: ${expressionData.name} (${successCount}/${Object.keys(expressionData.params).length} parameters)`)
      displayMessage(`${expressionData.name}的表情~`, 'expression')
    } else {
      console.warn('❌ Core model not available')
    }
  } catch (error) {
    console.warn('❌ Error playing face expression:', error)
  }
}

// 重置面部参数
const resetFaceParameters = () => {
  if (!model || !model.internalModel) return

  try {
    const coreModel = model.internalModel.coreModel
    if (coreModel) {
      // 重置表情相关参数到默认值
      const resetParams = {
        'ParamMouthForm': 0,
        'ParamMouthOpenY': 0,
        'ParamBrowLY': 0,
        'ParamBrowRY': 0,
        'ParamBrowLForm': 0,
        'ParamBrowRForm': 0
      }

      for (const [paramId, value] of Object.entries(resetParams)) {
        const paramIndex = coreModel.getParameterIndex(paramId)
        if (paramIndex >= 0) {
          coreModel.setParameterValueByIndex(paramIndex, value)
        }
      }
    }
  } catch (error) {
    console.warn('Error resetting face parameters:', error)
  }
}

// 重置身体参数
const resetBodyParameters = () => {
  if (!model || !model.internalModel) return

  try {
    const coreModel = model.internalModel.coreModel
    if (coreModel) {
      // 重置身体动作相关参数到默认值
      const resetParams = {
        'ParamAngleX': 0,
        'ParamAngleY': 0,
        'ParamAngleZ': 0,
        'ParamBodyAngleX': 0,
        'ParamBodyAngleY': 0,
        'ParamBodyAngleZ': 0
      }

      for (const [paramId, value] of Object.entries(resetParams)) {
        const paramIndex = coreModel.getParameterIndex(paramId)
        if (paramIndex >= 0) {
          coreModel.setParameterValueByIndex(paramIndex, value)
        }
      }
    }
  } catch (error) {
    console.warn('Error resetting body parameters:', error)
  }
}

// 播放身体动作
const playBodyAction = (actionData: any) => {
  if (!model || !model.internalModel) {
    console.warn('❌ Model or internal model not available for body action')
    return
  }

  try {
    const coreModel = model.internalModel.coreModel
    if (coreModel) {
      console.log('🎬 Playing body action:', actionData.name)

      // 检查coreModel的可用方法
      console.log('🔍 CoreModel methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(coreModel)))

      // 重置身体参数
      resetBodyParameters()

      let successCount = 0
      // 设置新的身体动作参数
      for (const [paramId, value] of Object.entries(actionData.params)) {
        try {
          const paramIndex = coreModel.getParameterIndex(paramId)
          if (paramIndex >= 0) {
            coreModel.setParameterValueByIndex(paramIndex, value as number)
            console.log(`✅ Set body parameter ${paramId} to ${value}`)
            successCount++
          } else {
            console.warn(`❌ Body parameter ${paramId} not found (index: ${paramIndex})`)
          }
        } catch (paramError) {
          console.warn(`❌ Error setting parameter ${paramId}:`, paramError)
        }
      }

      console.log(`✅ Body action applied: ${actionData.name} (${successCount}/${Object.keys(actionData.params).length} parameters)`)
      displayMessage(`${actionData.description}`, 'action')

      // 动作持续时间后重置
      if (actionData.duration) {
        setTimeout(() => {
          resetBodyParameters()
          console.log(`🔄 Reset body action: ${actionData.name}`)
        }, actionData.duration)
      }
    } else {
      console.warn('❌ Core model not available')
    }
  } catch (error) {
    console.warn('❌ Error playing body action:', error)
  }
}

// 播放复合动作（表情+身体动作）
const playComplexAction = (complexActionData: any) => {
  if (!model || !model.internalModel) {
    console.warn('❌ Model not available for complex action')
    return
  }

  console.log('🎭🎬 Playing complex action:', complexActionData.name)

  // 同时播放表情和身体动作
  playFaceExpression(complexActionData.faceExpression)

  setTimeout(() => {
    playBodyAction(complexActionData.bodyAction)
  }, 200) // 稍微延迟身体动作，让效果更自然

  // 显示专属消息
  setTimeout(() => {
    displayMessage(complexActionData.message, 'action')
  }, 400)
}

// 播放特殊表情（通过exp3文件）
const playSpecialExpression = (expressionFile: string) => {
  if (!model) return

  try {
    console.log('Playing special expression:', expressionFile)

    // 简化的表情播放
    if (typeof model.expression === 'function') {
      model.expression(expressionFile)
      return
    }

    if (model.internalModel?.expressionManager?.setExpression) {
      model.internalModel.expressionManager.setExpression(expressionFile)
      return
    }

    if (typeof model.motion === 'function') {
      model.motion(expressionFile)
      return
    }

  } catch (error) {
    console.warn('❌ Special expression error:', error)
  }
}

// 换装功能
const changeOutfit = () => {
  const randomOutfit = outfits[Math.floor(Math.random() * outfits.length)]
  playSpecialExpression(randomOutfit.file)
  displayMessage(getRandomMessage('outfit'), 'praise')
  console.log('Changed to outfit:', randomOutfit.name)
}

// 随机动作表演
const playRandomAction = () => {
  const random = Math.random()

  if (random < 0.3) {
    // 30% 概率播放面部表情
    const randomFaceExpression = faceExpressions[Math.floor(Math.random() * faceExpressions.length)]
    playFaceExpression(randomFaceExpression)
  } else if (random < 0.5) {
    // 20% 概率播放身体动作
    const randomBodyAction = bodyActions[Math.floor(Math.random() * bodyActions.length)]
    playBodyAction(randomBodyAction)
  } else if (random < 0.7) {
    // 20% 概率播放复合动作
    const randomComplexAction = complexActions[Math.floor(Math.random() * complexActions.length)]
    playComplexAction(randomComplexAction)
  } else if (random < 0.9) {
    // 20% 概率播放特殊表情
    const randomSpecialExpression = specialExpressions[Math.floor(Math.random() * specialExpressions.length)]
    playSpecialExpression(randomSpecialExpression.file)
    displayMessage(`${randomSpecialExpression.name}的特效~`, 'expression')
  } else {
    // 10% 概率换装
    changeOutfit()
  }
}

// 保持原有的随机表情函数（用于初始化）
const playRandomExpression = () => {
  const randomFaceExpression = faceExpressions[Math.floor(Math.random() * faceExpressions.length)]
  playFaceExpression(randomFaceExpression)
}

// 拖拽功能
const handleMouseDown = (event: MouseEvent) => {
  const container = event.currentTarget as HTMLElement
  const rect = container.getBoundingClientRect()

  dragOffset.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }

  dragStarted.value = false // 重置拖拽开始标志

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  event.preventDefault()
}

const handleMouseMove = (event: MouseEvent) => {
  // 只有移动了一定距离才算开始拖拽
  if (!dragStarted.value) {
    dragStarted.value = true
    isDragging.value = true
  }

  if (!isDragging.value) return

  const newRight = window.innerWidth - event.clientX + dragOffset.value.x - 280 // 280是容器宽度
  const newBottom = window.innerHeight - event.clientY + dragOffset.value.y - 300 // 300是容器高度

  // 限制在屏幕范围内
  position.value.right = Math.max(0, Math.min(newRight, window.innerWidth - 280))
  position.value.bottom = Math.max(0, Math.min(newBottom, window.innerHeight - 300))
}

const handleMouseUp = () => {
  // 延迟重置拖拽状态，避免立即触发点击事件
  setTimeout(() => {
    isDragging.value = false
    dragStarted.value = false
  }, 100)

  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

// 处理点击事件（区分拖拽和点击）
const handleCanvasClick = (event: MouseEvent) => {
  // 如果刚刚结束拖拽，不触发点击事件
  if (isDragging.value) return

  // 触发Live2D交互 - 复制原来canvas点击的逻辑
  console.log('Live2D clicked!') // 调试信息

  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  console.log('Click position:', x, y) // 调试信息

  // 只显示触摸反应消息
  const touchMessage = getRandomMessage('touch')
  console.log('Touch message:', touchMessage) // 调试信息
  displayMessage(touchMessage, 'touch')
}

// 隐藏Live2D
const hideLive2D = () => {
  displayMessage(getRandomMessage('hide'), 'normal', 2000)
  setTimeout(() => {
    isHidden.value = true
    stopAutoMode()
  }, 2000)
}

// 显示Live2D
const showLive2D = () => {
  isHidden.value = false
  displayMessage('我回来啦！想我了吗？', 'welcome')
}

// 鼠标悬停显示工具栏
const showToolbarOnHover = () => {
  showToolbar.value = true
  // 清除之前的隐藏定时器
  if (toolbarTimer) {
    clearTimeout(toolbarTimer)
    toolbarTimer = null
  }
}

// 鼠标离开后延迟隐藏工具栏
const hideToolbarOnLeave = () => {
  // 3秒后隐藏工具栏
  toolbarTimer = window.setTimeout(() => {
    showToolbar.value = false
    toolbarTimer = null
  }, 3000)
}

// 测试消息函数已移除

// 随机台词
const sayRandomMessage = () => {
  console.log('sayRandomMessage called') // 调试信息

  const hour = new Date().getHours()
  let category = 'idle'

  if (hour >= 6 && hour < 12) category = 'morning'
  else if (hour >= 12 && hour < 14) category = 'noon'
  else if (hour >= 14 && hour < 18) category = 'afternoon'
  else if (hour >= 18 || hour < 6) category = 'evening'

  console.log('Current hour:', hour, 'Category:', category) // 调试信息

  const message = hour >= 6 && hour <= 22
    ? getRandomMessage('time', category)
    : getRandomMessage('idle')

  console.log('Random message:', message) // 调试信息
  displayMessage(message, 'normal')
}

// 自动随机动作模式（已默认开启）

const startAutoMode = () => {
  if (autoTimer) return

  autoTimer = window.setInterval(() => {
    const random = Math.random()

    if (random < 0.4) {
      // 40% 概率播放表情/动作
      playRandomAction()
    } else if (random < 0.7) {
      // 30% 概率说随机台词
      sayRandomMessage()
    } else if (random < 0.9) {
      // 20% 概率换装
      changeOutfit()
    }
    // 10% 概率什么都不做，保持自然
  }, 30000) // 每30秒执行一次
}

const stopAutoMode = () => {
  if (autoTimer) {
    clearInterval(autoTimer)
    autoTimer = null
  }
}

// 清理
onBeforeUnmount(() => {
  stopAutoMode()
  if (toolbarTimer) {
    clearTimeout(toolbarTimer)
    toolbarTimer = null
  }
  // 移除事件监听器
  window.removeEventListener('applyLive2DModel', handleApplyModel as EventListener)
  model?.destroy()
  app?.destroy()
})

// 暴露方法
defineExpose({
  showMessage: displayMessage,
  playFaceExpression,
  playSpecialExpression,
  changeOutfit,
  sayRandomMessage,
  hideLive2D,
  showLive2D
})
</script>

<style scoped>
.live2d-container {
  position: fixed;
  z-index: 9999;
  pointer-events: auto;
  width: 280px;
  height: 300px;
  overflow: hidden;
  cursor: move;
  transition: transform 0.1s ease;
  user-select: none;
}

.live2d-container.dragging {
  cursor: grabbing;
  transform: scale(1.02);
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.3));
}

.live2d-container canvas {
  pointer-events: none; /* 禁用canvas的事件，让容器处理拖拽 */
  width: 100%;
  height: 100%;
}

/* 消息气泡 - 独立定位 */
.live2d-message-bubble {
  position: fixed;
  bottom: 200px;  /* 大幅下移，更贴近Live2D */
  right: 50px;    /* 与Live2D对齐 */
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 12px 16px;
  border-radius: 20px;
  font-size: 14px;
  white-space: nowrap;
  max-width: 250px;
  text-align: center;
  animation: bubbleSlideIn 0.4s ease-out;
  pointer-events: none;
  z-index: 99999;  /* 超高z-index */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

/* 气泡尾巴 */
.live2d-message-bubble::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid rgba(0, 0, 0, 0.9);
}

.live2d-message-bubble.welcome {
  background: linear-gradient(45deg, #ff6b6b, #feca57) !important;
  border-color: rgba(255, 107, 107, 0.5) !important;
}

.live2d-message-bubble.welcome::after {
  border-top-color: #ff6b6b !important;
}

.live2d-message-bubble.touch {
  background: linear-gradient(45deg, #ff9ff3, #f368e0) !important;
  border-color: rgba(255, 159, 243, 0.5) !important;
}

.live2d-message-bubble.touch::after {
  border-top-color: #ff9ff3 !important;
}

.live2d-message-bubble.normal {
  background: rgba(0, 0, 0, 0.9) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
}

.live2d-message-bubble.normal::after {
  border-top-color: rgba(0, 0, 0, 0.9) !important;
}

.live2d-message-bubble.expression {
  background: linear-gradient(45deg, #48cae4, #0077b6) !important;
  border-color: rgba(72, 202, 228, 0.5) !important;
}

.live2d-message-bubble.expression::after {
  border-top-color: #48cae4 !important;
}

.live2d-message-bubble.praise {
  background: linear-gradient(45deg, #54a0ff, #5f27cd) !important;
  border-color: rgba(84, 160, 255, 0.5) !important;
}

.live2d-message-bubble.praise::after {
  border-top-color: #54a0ff !important;
}

.live2d-message-bubble.action {
  background: linear-gradient(45deg, #a8e6cf, #88d8a3) !important;
  border-color: rgba(168, 230, 207, 0.5) !important;
}

.live2d-message-bubble.action::after {
  border-top-color: #a8e6cf !important;
}

/* 工具栏 */
.live2d-toolbar {
  position: absolute;
  top: 100px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  pointer-events: auto;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.live2d-toolbar:hover {
  opacity: 1;
}

.live2d-toolbar button {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 16px;
}

.live2d-toolbar button:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

.live2d-toolbar button.active {
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  color: white;
}

/* 气泡动画 */
@keyframes bubbleSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 显示按钮（隐藏时） */
.live2d-show-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 9999;
  animation: pulse 2s infinite;
}

.live2d-show-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.live2d-show-button span {
  font-size: 24px;
  color: white;
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
  50% {
    box-shadow: 0 4px 25px rgba(255, 107, 107, 0.4);
  }
  100% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
}

/* 响应式 */
@media (max-width: 768px) {
  .live2d-container {
    width: 220px;
    height: 250px;
    bottom: 0;
    right: 10px;
  }

  .live2d-toolbar button {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }

  .live2d-message-bubble {
    font-size: 12px;
    max-width: 180px;
    bottom: 170px;  /* 移动端大幅下移 */
    right: 30px;
  }

  .live2d-show-button {
    width: 50px;
    height: 50px;
    bottom: 15px;
    right: 15px;
  }

  .live2d-show-button span {
    font-size: 20px;
  }
}
</style>
