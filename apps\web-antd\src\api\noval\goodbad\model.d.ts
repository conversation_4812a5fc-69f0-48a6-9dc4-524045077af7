import type { PageQuery, BaseEntity } from '#/api/common';

export interface GoodbadVO {
  /**
   * 主键ID
   */
  id?: number | string;

  /**
   * 评论id
   */
  commerntId: number | string;

  /**
   * 执行人id
   */
  whoId: number | string;

  /**
   * 1代表赞，0代表踩
   */
  goodBad: number;
}

export interface GoodbadForm extends BaseEntity {
  /**
   * 评论id
   */
  commerntId?: number | string;

  /**
   * 执行人id
   */
  whoId?: number | string;

  /**
   * 1代表赞，0代表踩
   */
  goodBad?: number;
}

export interface GoodbadQuery extends PageQuery {
  /**
   * 评论id
   */
  commerntId?: number | string;

  /**
   * 执行人id
   */
  whoId?: number | string;

  /**
   * 1代表赞，0代表踩
   */
  goodBad?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
