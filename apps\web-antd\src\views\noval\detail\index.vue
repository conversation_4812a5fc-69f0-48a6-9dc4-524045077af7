<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { NovalForYhInfo } from '#/api/noval/NovalForYh';
import { novalDirectoryList, novalDirectoryInfo } from '#/api/noval/details/noval-directory';
import { getDictOptions } from '#/utils/dict';
import type { NovalForYhVO } from '#/api/noval/NovalForYh/model';
import type { DirectoryVO } from '#/api/noval/details/noval-directory-model';

const route = useRoute();
const router = useRouter();

// 响应式数据
const novelInfo = ref<NovalForYhVO | null>(null);
const chapterList = ref<DirectoryVO[]>([]);
const loading = ref(false);
const chapterLoading = ref(false);

// 分页数据
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
});

// 搜索表单
const searchForm = reactive({
  chaptername: '',
});



// 获取字典数据
const platformOptions = getDictOptions('sys_noval_platform');
const stateOptions = getDictOptions('sys_noval_state');
const typeOptions = getDictOptions('sys_noval_type');

// 字典值转换为标签
const getDictLabel = (dictOptions: any[], value: string) => {
  const option = dictOptions.find(item => item.value === value);
  return option ? option.label : value;
};



// 页面加载时获取数据
onMounted(() => {
  const novelId = route.params.id as string;
  console.log('详情页面加载，小说ID:', novelId);
  console.log('当前路由参数:', route.params);
  console.log('当前路由:', route);

  if (novelId) {
    loadNovelInfo(novelId);
    loadChapterList(novelId);
  } else {
    console.error('未获取到小说ID');
  }
});

// 加载小说信息
const loadNovelInfo = async (id: string) => {
  try {
    loading.value = true;
    console.log('开始加载小说信息，ID:', id);
    const response = await NovalForYhInfo(id);
    console.log('小说信息加载成功:', response);
    novelInfo.value = response;
  } catch (error) {
    console.error('加载小说信息失败:', error);
  } finally {
    loading.value = false;
  }
};

// 加载章节列表
const loadChapterList = async (id?: string) => {
  try {
    chapterLoading.value = true;
    const novelId = id || route.params.id as string;

    console.log('开始加载章节列表，小说ID:', novelId);

    // 尝试使用 novalDirectoryInfo 获取章节数据
    try {
      console.log('尝试使用 novalDirectoryInfo API');
      const directResponse = await novalDirectoryInfo(novelId);
      console.log('novalDirectoryInfo API响应:', directResponse);

      if (directResponse && Array.isArray(directResponse)) {
        // 如果有搜索条件，进行客户端过滤
        let filteredData = directResponse;
        if (searchForm.chaptername) {
          filteredData = directResponse.filter(item =>
            item.chaptername.includes(searchForm.chaptername)
          );
        }

        // 客户端分页
        const startIndex = (pagination.current - 1) * pagination.pageSize;
        const endIndex = startIndex + pagination.pageSize;
        chapterList.value = filteredData.slice(startIndex, endIndex);
        pagination.total = filteredData.length;

        console.log('使用 novalDirectoryInfo 成功，章节数据:', chapterList.value);
        return;
      }
    } catch (directError) {
      console.log('novalDirectoryInfo 失败，尝试 novalDirectoryList:', directError);
    }

    // 如果 novalDirectoryInfo 失败，使用 novalDirectoryList
    const params = {
      id: novelId,
      ...searchForm,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };

    console.log('使用 novalDirectoryList，参数:', params);
    const response = await novalDirectoryList(params);
    console.log('novalDirectoryList API响应:', response);

    chapterList.value = response.rows || [];
    pagination.total = response.total || 0;

    console.log('最终章节列表数据:', chapterList.value);
    console.log('总数:', pagination.total);
  } catch (error) {
    console.error('加载章节列表失败:', error);
    chapterList.value = [];
    pagination.total = 0;
  } finally {
    chapterLoading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  loadChapterList();
};

// 重置处理
const handleReset = () => {
  searchForm.chaptername = '';
  pagination.current = 1;
  loadChapterList();
};

// 分页处理
const handlePageChange = (page: number, pageSize?: number) => {
  pagination.current = page;
  if (pageSize) {
    pagination.pageSize = pageSize;
  }
  loadChapterList();
};

// 分页大小变化处理
const handlePageSizeChange = (_current: number, size: number) => {
  pagination.current = 1; // 重置到第一页
  pagination.pageSize = size;
  loadChapterList();
};



// 返回列表
const handleBack = () => {
  router.push('/noval/NovalForYh');
};

// 修复图片链接（处理重复的https://问题）
const fixImageUrl = (url: string) => {
  if (!url) return 'https://via.placeholder.com/300x400/f0f0f0/999999?text=暂无封面';

  // 修复重复的 https:// 问题
  if (url.startsWith('https://https://')) {
    return url.replace('https://https://', 'https://');
  }

  // 修复重复的 http:// 问题
  if (url.startsWith('http://https://')) {
    return url.replace('http://https://', 'https://');
  }

  if (url.startsWith('http://http://')) {
    return url.replace('http://http://', 'http://');
  }

  return url;
};

// 格式化排名
const formatRanking = (ranking: number) => {
  if (!ranking) return '暂无排名';
  return `排名 ${ranking}`;
};


</script>

<template>
  <div class="novel-detail-page">
    <!-- 返回按钮和外链提示 -->
    <div class="back-section">
      <button @click="handleBack" class="back-btn">
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        返回书库
      </button>

      <!-- 外链提示 -->
      <div class="external-link-notice-inline">
        <div class="notice-content-inline">
          <div class="notice-icon-inline">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 13V19C18 20.1046 17.1046 21 16 21H5C3.89543 21 3 20.1046 3 19V5C3 3.89543 3.89543 3 5 3H11M15 3H21V9M10 14L21 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <div class="notice-text-inline">
            <span class="notice-title-inline">外链提示</span>
            <span class="notice-desc-inline">点击章节将跳转到外部网站进行阅读</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 小说信息区域 -->
    <div class="novel-info-section">
      <div v-if="loading" class="loading-container">
        <a-spin size="large" />
      </div>
      <div v-else-if="novelInfo" class="novel-info-content">
        <!-- 左侧：封面图片 (1/3) -->
        <div class="novel-cover">
          <img
            :src="fixImageUrl(novelInfo.icon)"
            :alt="novelInfo.name"
            class="cover-image"
          />
        </div>

        <!-- 右侧：小说信息 (2/3) -->
        <div class="novel-details">
          <h1 class="novel-title">{{ novelInfo.name }}</h1>
          <div class="novel-meta">
            <div class="meta-item">
              <span class="meta-label">作者：</span>
              <span class="meta-value author">{{ novelInfo.author }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">状态：</span>
              <span class="meta-value status">{{ getDictLabel(stateOptions, novelInfo.flag) }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">平台：</span>
              <span class="meta-value platform">{{ getDictLabel(platformOptions, novelInfo.platform) }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">类型：</span>
              <span class="meta-value category">{{ getDictLabel(typeOptions, novelInfo.type) }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">排名：</span>
              <span class="meta-value ranking">{{ formatRanking(novelInfo.grades) }}</span>
            </div>
          </div>
          <div class="novel-summary">
            <h3 class="summary-title">简介</h3>
            <p class="summary-content">{{ novelInfo.summary || '暂无简介' }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 章节列表区域 -->
    <div class="chapter-section">
      <!-- 搜索表单 -->
      <div class="chapter-search">
        <div class="search-form">
          <div class="search-inputs">
            <div class="input-group">
              <label class="input-label">章节搜索</label>
              <input
                v-model="searchForm.chaptername"
                placeholder="请输入章节名称"
                class="search-input"
                @keyup.enter="handleSearch"
              />
            </div>
          </div>
          <div class="search-buttons">
            <button
              type="button"
              @click="handleSearch"
              :disabled="chapterLoading"
              class="search-btn primary"
            >
              {{ chapterLoading ? '搜索中...' : '搜索' }}
            </button>
            <button
              type="button"
              @click="handleReset"
              class="search-btn secondary"
            >
              重置
            </button>
          </div>
        </div>
      </div>

      <!-- 章节列表 -->
      <div class="chapter-list-container">
        <div v-if="chapterLoading" class="loading-container">
          <a-spin size="large" />
        </div>
        <div v-else class="chapter-list">
          <!-- 章节列表 -->
          <div class="chapter-list-simple" style="margin-top: 20px;">
            <div v-for="chapter in chapterList" :key="chapter.id + '_' + chapter.chapter" class="chapter-item-simple">
              <div class="chapter-info">
                <span class="chapter-number-simple">第{{ chapter.chapter }}章</span>
                <span class="chapter-title-simple">{{ chapter.chaptername }}</span>
              </div>
              <a
                :href="chapter.link"
                target="_blank"
                class="chapter-link-simple"
                @click.stop
              >
                <button class="read-btn">阅读</button>
              </a>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="!chapterList.length && !chapterLoading" class="empty-state">
            <a-empty description="暂无章节数据" />
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.total > 0" class="pagination-section">
        <a-pagination
          v-model:current="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :show-total="(total: number, range: number[]) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
          @change="handlePageChange"
          @show-size-change="handlePageSizeChange"
        />
      </div>
    </div>



  </div>
</template>

<style scoped>
/* 页面容器 - P5X风格 */
.novel-detail-page {
  padding: 24px;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

.novel-detail-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(147, 51, 234, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(6, 182, 212, 0.04) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

.novel-detail-page::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 48%, rgba(59, 130, 246, 0.02) 49%, rgba(59, 130, 246, 0.02) 51%, transparent 52%),
    linear-gradient(-45deg, transparent 48%, rgba(147, 51, 234, 0.02) 49%, rgba(147, 51, 234, 0.02) 51%, transparent 52%);
  background-size: 60px 60px;
  pointer-events: none;
  z-index: 0;
}

/* 返回按钮区域 */
.back-section {
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.9) 100%);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  color: #e2e8f0;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
}

.back-btn svg {
  width: 20px;
  height: 20px;
}

.back-btn:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(30, 41, 59, 0.9) 100%);
  border-color: #60a5fa;
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
}

/* 小说信息区域 */
.novel-info-section {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow:
    0 12px 24px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  position: relative;
  z-index: 1;
}

.novel-info-content {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

/* 封面区域 (1/3) */
.novel-cover {
  flex: 0 0 200px;
  width: 200px;
  height: auto;
}

.cover-image {
  width: 100%;
  max-height: 280px;
  object-fit: contain;
  border-radius: 8px;
  border: 2px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  background: rgba(15, 23, 42, 0.5);
}

.cover-image:hover {
  transform: scale(1.02);
  border-color: rgba(139, 92, 246, 0.5);
  box-shadow: 0 12px 48px rgba(59, 130, 246, 0.2);
}

/* 小说详情区域 (2/3) */
.novel-details {
  flex: 1;
  min-width: 0;
}

.novel-title {
  font-size: 24px;
  font-weight: 700;
  color: #60a5fa;
  margin: 0 0 16px 0;
  line-height: 1.3;
  text-shadow: 0 2px 8px rgba(96, 165, 250, 0.5);
  letter-spacing: 0.5px;
}

.novel-meta {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 20px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.meta-label {
  font-size: 12px;
  color: #94a3b8;
  font-weight: 500;
  min-width: 50px;
}

.meta-value {
  font-size: 12px;
  font-weight: 600;
}

.meta-value.author {
  color: #a78bfa;
  text-shadow: 0 1px 4px rgba(167, 139, 250, 0.5);
}

.meta-value.status {
  color: #fbbf24;
}

.meta-value.platform {
  color: #34d399;
}

.meta-value.category {
  color: #f472b6;
}

.meta-value.ranking {
  color: #06b6d4;
  text-shadow: 0 1px 4px rgba(6, 182, 212, 0.5);
}

.novel-summary {
  background: rgba(15, 23, 42, 0.5);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid rgba(147, 51, 234, 0.3);
}

.summary-title {
  font-size: 14px;
  font-weight: 600;
  color: #e2e8f0;
  margin: 0 0 12px 0;
}

.summary-content {
  font-size: 12px;
  color: #cbd5e1;
  line-height: 1.6;
  margin: 0;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #60a5fa;
}

/* 章节区域 */
.chapter-section {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(147, 51, 234, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(147, 51, 234, 0.3);
  position: relative;
  z-index: 1;
  min-height: 500px;
  display: flex;
  flex-direction: column;
}

/* 章节搜索区域 */
.chapter-search {
  margin-bottom: 16px;
  padding: 16px;
  background: rgba(15, 23, 42, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.search-form {
  display: flex;
  align-items: flex-end;
  gap: 24px;
  flex-wrap: wrap;
}

.search-inputs {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  flex: 1;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
}

.input-label {
  font-size: 14px;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 4px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 0.5px;
  position: relative;
}

.input-label::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transition: width 0.3s ease;
}

.input-group:focus-within .input-label::after {
  width: 100%;
}

.search-input {
  width: 280px;
  height: 48px;
  padding: 12px 20px;
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  font-size: 14px;
  color: #f1f5f9;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.search-input:focus {
  border-color: #3b82f6;
  box-shadow:
    0 0 0 3px rgba(59, 130, 246, 0.2),
    0 8px 32px rgba(59, 130, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  background: rgba(15, 23, 42, 0.95);
  transform: translateY(-2px);
}

.search-input::placeholder {
  color: #64748b;
  transition: color 0.3s ease;
}

.search-input:focus::placeholder {
  color: #94a3b8;
}

.search-buttons {
  display: flex;
  gap: 12px;
}

.search-btn {
  height: 48px;
  padding: 12px 32px;
  border: none;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.search-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.search-btn:hover::before {
  left: 100%;
}

.search-btn.primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  border: 2px solid rgba(59, 130, 246, 0.5);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.search-btn.primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #1d4ed8 0%, #3b82f6 100%);
  transform: translateY(-3px);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.4);
  border-color: #60a5fa;
}

.search-btn.primary:disabled {
  background: rgba(71, 85, 105, 0.5);
  color: #94a3b8;
  cursor: not-allowed;
  border-color: rgba(71, 85, 105, 0.3);
  box-shadow: none;
}

.search-btn.secondary {
  background: rgba(15, 23, 42, 0.8);
  color: #e2e8f0;
  border: 2px solid rgba(147, 51, 234, 0.5);
  box-shadow: 0 4px 16px rgba(147, 51, 234, 0.2);
}

.search-btn.secondary:hover {
  background: rgba(147, 51, 234, 0.2);
  border-color: #a855f7;
  transform: translateY(-3px);
  box-shadow: 0 8px 32px rgba(147, 51, 234, 0.3);
}

/* 章节列表容器 */
.chapter-list-container {
  margin-bottom: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.chapter-list {
  flex: 1;
  display: flex;
  flex-direction: column;
}



/* 简单章节列表 */
.chapter-list-simple {
  margin-bottom: 16px;
}

.chapter-item-simple {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  margin-bottom: 12px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.8) 100%);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.chapter-item-simple:hover {
  border-color: rgba(139, 92, 246, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.2);
}

.chapter-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.chapter-number-simple {
  flex-shrink: 0;
  width: 80px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  font-size: 12px;
  font-weight: 600;
  border-radius: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.chapter-title-simple {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #e2e8f0;
  line-height: 1.5;
}

.chapter-link-simple {
  text-decoration: none;
}

.read-btn {
  padding: 8px 20px;
  background: linear-gradient(135deg, #a78bfa 0%, #8b5cf6 100%);
  color: #ffffff;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(167, 139, 250, 0.3);
}

.read-btn:hover {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(167, 139, 250, 0.4);
}

/* 自定义表格样式 */
.chapter-table :deep(.ant-table) {
  background: transparent;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.chapter-table :deep(.ant-table-thead > tr > th) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(147, 51, 234, 0.2) 100%);
  border-bottom: 2px solid rgba(59, 130, 246, 0.4);
  color: #e2e8f0;
  font-weight: 600;
  font-size: 14px;
  padding: 16px 12px;
  text-align: center;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.chapter-table :deep(.ant-table-tbody > tr > td) {
  background: rgba(15, 23, 42, 0.6);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  color: #cbd5e1;
  padding: 16px 12px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.chapter-table :deep(.ant-table-tbody > tr:hover > td) {
  background: rgba(59, 130, 246, 0.1);
  color: #e2e8f0;
}

.chapter-table :deep(.ant-table-tbody > tr:nth-child(even) > td) {
  background: rgba(15, 23, 42, 0.4);
}

.chapter-table :deep(.ant-table-tbody > tr:nth-child(even):hover > td) {
  background: rgba(59, 130, 246, 0.15);
}

/* 章节序号样式 */
.chapter-number {
  font-weight: 600;
  color: #60a5fa;
  text-shadow: 0 1px 4px rgba(96, 165, 250, 0.5);
  letter-spacing: 0.5px;
}

/* 章节标题样式 */
.chapter-title {
  color: #e2e8f0;
  font-weight: 500;
  line-height: 1.5;
}

/* 章节链接样式 */
.chapter-link {
  text-decoration: none;
}

.chapter-link :deep(.ant-btn-link) {
  color: #a78bfa;
  font-weight: 600;
  border: 1px solid rgba(167, 139, 250, 0.3);
  border-radius: 8px;
  padding: 4px 16px;
  background: rgba(167, 139, 250, 0.1);
  transition: all 0.3s ease;
}

.chapter-link :deep(.ant-btn-link:hover) {
  color: #ffffff;
  background: rgba(167, 139, 250, 0.3);
  border-color: #a78bfa;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(167, 139, 250, 0.3);
}

.chapter-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.8) 100%);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.chapter-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(59, 130, 246, 0.05) 50%, transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chapter-item:hover {
  border-color: rgba(139, 92, 246, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.2);
}

.chapter-item:hover::before {
  opacity: 1;
}



.chapter-name {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #e2e8f0;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chapter-link-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  color: #60a5fa;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.chapter-item:hover .chapter-link-icon {
  opacity: 1;
  transform: scale(1.1);
  color: #a78bfa;
}

.chapter-link-icon svg {
  width: 100%;
  height: 100%;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 0;
  color: #94a3b8;
}

/* 分页区域 */
.pagination-section {
  display: flex;
  justify-content: center;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
  backdrop-filter: blur(20px);
  padding: 16px;
  border-radius: 12px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(6, 182, 212, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(6, 182, 212, 0.3);
  position: relative;
  z-index: 1;
  margin-bottom: 16px;
}



/* 外链提示区域 */
.external-link-notice {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.9) 100%);
  backdrop-filter: blur(15px);
  border-radius: 12px;
  padding: 16px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(251, 191, 36, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(251, 191, 36, 0.3);
  position: relative;
  overflow: hidden;
  margin-bottom: 16px;
}

.external-link-notice::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(251, 191, 36, 0.03) 50%, transparent 70%);
  pointer-events: none;
}

.notice-content {
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  z-index: 1;
}

.notice-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.2) 0%, rgba(245, 158, 11, 0.2) 100%);
  border-radius: 12px;
  border: 1px solid rgba(251, 191, 36, 0.4);
  color: #fbbf24;
}

.notice-icon svg {
  width: 24px;
  height: 24px;
}

.notice-text {
  flex: 1;
}

.notice-title {
  font-size: 16px;
  font-weight: 600;
  color: #fbbf24;
  margin: 0 0 4px 0;
  text-shadow: 0 1px 4px rgba(251, 191, 36, 0.5);
  letter-spacing: 0.5px;
}

.notice-desc {
  font-size: 14px;
  color: #cbd5e1;
  margin: 0;
  line-height: 1.5;
  font-weight: 400;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .novel-info-content {
    flex-direction: column;
    gap: 24px;
  }

  .novel-cover {
    flex: none;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
  }

  .novel-meta {
    grid-template-columns: 1fr;
  }
}

/* 内联外链提示 */
.external-link-notice-inline {
  flex: 1;
  max-width: 400px;
}

.notice-content-inline {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(245, 158, 11, 0.1) 100%);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(251, 191, 36, 0.3);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(251, 191, 36, 0.1);
}

.notice-icon-inline {
  flex-shrink: 0;
  width: 18px;
  height: 18px;
  color: #fbbf24;
}

.notice-icon-inline svg {
  width: 100%;
  height: 100%;
}

.notice-text-inline {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.notice-title-inline {
  font-size: 13px;
  font-weight: 600;
  color: #fbbf24;
  text-shadow: 0 2px 4px rgba(251, 191, 36, 0.3);
}

.notice-desc-inline {
  font-size: 11px;
  color: #cbd5e1;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .back-section {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .external-link-notice-inline {
    max-width: none;
  }

  .notice-content-inline {
    justify-content: center;
  }
}



/* 阅读提示 */
.reading-notice {
  margin-top: 24px;
  position: relative;
  z-index: 1;
}

.reading-notice .notice-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 24px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.1);
}

.reading-notice .notice-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  color: #60a5fa;
}

.reading-notice .notice-icon svg {
  width: 100%;
  height: 100%;
}

.reading-notice .notice-text {
  flex: 1;
}

.reading-notice .notice-title {
  font-size: 16px;
  font-weight: 600;
  color: #60a5fa;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(96, 165, 250, 0.3);
}

.reading-notice .notice-desc {
  font-size: 14px;
  color: #cbd5e1;
  margin: 0;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .novel-detail-page {
    padding: 16px;
  }

  .novel-info-section,
  .chapter-section {
    padding: 20px;
  }

  .novel-title {
    font-size: 24px;
  }

  .search-form {
    flex-direction: column;
    align-items: stretch;
  }

  .search-inputs {
    flex-direction: column;
    width: 100%;
  }

  .search-input {
    width: 100%;
  }

  .search-buttons {
    justify-content: center;
    margin-top: 16px;
  }

  .chapter-item {
    padding: 12px 16px;
  }

  .chapter-number {
    width: 50px;
    height: 28px;
    font-size: 11px;
  }

  .chapter-name {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .novel-detail-page {
    padding: 12px;
  }

  .novel-info-section,
  .chapter-section,
  .chapter-search {
    padding: 16px;
  }

  .novel-title {
    font-size: 20px;
  }

  .cover-image {
    height: 300px;
  }

  .search-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .search-btn {
    width: 100%;
  }

  .notice-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .notice-icon {
    width: 40px;
    height: 40px;
  }

  .notice-icon svg {
    width: 20px;
    height: 20px;
  }

  .notice-title {
    font-size: 14px;
  }

  .notice-desc {
    font-size: 13px;
  }
}
</style>
