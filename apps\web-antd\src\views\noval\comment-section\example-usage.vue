<script setup lang="ts">
import { ref } from 'vue';

import { Card, Tabs, TabPane, Divider } from 'ant-design-vue';

import CommentSection from './index.vue';
import CompactCommentSection from './compact.vue';

// 模拟小说信息
const novelInfo = ref({
  id: 'novel-123',
  title: '示例小说标题',
  author: '作者名',
  description: '这是一个示例小说的描述...',
});

// 处理查看更多评论
function handleViewMoreComments() {
  console.log('跳转到完整评论页面');
  // 这里可以跳转到完整的评论页面
}

// 处理评论添加成功
function handleCommentAdded(comment: any) {
  console.log('新评论已添加:', comment);
  // 可以在这里处理评论添加成功后的逻辑
}
</script>

<template>
  <div class="p-4 max-w-4xl mx-auto">
    <h1 class="text-2xl font-bold mb-4">评论区组件使用示例</h1>
    
    <!-- 模拟小说信息卡片 -->
    <Card class="mb-6">
      <h2 class="text-xl font-semibold mb-2">{{ novelInfo.title }}</h2>
      <p class="text-gray-600 mb-2">作者：{{ novelInfo.author }}</p>
      <p class="text-gray-700">{{ novelInfo.description }}</p>
    </Card>

    <Tabs default-active-key="1">
      <!-- 完整评论区 -->
      <TabPane key="1" tab="完整评论区">
        <Card title="完整功能评论区">
          <template #extra>
            <span class="text-sm text-gray-500">支持回复、完整功能</span>
          </template>
          <CommentSection 
            :target-id="novelInfo.id"
            :comment-type="1"
            :readonly="false"
          />
        </Card>
      </TabPane>

      <!-- 紧凑评论区 -->
      <TabPane key="2" tab="紧凑评论区">
        <Card title="紧凑版评论区">
          <template #extra>
            <span class="text-sm text-gray-500">适合嵌入其他页面</span>
          </template>
          <CompactCommentSection 
            :target-id="novelInfo.id"
            :comment-type="1"
            :max-comments="3"
            :show-comment-box="true"
            :show-more-link="true"
            @view-more="handleViewMoreComments"
            @comment-added="handleCommentAdded"
          />
        </Card>
      </TabPane>

      <!-- 只读模式 -->
      <TabPane key="3" tab="只读模式">
        <Card title="只读评论区">
          <template #extra>
            <span class="text-sm text-gray-500">只展示，不能发表评论</span>
          </template>
          <CommentSection 
            :target-id="novelInfo.id"
            :comment-type="1"
            :readonly="true"
          />
        </Card>
      </TabPane>

      <!-- 嵌入式使用 -->
      <TabPane key="4" tab="嵌入式使用">
        <div class="space-y-4">
          <!-- 主要内容区域 -->
          <Card title="小说章节内容">
            <div class="prose max-w-none">
              <p>这里是小说章节的正文内容...</p>
              <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit...</p>
              <p>更多内容...</p>
            </div>
          </Card>

          <Divider>评论区</Divider>

          <!-- 嵌入的紧凑评论区 -->
          <CompactCommentSection 
            :target-id="`${novelInfo.id}-chapter-1`"
            :comment-type="1"
            :max-comments="5"
            :show-comment-box="true"
            :show-more-link="true"
            @view-more="handleViewMoreComments"
            @comment-added="handleCommentAdded"
          />
        </div>
      </TabPane>

      <!-- 无评论框模式 -->
      <TabPane key="5" tab="无评论框模式">
        <Card title="只显示评论列表">
          <template #extra>
            <span class="text-sm text-gray-500">不显示发表评论框</span>
          </template>
          <CompactCommentSection 
            :target-id="novelInfo.id"
            :comment-type="1"
            :max-comments="5"
            :show-comment-box="false"
            :show-more-link="true"
            @view-more="handleViewMoreComments"
          />
        </Card>
      </TabPane>
    </Tabs>

    <!-- 使用说明 -->
    <Card class="mt-6" title="使用说明">
      <div class="space-y-4 text-sm">
        <div>
          <h4 class="font-semibold mb-2">完整评论区 (CommentSection)</h4>
          <ul class="list-disc list-inside space-y-1 text-gray-600">
            <li>支持发表评论和回复功能</li>
            <li>显示用户头像和昵称</li>
            <li>支持嵌套回复结构</li>
            <li>适合作为独立的评论页面</li>
          </ul>
        </div>

        <div>
          <h4 class="font-semibold mb-2">紧凑评论区 (CompactCommentSection)</h4>
          <ul class="list-disc list-inside space-y-1 text-gray-600">
            <li>紧凑的设计，适合嵌入其他页面</li>
            <li>可限制显示的评论数量</li>
            <li>支持"查看更多"功能</li>
            <li>可选择是否显示发表评论框</li>
          </ul>
        </div>

        <div>
          <h4 class="font-semibold mb-2">主要属性</h4>
          <ul class="list-disc list-inside space-y-1 text-gray-600">
            <li><code>target-id</code>: 关联的主题ID</li>
            <li><code>comment-type</code>: 评论类型 (0: 楼主, 1: 评论)</li>
            <li><code>readonly</code>: 是否只读模式</li>
            <li><code>max-comments</code>: 最大显示评论数 (仅紧凑版)</li>
            <li><code>show-comment-box</code>: 是否显示发表评论框 (仅紧凑版)</li>
            <li><code>show-more-link</code>: 是否显示查看更多链接 (仅紧凑版)</li>
          </ul>
        </div>

        <div>
          <h4 class="font-semibold mb-2">事件</h4>
          <ul class="list-disc list-inside space-y-1 text-gray-600">
            <li><code>@view-more</code>: 点击查看更多时触发 (仅紧凑版)</li>
            <li><code>@comment-added</code>: 评论添加成功时触发 (仅紧凑版)</li>
          </ul>
        </div>
      </div>
    </Card>
  </div>
</template>

<style scoped>
.prose {
  line-height: 1.7;
}

code {
  background-color: #f1f5f9;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 0.875em;
}
</style>
