import type { PageQuery, BaseEntity } from '#/api/common';

export interface ManageVO {
  /**
   * 书本id
   */
  id: string | number;

  /**
   * 书名
   */
  name: string;

  /**
   * 状态
   */
  flag: string;

  /**
   * 作者
   */
  author: string;
}

export interface ManageForm extends BaseEntity {
  /**
   * 书本id
   */
  id?: string | number;

  /**
   * 书名
   */
  name?: string;

  /**
   * 状态
   */
  flag?: string;
  /**
   * 作者
   */
  author?: string;

}

export interface ManageQuery extends PageQuery {
  /**
   * 书本id
   */
  id?: string | number;

  /**
   * 书名
   */
  name?: string;

  /**
   * 状态
   */
  flag?: string;

  /**
   * 作者
   */
  author?: string;

  /**
    * 日期范围参数
    */
  params?: any;
}
