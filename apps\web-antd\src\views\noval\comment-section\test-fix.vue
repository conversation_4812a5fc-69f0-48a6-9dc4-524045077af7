<script setup lang="ts">
import { ref } from 'vue';

import type { CommentForm } from '#/api/noval/comment/model';

import { useUserStore } from '@vben/stores';

import { Button, Card, Input, message, Space } from 'ant-design-vue';

import { commentAdd } from '#/api/noval/comment';

const userStore = useUserStore();
const testContent = ref('这是一个测试帖子内容');
const submitting = ref(false);

// 测试发表帖子（不传递commerntId）
async function testCreatePost() {
  if (!userStore.userInfo?.userId) {
    message.error('请先登录');
    return;
  }

  submitting.value = true;
  try {
    const postData: CommentForm = {
      // 不传递 commerntId，让后端自动生成
      content: testContent.value,
      myId: userStore.userInfo.userId,
      commerntType: 0, // 0代表发帖主人（楼主）
      isResponse: 0, // 0代表不是回复
      responseId: 0, // 主帖没有回复对象
    };

    console.log('发送的数据:', postData);

    await commentAdd(postData);
    message.success('测试帖子发表成功！');
  } catch (error) {
    console.error('发表失败:', error);
    message.error('发表失败: ' + (error as any).message);
  } finally {
    submitting.value = false;
  }
}

// 测试发表回复（不传递commerntId）
async function testCreateReply() {
  if (!userStore.userInfo?.userId) {
    message.error('请先登录');
    return;
  }

  submitting.value = true;
  try {
    const replyData: CommentForm = {
      // 不传递 commerntId，让后端自动生成
      content: testContent.value,
      myId: userStore.userInfo.userId,
      commerntType: 1, // 1代表这是帖子的评论
      isResponse: 0, // 0代表不是回复
      responseId: 1, // 假设回复帖子ID为1
    };

    console.log('发送的数据:', replyData);

    await commentAdd(replyData);
    message.success('测试回复发表成功！');
  } catch (error) {
    console.error('发表失败:', error);
    message.error('发表失败: ' + (error as any).message);
  } finally {
    submitting.value = false;
  }
}

// 测试发表回复的回复（不传递commerntId）
async function testCreateSubReply() {
  if (!userStore.userInfo?.userId) {
    message.error('请先登录');
    return;
  }

  submitting.value = true;
  try {
    const subReplyData: CommentForm = {
      // 不传递 commerntId，让后端自动生成
      content: testContent.value,
      myId: userStore.userInfo.userId,
      commerntType: 1, // 1代表这是评论
      isResponse: 1, // 1代表是回复的回复
      responseId: 1, // 假设回复评论ID为1
    };

    console.log('发送的数据:', subReplyData);

    await commentAdd(subReplyData);
    message.success('测试回复的回复发表成功！');
  } catch (error) {
    console.error('发表失败:', error);
    message.error('发表失败: ' + (error as any).message);
  } finally {
    submitting.value = false;
  }
}
</script>

<template>
  <div class="p5x-test-page">
    <Card title="评论ID修复测试" class="mb-4">
      <div class="mb-4">
        <h3 class="text-lg font-semibold mb-2">修复说明</h3>
        <div class="text-sm text-gray-600 space-y-2">
          <p>
            <strong>问题:</strong> 后端期望 commerntId 为 Long 类型，但前端发送的是字符串类型的UUID
          </p>
          <p>
            <strong>解决方案:</strong> 不传递 commerntId 字段，让后端自动生成数字类型的ID
          </p>
          <p>
            <strong>正确的数据结构:</strong>
          </p>
          <ul class="list-disc list-inside ml-4">
            <li>commerntType=0: 发帖主人（楼主），有曝光数 look</li>
            <li>commerntType=1, isResponse=0: 帖子的评论</li>
            <li>commerntType=1, isResponse=1: 评论的回复</li>
          </ul>
        </div>
      </div>

      <div class="mb-4">
        <label class="block text-sm font-medium mb-2">测试内容:</label>
        <Input.TextArea
          v-model:value="testContent"
          :rows="3"
          placeholder="输入测试内容..."
          :maxlength="200"
          show-count
        />
      </div>

      <Space direction="vertical" class="w-full">
        <div class="flex items-center gap-4">
          <Button
            type="primary"
            :loading="submitting"
            @click="testCreatePost"
          >
            测试发表帖子 (commerntType=0)
          </Button>
          <span class="text-sm text-gray-500">
            发表新帖子，作为楼主，有曝光数
          </span>
        </div>

        <div class="flex items-center gap-4">
          <Button
            :loading="submitting"
            @click="testCreateReply"
          >
            测试发表评论 (commerntType=1, isResponse=0)
          </Button>
          <span class="text-sm text-gray-500">
            评论帖子
          </span>
        </div>

        <div class="flex items-center gap-4">
          <Button
            :loading="submitting"
            @click="testCreateSubReply"
          >
            测试回复评论 (commerntType=1, isResponse=1)
          </Button>
          <span class="text-sm text-gray-500">
            回复某个评论
          </span>
        </div>
      </Space>

      <div class="mt-4 p-3 bg-green-50 rounded">
        <h4 class="text-sm font-medium text-green-800 mb-2">测试步骤:</h4>
        <ol class="text-sm text-green-700 space-y-1 list-decimal list-inside">
          <li>确保已登录</li>
          <li>输入测试内容</li>
          <li>点击相应的测试按钮</li>
          <li>查看控制台输出的数据结构</li>
          <li>确认请求成功且没有类型错误</li>
        </ol>
      </div>

      <div class="mt-4 p-3 bg-blue-50 rounded">
        <h4 class="text-sm font-medium text-blue-800 mb-2">预期结果:</h4>
        <ul class="text-sm text-blue-700 space-y-1 list-disc list-inside">
          <li>不再出现 "Cannot deserialize value of type java.lang.Long" 错误</li>
          <li>后端能够正常接收并处理请求</li>
          <li>自动生成数字类型的 commerntId</li>
          <li>数据能够正常保存到数据库</li>
        </ul>
      </div>
    </Card>
  </div>
</template>

<style scoped>
/* P5X风格测试页面 */
.p5x-test-page {
  padding: 20px;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a0a0a 100%);
  min-height: 100vh;
  font-family: 'Arial', 'Microsoft YaHei', sans-serif;
  position: relative;
  overflow-x: hidden;
}

.p5x-test-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 0, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* 卡片样式 */
.p5x-test-page :deep(.ant-card) {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border: 2px solid #444444;
  border-radius: 0;
  transform: skewX(-1deg);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.p5x-test-page :deep(.ant-card::before) {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(220, 20, 60, 0.1), transparent);
  animation: shine 3s infinite;
}

.p5x-test-page :deep(.ant-card-head) {
  background: linear-gradient(135deg, #dc143c, #8b0000);
  border-bottom: 2px solid #ff0000;
  transform: skewX(1deg);
  margin: -1px -1px 0 -1px;
}

.p5x-test-page :deep(.ant-card-head-title) {
  color: white;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.p5x-test-page :deep(.ant-card-body) {
  transform: skewX(1deg);
  background: transparent;
  color: #ffffff;
  position: relative;
  z-index: 1;
}

/* 文本样式 */
.p5x-test-page h3 {
  color: #dc143c;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.p5x-test-page p,
.p5x-test-page span {
  color: #cccccc;
}

.p5x-test-page strong {
  color: #ffffff;
  font-weight: bold;
}

/* 列表样式 */
.list-disc {
  list-style-type: disc;
  color: #cccccc;
}

.list-decimal {
  list-style-type: decimal;
  color: #cccccc;
}

.p5x-test-page ul li {
  margin-bottom: 4px;
  color: #cccccc;
}

.p5x-test-page ol li {
  margin-bottom: 4px;
  color: #cccccc;
}

/* 标签样式 */
.p5x-test-page label {
  color: #ffffff;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 输入框样式 */
.p5x-test-page :deep(.ant-input),
.p5x-test-page :deep(.ant-input-affix-wrapper) {
  background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
  border: 2px solid #444444;
  border-radius: 0;
  color: #ffffff;
  transition: all 0.3s ease;
}

.p5x-test-page :deep(.ant-input:focus),
.p5x-test-page :deep(.ant-input-affix-wrapper:focus),
.p5x-test-page :deep(.ant-input-affix-wrapper-focused) {
  border-color: #dc143c;
  box-shadow: 0 0 10px rgba(220, 20, 60, 0.3);
}

.p5x-test-page :deep(.ant-input::placeholder) {
  color: #666666;
}

/* 按钮样式 */
.p5x-test-page :deep(.ant-btn) {
  border-radius: 0;
  transform: skewX(-5deg);
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #333333, #1a1a1a);
  border: 2px solid #666666;
  color: #ffffff;
}

.p5x-test-page :deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #dc143c, #8b0000);
  border: 2px solid #ff0000;
  color: white;
  box-shadow: 0 4px 15px rgba(220, 20, 60, 0.4);
}

.p5x-test-page :deep(.ant-btn:hover) {
  transform: skewX(-5deg) translateY(-2px);
}

.p5x-test-page :deep(.ant-btn-primary:hover) {
  background: linear-gradient(135deg, #ff0000, #dc143c);
  box-shadow: 0 6px 20px rgba(220, 20, 60, 0.6);
}

.p5x-test-page :deep(.ant-btn:not(.ant-btn-primary):hover) {
  background: linear-gradient(135deg, #555555, #333333);
  border-color: #888888;
}

/* 特殊背景区域 */
.p5x-test-page :deep(.bg-green-50) {
  background: linear-gradient(135deg, rgba(0, 100, 0, 0.2), rgba(0, 150, 0, 0.1)) !important;
  border: 1px solid rgba(0, 255, 0, 0.3);
  border-radius: 0;
}

.p5x-test-page :deep(.bg-blue-50) {
  background: linear-gradient(135deg, rgba(0, 0, 100, 0.2), rgba(0, 0, 150, 0.1)) !important;
  border: 1px solid rgba(0, 100, 255, 0.3);
  border-radius: 0;
}

.p5x-test-page :deep(.text-green-800),
.p5x-test-page :deep(.text-green-700) {
  color: #00ff00 !important;
}

.p5x-test-page :deep(.text-blue-800),
.p5x-test-page :deep(.text-blue-700) {
  color: #00aaff !important;
}

/* 代码块样式 */
.p5x-test-page code {
  background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
  color: #dc143c;
  padding: 2px 6px;
  border: 1px solid #444444;
  font-family: 'Courier New', monospace;
  font-weight: bold;
}

.p5x-test-page pre {
  background: linear-gradient(135deg, #1a1a1a, #0a0a0a);
  border: 2px solid #444444;
  border-left: 4px solid #dc143c;
  color: #cccccc;
  padding: 16px;
  overflow-x: auto;
}

.p5x-test-page pre code {
  background: transparent;
  border: none;
  color: #cccccc;
  padding: 0;
}

/* 动画 */
@keyframes shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #dc143c, #8b0000);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #ff0000, #dc143c);
}
</style>
