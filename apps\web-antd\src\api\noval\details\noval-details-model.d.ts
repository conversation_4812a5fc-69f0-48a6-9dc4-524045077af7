import type { PageQuery, BaseEntity } from '#/api/common';

export interface DetailsVO {
  /**
   * 书本id
   */
  id: string | number;

  /**
   * 成绩
   */
  grades: number;

  /**
   * 类型
   */
  type: string;

  /**
   * 平台
   */
  platform: string;

  /**
   * 简介
   */
  summary: string;

  /**
   * 图标
   */
  icon: string;

}

export interface DetailsForm extends BaseEntity {
  /**
   * 书本id
   */
  id?: string | number;

  /**
   * 成绩
   */
  grades?: number;

  /**
   * 类型
   */
  type?: string;

  /**
   * 平台
   */
  platform?: string;

  /**
   * 简介
   */
  summary?: string;

  /**
   * 图标
   */
  icon?: string;

}

export interface DetailsQuery extends PageQuery {
  /**
   * 书本id
   */
  id?: string | number;

  /**
   * 成绩
   */
  grades?: number;

  /**
   * 类型
   */
  type?: string;

  /**
   * 平台
   */
  platform?: string;

   /**
   * 简介
   */
  summary?: string;

  /**
   * 图标
   */
  icon?: string;

  /**
    * 日期范围参数
    */
  params?: any;
}
