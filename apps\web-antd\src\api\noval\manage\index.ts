import type { ManageVO, ManageForm, ManageQuery } from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询书库管理列表
* @param params
* @returns 书库管理列表
*/
export function manageList(params?: ManageQuery) {
  return requestClient.get<PageResult<ManageVO>>('/noval/manage/list', { params });
}

/**
 * 导出书库管理列表
 * @param params
 * @returns 书库管理列表
 */
export function manageExport(params?: ManageQuery) {
  return commonExport('/noval/manage/export', params ?? {});
}

/**
 * 查询书库管理详情
 * @param id id
 * @returns 书库管理详情
 */
export function manageInfo(id: ID) {
  return requestClient.get<ManageVO>(`/noval/manage/${id}`);
}

/**
 * 新增书库管理
 * @param data
 * @returns void
 */
export function manageAdd(data: ManageForm) {
  return requestClient.postWithMsg<void>('/noval/manage', data);
}

/**
 * 更新书库管理
 * @param data
 * @returns void
 */
export function manageUpdate(data: ManageForm) {
  return requestClient.putWithMsg<void>('/noval/manage', data);
}

/**
 * 删除书库管理
 * @param id id
 * @returns void
 */
export function manageRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/noval/manage/${id}`);
}

/**
 * 本周新增书籍
 * @returns 本周新增书籍列表
 */
export function manageWeekAdd() {
  return requestClient.get<ManageVO[]>('/noval/manage/weekAdd');
}
