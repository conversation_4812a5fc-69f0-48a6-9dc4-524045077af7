<template>
  <div class="modern-live2d-container" :class="{ 'is-mobile': isMobile }">
    <!-- Live2D Canvas -->
    <div ref="live2dContainer" class="live2d-canvas-container"></div>

    <!-- 提示框 -->
    <div ref="tipsContainer" class="live2d-tips" v-show="showTips">
      {{ currentTip }}
    </div>

    <!-- 工具栏 -->
    <div class="live2d-toolbar" v-show="showToolbar">
      <button @click="switchModel" title="切换模型">
        <i class="icon-refresh"></i>
      </button>
      <button @click="switchTexture" title="切换衣服">
        <i class="icon-user"></i>
      </button>
      <button @click="showRandomMessage" title="一言">
        <i class="icon-chat"></i>
      </button>
      <button @click="takeScreenshot" title="截图">
        <i class="icon-camera"></i>
      </button>
      <button @click="toggleVisibility" title="隐藏">
        <i class="icon-close"></i>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { Application } from 'pixi.js'
import { Live2DModel } from 'pixi-live2d-display'
import { LIVE2D_CONFIG, getRandomMessage, getTimeBasedMessage, getDeviceConfig } from './config'

// 启用 Live2D 的 Cubism 2 和 Cubism 4 支持
window.PIXI = require('pixi.js')

interface Props {
  modelPath?: string
  width?: number
  height?: number
  showToolbar?: boolean
  autoPlay?: boolean
  scale?: number
  position?: { x: number; y: number }
}

const props = withDefaults(defineProps<Props>(), {
  modelPath: LIVE2D_CONFIG.models[0].path,
  width: LIVE2D_CONFIG.width,
  height: LIVE2D_CONFIG.height,
  showToolbar: LIVE2D_CONFIG.showToolbar,
  autoPlay: LIVE2D_CONFIG.autoPlay,
  scale: LIVE2D_CONFIG.scale,
  position: () => LIVE2D_CONFIG.position
})

// 响应式数据
const live2dContainer = ref<HTMLElement>()
const tipsContainer = ref<HTMLElement>()
const showTips = ref(false)
const currentTip = ref('')
const isMobile = ref(false)

// PIXI 应用和模型
let app: Application | null = null
let model: Live2DModel | null = null
let currentModelIndex = 0
let currentTextureIndex = 0

// 可用的模型列表
const modelList = LIVE2D_CONFIG.models.map(model => model.path)

// 检测移动设备
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768

  // 根据设备调整配置
  const deviceConfig = getDeviceConfig()
  if (app && model) {
    model.scale.set(deviceConfig.scale)
    app.renderer.resize(deviceConfig.width, deviceConfig.height)
  }
}

// 初始化 PIXI 应用
const initPixiApp = async () => {
  if (!live2dContainer.value) return

  try {
    // 创建 PIXI 应用
    app = new Application({
      width: props.width,
      height: props.height,
      backgroundColor: 0x000000,
      backgroundAlpha: 0,
      antialias: true,
      resolution: window.devicePixelRatio || 1,
      autoDensity: true
    })

    // 将 canvas 添加到容器
    live2dContainer.value.appendChild(app.view as HTMLCanvasElement)

    // 加载 Live2D 模型
    await loadModel(props.modelPath)

  } catch (error) {
    console.error('Failed to initialize PIXI app:', error)
  }
}

// 加载 Live2D 模型
const loadModel = async (modelPath: string) => {
  if (!app) return

  try {
    // 移除旧模型
    if (model) {
      app.stage.removeChild(model)
      model.destroy()
    }

    // 加载新模型
    model = await Live2DModel.from(modelPath)

    if (model) {
      // 设置模型属性
      model.scale.set(props.scale)
      model.position.set(props.position.x, props.position.y)

      // 添加到舞台
      app.stage.addChild(model)

      // 设置交互
      setupModelInteraction()

      // 自动播放动画
      if (props.autoPlay && model.internalModel.motionManager) {
        model.motion('idle')
      }

      console.log('Live2D model loaded successfully')
      showMessage(getRandomMessage('system', 'modelLoaded'))
    }

  } catch (error) {
    console.error('Failed to load Live2D model:', error)
    showMessage(getRandomMessage('system', 'modelLoadFailed'))
  }
}

// 设置模型交互
const setupModelInteraction = () => {
  if (!model) return

  model.interactive = true
  model.buttonMode = true

  // 点击事件
  model.on('pointerdown', (event) => {
    const point = event.data.global

    // 检测点击区域
    if (model?.hitTest('head', point.x, point.y)) {
      showMessage(getRandomMessage('click', 'head'))
      model?.motion(LIVE2D_CONFIG.animations.tapHead)
    } else if (model?.hitTest('body', point.x, point.y)) {
      showMessage(getRandomMessage('click', 'body'))
      model?.motion(LIVE2D_CONFIG.animations.tapBody)
    } else {
      showRandomMessage()
    }
  })

  // 鼠标悬停
  model.on('pointerover', () => {
    showMessage('你在看我吗？')
  })
}

// 显示消息
const showMessage = (message: string, duration = 3000) => {
  currentTip.value = message
  showTips.value = true

  setTimeout(() => {
    showTips.value = false
  }, duration)
}

// 显示随机消息
const showRandomMessage = () => {
  const randomTip = getRandomMessage('random')
  showMessage(randomTip)
}

// 切换模型
const switchModel = async () => {
  if (modelList.length <= 1) {
    showMessage('暂时只有一个模型哦~')
    return
  }

  currentModelIndex = (currentModelIndex + 1) % modelList.length
  await loadModel(modelList[currentModelIndex])
  showMessage(getRandomMessage('system', 'modelSwitched'))
}

// 切换材质
const switchTexture = () => {
  if (!model || !model.internalModel.textures) {
    showMessage('当前模型不支持换装~')
    return
  }

  const textures = model.internalModel.textures
  if (textures.length <= 1) {
    showMessage('暂时没有其他衣服~')
    return
  }

  currentTextureIndex = (currentTextureIndex + 1) % textures.length
  // 这里需要根据具体的 Live2D 版本实现材质切换
  showMessage('换了新衣服！')
}

// 截图
const takeScreenshot = () => {
  if (!app) return

  try {
    const canvas = app.view as HTMLCanvasElement
    const link = document.createElement('a')
    link.download = 'live2d-screenshot.png'
    link.href = canvas.toDataURL()
    link.click()
    showMessage('截图保存成功！')
  } catch (error) {
    console.error('Screenshot failed:', error)
    showMessage('截图失败了~')
  }
}

// 切换显示/隐藏
const toggleVisibility = () => {
  if (live2dContainer.value) {
    const container = live2dContainer.value.parentElement
    if (container) {
      container.style.display = container.style.display === 'none' ? 'block' : 'none'
      if (container.style.display === 'none') {
        showMessage('我先隐藏一下~')
      }
    }
  }
}

// 窗口大小改变处理
const handleResize = () => {
  checkMobile()
  if (app) {
    // 可以在这里调整 canvas 大小
  }
}

// 组件挂载
onMounted(async () => {
  checkMobile()
  window.addEventListener('resize', handleResize)

  await nextTick()
  await initPixiApp()
})

// 组件卸载
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)

  if (model) {
    model.destroy()
  }

  if (app) {
    app.destroy(true)
  }
})

// 暴露方法给父组件
defineExpose({
  loadModel,
  showMessage,
  switchModel,
  switchTexture,
  takeScreenshot
})
</script>

<style scoped>
.modern-live2d-container {
  position: fixed;
  bottom: 0;
  right: 20px;
  z-index: 9999;
  pointer-events: none;
}

.live2d-canvas-container {
  position: relative;
  pointer-events: auto;
}

.live2d-tips {
  position: absolute;
  top: -80px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  white-space: nowrap;
  max-width: 200px;
  text-align: center;
  animation: fadeInOut 0.3s ease-in-out;
  pointer-events: none;
}

.live2d-toolbar {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  pointer-events: auto;
}

.live2d-toolbar button {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.live2d-toolbar button:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

/* 移动端适配 */
.is-mobile {
  right: 10px;
  bottom: 10px;
}

.is-mobile .live2d-canvas-container {
  transform: scale(0.8);
  transform-origin: bottom right;
}

.is-mobile .live2d-toolbar {
  top: 5px;
  right: 5px;
}

.is-mobile .live2d-toolbar button {
  width: 28px;
  height: 28px;
}

/* 图标样式 */
.icon-refresh::before { content: "🔄"; }
.icon-user::before { content: "👕"; }
.icon-chat::before { content: "💬"; }
.icon-camera::before { content: "📷"; }
.icon-close::before { content: "❌"; }

@keyframes fadeInOut {
  0% { opacity: 0; transform: translateX(-50%) translateY(10px); }
  100% { opacity: 1; transform: translateX(-50%) translateY(0); }
}
</style>
