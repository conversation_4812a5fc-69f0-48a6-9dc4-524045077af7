<script setup lang="ts">
import type { UploadFile } from 'ant-design-vue';

import { computed, getCurrentInstance, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { useUserStore } from '@vben/stores';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { live2dAdd, live2dInfo, live2dUpdate } from '#/api/noval/live2d';
import { requestClient } from '#/api/request';

import { modalSchema } from './data';

const emit = defineEmits<{ reload: [] }>();

const userStore = useUserStore();
const uploading = ref(false);
const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    // 默认占满两列
    formItemClass: 'col-span-2',
    // 默认label宽度 px
    labelWidth: 80,
    // 通用配置项 会影响到所有表单项
    componentProps: {
      class: 'w-full',
    }
  },
  schema: modalSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

const [BasicModal, modalApi] = useVbenModal({
  // 在这里更改宽度 - 增加宽度以容纳新的上传说明
  class: 'w-[800px]',
  fullscreenButton: false,
  // 点击遮罩是否关闭
  closeOnClickModal: false,
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    modalApi.modalLoading(true);

    const { id } = modalApi.getData() as { id?: number | string };
    isUpdate.value = !!id;

    if (isUpdate.value && id) {
      const record = await live2dInfo(id);
      await formApi.setValues(record);
    } else {
      // 新增时清空表单
      await formApi.resetForm();
    }

    modalApi.modalLoading(false);
  },
});

// 上传Live2D文件夹
async function uploadLive2DFolder(files: UploadFile[]) {
  if (!files || files.length === 0) return { mainModelPath: '', uploadedFiles: [] };

  const userId = userStore.userInfo?.userId;
  if (!userId) {
    throw new Error('用户未登录');
  }

  uploading.value = true;

  try {
    const uploadedFiles = [];
    let folderName = '';

    // 从第一个文件获取文件夹名称
    if (files[0]?.originFileObj) {
      const webkitRelativePath = (files[0].originFileObj as any).webkitRelativePath;
      if (webkitRelativePath) {
        folderName = webkitRelativePath.split('/')[0];
      }
    }

    console.log(`开始上传Live2D文件夹: ${folderName}, 共${files.length}个文件`);

    // 逐个上传文件，保持文件夹结构
    for (const file of files) {
      if (file.originFileObj) {
        const formData = new FormData();
        formData.append('file', file.originFileObj);

        // 获取文件的相对路径
        const webkitRelativePath = (file.originFileObj as any).webkitRelativePath;
        let relativePath = '';

        if (webkitRelativePath) {
          // 保持原始文件夹结构：用户id/live2d/文件夹名/子路径
          relativePath = webkitRelativePath;
          formData.append('path', `${userId}/live2d/${relativePath}`);
        } else {
          // 如果没有相对路径，直接放在用户文件夹下
          formData.append('path', `${userId}/live2d/${folderName || 'model'}/${file.name}`);
        }

        console.log(`上传文件: ${file.name} -> ${userId}/live2d/${relativePath || folderName + '/' + file.name}`);

        // 上传单个文件
        const response = await requestClient.post('/resource/oss/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });

        uploadedFiles.push({
          ...response.data || response,
          originalPath: webkitRelativePath || file.name,
          originalName: file.name  // ✅ 记录文件的原始名称
        });
      }
    }

    console.log('所有文件上传完成:', uploadedFiles);

    // 查找主模型文件
    const modelFile = uploadedFiles.find((file: any) =>
      file.fileName?.endsWith('.model3.json') ||
      file.fileName?.endsWith('.model.json') ||
      file.originalFilename?.endsWith('.model3.json') ||
      file.originalFilename?.endsWith('.model.json') ||
      file.originalPath?.endsWith('.model3.json') ||
      file.originalPath?.endsWith('.model.json')
    );

    const mainModelPath = modelFile
      ? (modelFile.url || modelFile.path || modelFile.fileName)
      : `${userId}/live2d/${folderName}/`;

    console.log('主模型文件路径:', mainModelPath);

    return {
      mainModelPath,
      uploadedFiles  // ✅ 返回所有上传的文件信息，包含原始文件名
    };
  } catch (error) {
    console.error('文件夹上传失败:', error);
    throw error;
  } finally {
    uploading.value = false;
  }
}

// 防止重复提交的标志
const isSubmitting = ref(false);

async function handleConfirm() {
  // 防止重复提交
  if (isSubmitting.value) {
    console.log('正在提交中，忽略重复请求');
    return;
  }

  try {
    isSubmitting.value = true;
    modalApi.modalLoading(true);
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    // getValues获取为一个readonly的对象 需要修改必须先深拷贝一次
    const data = cloneDeep(await formApi.getValues());

    // 如果有文件上传，处理文件上传并创建记录
    if (data.icon && Array.isArray(data.icon) && data.icon.length > 0) {
      console.log('检测到文件上传，准备创建Live2D记录');
      console.log('上传的文件URLs:', data.icon);

      // 从全局文件名映射中获取原始文件名数组
      console.log('🗂️ 当前文件名映射:', window.live2dFileNameMap);

      const originalNames = data.icon.map((fileUrl: string, index: number) => {
        // 优先从全局映射中获取真实的原始文件名
        if (window.live2dFileNameMap && window.live2dFileNameMap[fileUrl]) {
          const originalName = window.live2dFileNameMap[fileUrl];
          console.log(`📁 文件 ${index}: ${fileUrl} -> ${originalName} (来自映射)`);
          return originalName;
        }

        // 如果映射中没有，使用智能命名规则
        const urlParts = fileUrl.split('/');
        const fileName = urlParts[urlParts.length - 1];

        let defaultName = fileName;

        // 根据文件扩展名和内容推断真实文件名
        if (fileName.includes('.json')) {
          if (fileName.includes('model3') || index === 0) {
            defaultName = `${data.name}.model3.json`;
          } else if (fileName.includes('motion3')) {
            defaultName = `motion_${index}.motion3.json`;
          } else if (fileName.includes('exp3')) {
            defaultName = `expression_${index}.exp3.json`;
          } else if (fileName.includes('physics3')) {
            defaultName = `physics.physics3.json`;
          } else {
            defaultName = `config_${index}.json`;
          }
        } else if (fileName.includes('.moc3')) {
          defaultName = `${data.name}.moc3`;
        } else if (fileName.includes('.png')) {
          defaultName = `texture_${index}.png`;
        } else if (fileName.includes('.txt')) {
          defaultName = `readme.txt`;
        }

        console.log(`📁 文件 ${index}: ${fileUrl} -> ${defaultName} (智能推断)`);
        return defaultName;
      });

      // 准备提交数据，包含icon数组和originalName数组
      const submitData = {
        name: data.name,
        type: data.type,
        icon: data.icon,                    // ✅ 文件URL数组
        originalName: originalNames         // ✅ 原始文件名数组
      };

      console.log('提交数据:', submitData);
      console.log('原名数组:', originalNames);

      // 调用后端API，传递数组参数
      await live2dAdd(submitData);

      console.log(`成功创建Live2D记录，包含 ${data.icon.length} 个文件`);
      emit('reload');
      await handleCancel();
      return; // 直接返回，不执行后面的单个记录创建逻辑
    }

    // 移除文件字段，不提交到后端
    const { modelFolder, ...submitData } = data;

    // 新增时移除ID字段，让后台自动生成
    if (!isUpdate.value) {
      delete submitData.id;
    }

    await (isUpdate.value ? live2dUpdate(submitData) : live2dAdd(submitData));
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    isSubmitting.value = false;
    modalApi.modalLoading(false);
  }
}

async function handleCancel() {
  modalApi.close();
  await formApi.resetForm();
}
</script>

<template>
  <BasicModal :title="title" :loading="uploading">
    <BasicForm />

    <!-- 上传进度提示 -->
    <div v-if="uploading" class="upload-progress">
      <div class="progress-content">
        <div class="progress-icon">
          <svg class="animate-spin h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
        <div class="progress-text">
          <div class="progress-title">正在上传Live2D模型文件...</div>
          <div class="progress-subtitle">请稍候，正在保持文件夹结构上传所有文件</div>
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<style scoped>
.upload-progress {
  margin-top: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  border: 1px solid #b3d9ff;
  border-radius: 8px;
  text-align: center;
}

.progress-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.progress-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-text {
  text-align: left;
}

.progress-title {
  color: #1890ff;
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 4px;
}

.progress-subtitle {
  color: #8c8c8c;
  font-size: 12px;
  line-height: 1.4;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>

