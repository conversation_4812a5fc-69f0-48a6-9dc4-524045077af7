import { ossInfo, ossRemove, ossList } from '#/api/system/oss';
import type { OssFile } from '#/api/system/oss/model';

/**
 * 文件删除辅助工具
 * 用于根据文件URL查找并删除对应的OSS文件
 */

/**
 * 从URL中提取文件路径
 * @param url 完整的文件URL
 * @returns 文件路径
 */
function extractFilePathFromUrl(url: string): string {
  if (!url) return '';

  try {
    // 如果是完整URL，提取路径部分
    if (url.startsWith('http')) {
      const urlObj = new URL(url);
      return urlObj.pathname.substring(1); // 去掉开头的 /
    }

    // 如果已经是相对路径，直接返回
    return url;
  } catch (error) {
    console.error('解析文件URL失败:', url, error);
    return url;
  }
}

/**
 * 根据文件URL查找对应的OSS文件记录
 * @param fileUrls 文件URL数组
 * @returns OSS文件记录数组
 */
export async function findOssFilesByUrls(fileUrls: string[]): Promise<OssFile[]> {
  if (!fileUrls || fileUrls.length === 0) {
    return [];
  }

  try {
    // 过滤掉空值
    const validUrls = fileUrls.filter(url => url && url.trim());
    if (validUrls.length === 0) {
      return [];
    }

    // 尝试通过OSS API查找文件记录
    const ossFiles: OssFile[] = [];

    for (const url of validUrls) {
      try {
        // 从URL中提取文件名
        const fileName = url.split('/').pop();
        if (fileName) {
          // 查询OSS文件记录
          const result = await ossList({
            pageNum: 1,
            pageSize: 100,
            fileName: fileName,
          });

          if (result.rows && result.rows.length > 0) {
            // 找到匹配的文件，添加到删除列表
            const matchedFile = result.rows.find(file => file.url === url);
            if (matchedFile) {
              ossFiles.push(matchedFile);
            }
          }
        }
      } catch (error) {
        console.warn(`查找文件失败: ${url}`, error);
      }
    }

    console.log(`找到 ${ossFiles.length} 个OSS文件记录，准备删除`);
    return ossFiles;

  } catch (error) {
    console.error('查找OSS文件失败:', error);
    return [];
  }
}

/**
 * 删除OSS文件
 * @param ossFiles OSS文件记录数组
 * @returns 删除结果
 */
export async function deleteOssFiles(ossFiles: OssFile[]): Promise<boolean> {
  if (!ossFiles || ossFiles.length === 0) {
    return true;
  }

  try {
    const ossIds = ossFiles.map(file => file.ossId);
    await ossRemove(ossIds);
    console.log('成功删除OSS文件:', ossIds);
    return true;
  } catch (error) {
    console.error('删除OSS文件失败:', error);
    return false;
  }
}

/**
 * 根据文件URL删除对应的OSS文件
 * @param fileUrls 文件URL数组
 * @returns 删除结果
 */
export async function deleteFilesByUrls(fileUrls: string[]): Promise<boolean> {
  try {
    const ossFiles = await findOssFilesByUrls(fileUrls);
    return await deleteOssFiles(ossFiles);
  } catch (error) {
    console.error('根据URL删除文件失败:', error);
    return false;
  }
}

/**
 * 从对象中提取所有图片字段的URL
 * @param obj 包含图片字段的对象
 * @param iconFields 图片字段名数组
 * @returns 文件URL数组
 */
export function extractIconUrls(obj: any, iconFields: string[]): string[] {
  if (!obj || !iconFields) {
    return [];
  }

  const urls: string[] = [];

  iconFields.forEach(field => {
    const value = obj[field];
    if (value) {
      if (typeof value === 'string') {
        // 处理单个URL
        urls.push(value);
      } else if (Array.isArray(value)) {
        // 处理URL数组
        urls.push(...value.filter(url => url && typeof url === 'string'));
      } else if (typeof value === 'object') {
        // 处理对象格式（可能是上传组件返回的格式）
        if (value.url) {
          urls.push(value.url);
        }
      }
    }
  });

  return urls.filter(url => url && url.trim());
}

/**
 * Live2D专用：提取Live2D文件URLs
 * @param live2dRecord Live2D记录
 * @returns 文件URL数组
 */
export function extractLive2DUrls(live2dRecord: any): string[] {
  return extractIconUrls(live2dRecord, ['icon']);
}

/**
 * Live2D专用：根据名字删除所有同名记录及文件
 * @param name Live2D名字
 * @param live2dListByNameFn 根据名字查询记录的函数
 * @param live2dRemoveByNameFn 根据名字删除记录的函数
 * @returns 删除结果
 */
export async function deleteLive2DByName(
  name: string,
  live2dListByNameFn: (name: string) => Promise<any>,
  live2dRemoveByNameFn: (name: string) => Promise<any>
): Promise<boolean> {
  try {
    console.log(`🗑️ 开始删除Live2D "${name}"`);

    // 1. 查询所有同名记录，获取文件URLs
    const records = await live2dListByNameFn(name);
    const allFileUrls: string[] = [];

    records.forEach((record: any) => {
      const urls = extractLive2DUrls(record);
      allFileUrls.push(...urls);
    });

    console.log(`📋 找到 "${name}" 的记录数: ${records.length}`);
    console.log(`📁 找到关联文件数: ${allFileUrls.length}`);

    // 2. 删除数据库记录
    try {
      await live2dRemoveByNameFn(name);
      console.log(`✅ 成功删除数据库记录: "${name}"`);
    } catch (deleteError) {
      console.error(`❌ 删除数据库记录失败: "${name}"`, deleteError);
      throw deleteError;
    }

    // 3. 删除关联文件（异步执行，不影响主流程）
    if (allFileUrls.length > 0) {
      deleteFilesByUrls(allFileUrls).catch(error => {
        console.error('删除关联文件失败:', error);
      });
    }

    console.log(`✅ 成功删除Live2D "${name}"，共${records.length}条记录，${allFileUrls.length}个文件`);
    return true;
  } catch (error) {
    console.error(`❌ 删除Live2D失败: "${name}"`, error);
    throw error;
  }
}

/**
 * Comment专用：提取评论图片URLs
 * @param commentRecord 评论记录
 * @returns 文件URL数组
 */
export function extractCommentUrls(commentRecord: any): string[] {
  return extractIconUrls(commentRecord, ['icon1', 'icon2', 'icon3', 'icon4', 'icon5']);
}

/**
 * Details专用：提取详情图片URLs
 * @param detailsRecord 详情记录
 * @returns 文件URL数组
 */
export function extractDetailsUrls(detailsRecord: any): string[] {
  return extractIconUrls(detailsRecord, ['icon']);
}

/**
 * 通用删除函数：删除记录及其关联文件
 * @param record 要删除的记录
 * @param deleteRecordFn 删除记录的函数
 * @param extractUrlsFn 提取文件URLs的函数
 * @returns 删除结果
 */
export async function deleteRecordWithFiles<T>(
  record: T,
  deleteRecordFn: (id: any) => Promise<any>,
  extractUrlsFn: (record: T) => string[]
): Promise<boolean> {
  try {
    // 1. 提取文件URLs
    const fileUrls = extractUrlsFn(record);

    // 2. 删除数据库记录
    const recordId = (record as any).id || (record as any).commerntId;
    await deleteRecordFn(recordId);

    // 3. 删除关联文件（异步执行，不影响主流程）
    if (fileUrls.length > 0) {
      deleteFilesByUrls(fileUrls).catch(error => {
        console.error('删除关联文件失败:', error);
      });
    }

    return true;
  } catch (error) {
    console.error('删除记录失败:', error);
    throw error;
  }
}

/**
 * 批量删除函数：删除多个记录及其关联文件
 * @param records 要删除的记录数组
 * @param deleteRecordsFn 批量删除记录的函数
 * @param extractUrlsFn 提取文件URLs的函数
 * @returns 删除结果
 */
export async function deleteRecordsWithFiles<T>(
  records: T[],
  deleteRecordsFn: (ids: any[]) => Promise<any>,
  extractUrlsFn: (record: T) => string[]
): Promise<boolean> {
  try {
    // 1. 提取所有文件URLs
    const allFileUrls: string[] = [];
    records.forEach(record => {
      const urls = extractUrlsFn(record);
      allFileUrls.push(...urls);
    });

    // 2. 删除数据库记录
    const recordIds = records.map(record =>
      (record as any).id || (record as any).commerntId
    );
    await deleteRecordsFn(recordIds);

    // 3. 删除关联文件（异步执行，不影响主流程）
    if (allFileUrls.length > 0) {
      deleteFilesByUrls(allFileUrls).catch(error => {
        console.error('批量删除关联文件失败:', error);
      });
    }

    return true;
  } catch (error) {
    console.error('批量删除记录失败:', error);
    throw error;
  }
}
