// 共享的news数据存储
export let newsData = [
  {
    id: 1, // 消息发起人id (管理员)
    otherId: 1, // 消息接收者id (用户1)
    news: '欢迎使用我们的小说论坛！请遵守社区规则，文明发言。',
    type: 0, // 管理员信息
    createTime: '2024-01-15 10:30:00',
  },
  {
    id: 2, // 消息发起人id (用户2)
    otherId: 1, // 消息接收者id (用户1)
    news: '您的帖子《关于玄幻小说的讨论》收到了新的回复',
    type: 1, // 论坛信息，id对应CommentVO的commerntId
    createTime: '2024-01-15 14:20:00',
  },
  {
    id: 3, // 消息发起人id (用户3)
    otherId: 1, // 消息接收者id (用户1)
    news: '你好，我想和你讨论一下最近看的小说',
    type: 2, // 私聊信息
    createTime: '2024-01-15 16:45:00',
  },
  {
    id: 1, // 管理员
    otherId: 1, // 用户1
    news: '系统将于今晚22:00-24:00进行维护，期间可能影响正常使用。',
    type: 0, // 管理员信息
    createTime: '2024-01-16 09:00:00',
  },
  {
    id: 4, // 用户4
    otherId: 1, // 用户1
    news: '有人点赞了您的评论',
    type: 1, // 论坛信息
    createTime: '2024-01-16 11:30:00',
  },
  {
    id: 5, // 用户5
    otherId: 1, // 用户1
    news: '感谢您的推荐，那本小说真的很不错！',
    type: 2, // 私聊信息
    createTime: '2024-01-16 15:20:00',
  },
];

// 获取下一个ID
export function getNextNewsId(): number {
  return Math.max(...newsData.map(item => Number(item.id))) + 1;
}

// 添加消息
export function addNews(news: any): void {
  newsData.push(news);
}

// 更新消息
export function updateNews(id: number, updates: any): boolean {
  const index = newsData.findIndex(item => Number(item.id) === id);
  if (index === -1) {
    return false;
  }
  newsData[index] = { ...newsData[index], ...updates };
  return true;
}

// 删除消息
export function deleteNews(id: number): boolean {
  const index = newsData.findIndex(item => Number(item.id) === id);
  if (index === -1) {
    return false;
  }
  newsData.splice(index, 1);
  return true;
}

// 删除多个消息
export function deleteNewsMultiple(ids: number[]): number {
  let deletedCount = 0;
  ids.forEach(id => {
    if (deleteNews(id)) {
      deletedCount++;
    }
  });
  return deletedCount;
}

// 查找消息
export function findNews(id: number): any | undefined {
  return newsData.find(item => Number(item.id) === id);
}

// 根据接收者ID查找消息
export function findNewsByReceiver(otherId: number | string): any[] {
  return newsData.filter(item => Number(item.otherId) === Number(otherId));
}

// 根据发送者ID查找消息
export function findNewsBySender(id: number | string): any[] {
  return newsData.filter(item => Number(item.id) === Number(id));
}

// 根据类型查找消息
export function findNewsByType(type: number | string): any[] {
  return newsData.filter(item => Number(item.type) === Number(type));
}
