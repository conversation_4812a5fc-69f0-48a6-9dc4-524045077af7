<script setup lang="ts">
import { ref } from 'vue';

import { formatDateTime } from '@vben/utils';

import { Card, Button, message } from 'ant-design-vue';

// 测试时间格式化函数
const testTime = ref(new Date().toISOString());
const formattedTime = ref('');

function testFormatDateTime() {
  try {
    formattedTime.value = formatDateTime(testTime.value);
    message.success('时间格式化成功！');
  } catch (error) {
    console.error('时间格式化失败:', error);
    message.error('时间格式化失败');
  }
}

// 测试不同的时间格式
const timeFormats = [
  new Date().toISOString(),
  '2024-01-01 12:00:00',
  '2024-01-01T12:00:00.000Z',
  Date.now(),
];

function testAllFormats() {
  timeFormats.forEach((time, index) => {
    try {
      const formatted = formatDateTime(time);
      console.log(`格式 ${index + 1}: ${time} -> ${formatted}`);
    } catch (error) {
      console.error(`格式 ${index + 1} 失败:`, error);
    }
  });
  message.success('所有格式测试完成，请查看控制台');
}
</script>

<template>
  <div class="p-4">
    <Card title="时间格式化函数测试">
      <div class="space-y-4">
        <div>
          <h3 class="text-lg font-semibold mb-2">测试 formatDateTime 函数</h3>
          <p class="text-gray-600 mb-4">
            这个页面用于测试 formatDateTime 函数是否正常工作
          </p>
        </div>

        <div class="border p-4 rounded">
          <h4 class="font-medium mb-2">当前时间测试</h4>
          <p class="mb-2">
            <strong>原始时间:</strong> {{ testTime }}
          </p>
          <p class="mb-2">
            <strong>格式化后:</strong> {{ formattedTime || '点击按钮进行测试' }}
          </p>
          <Button type="primary" @click="testFormatDateTime">
            测试格式化
          </Button>
        </div>

        <div class="border p-4 rounded">
          <h4 class="font-medium mb-2">多种格式测试</h4>
          <p class="mb-2 text-gray-600">
            测试不同的时间格式输入，结果将在控制台显示
          </p>
          <ul class="list-disc list-inside mb-4 text-sm text-gray-600">
            <li>ISO 字符串格式</li>
            <li>标准日期时间字符串</li>
            <li>时间戳数字</li>
          </ul>
          <Button @click="testAllFormats">
            测试所有格式
          </Button>
        </div>

        <div class="border p-4 rounded bg-green-50">
          <h4 class="font-medium mb-2 text-green-800">修复说明</h4>
          <div class="text-sm text-green-700 space-y-2">
            <p>
              <strong>问题:</strong> 原来使用的 <code>formatToDateTime</code> 函数不存在
            </p>
            <p>
              <strong>解决:</strong> 改为使用正确的 <code>formatDateTime</code> 函数
            </p>
            <p>
              <strong>来源:</strong> <code>@vben/utils</code> 包中的 <code>formatDateTime</code> 函数
            </p>
            <p>
              <strong>格式:</strong> 输出格式为 'YYYY-MM-DD HH:mm:ss'
            </p>
          </div>
        </div>

        <div class="border p-4 rounded bg-blue-50">
          <h4 class="font-medium mb-2 text-blue-800">使用方法</h4>
          <div class="text-sm text-blue-700">
            <pre class="bg-blue-100 p-2 rounded mt-2 overflow-x-auto"><code>import { formatDateTime } from '@vben/utils';

// 使用示例
const formatted = formatDateTime(new Date());
// 输出: 2024-01-01 12:00:00</code></pre>
          </div>
        </div>
      </div>
    </Card>
  </div>
</template>

<style scoped>
code {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
